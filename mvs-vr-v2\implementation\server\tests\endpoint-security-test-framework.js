/**
 * Endpoint Security Test Framework
 * 
 * Comprehensive testing framework for endpoint information disclosure reduction
 */

const express = require('express');
const request = require('supertest');
const { createEndpointSecuritySuite, SECURITY_LEVELS } = require('../middleware/endpoint-security-suite');

const logger = require('../utils/logger').getLogger('endpoint-security-test');

// Test data with various sensitivity levels
const TEST_DATA = {
  public: {
    id: '123',
    name: 'Test User',
    status: 'active',
    created_at: '2024-01-01T00:00:00Z'
  },
  internal: {
    id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    username: 'testuser',
    profile: { bio: 'Test bio' }
  },
  confidential: {
    id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    phone: '******-123-4567',
    address: '123 Main St, City, State 12345',
    birth_date: '1990-01-01'
  },
  restricted: {
    id: '123',
    name: 'Test User',
    email: '<EMAIL>',
    ssn: '***********',
    credit_card: '4111-1111-1111-1111',
    bank_account: '*********'
  },
  top_secret: {
    id: '123',
    name: 'Test User',
    password: 'secret123',
    api_key: 'sk_test_*********0abcdef',
    private_key: '-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...',
    secret: 'top-secret-value'
  }
};

// Test users with different roles
const TEST_USERS = {
  guest: { id: 'guest', role: 'guest' },
  user: { id: 'user1', role: 'user' },
  premium_user: { id: 'premium1', role: 'premium_user' },
  admin: { id: 'admin1', role: 'admin' },
  super_admin: { id: 'super1', role: 'super_admin' }
};

/**
 * Create test app with security middleware
 * @param {string} securityLevel - Security level to test
 * @returns {Object} Express app
 */
function createTestApp(securityLevel = SECURITY_LEVELS.STANDARD) {
  const app = express();
  
  app.use(express.json());
  
  // Add authentication middleware (mock)
  app.use((req, res, next) => {
    const userRole = req.headers['x-test-user-role'] || 'guest';
    req.user = TEST_USERS[userRole] || TEST_USERS.guest;
    next();
  });
  
  // Apply security middleware
  const securityMiddlewares = createEndpointSecuritySuite({
    securityLevel,
    skipPaths: ['/test/health']
  });
  
  securityMiddlewares.forEach(middleware => {
    app.use(middleware);
  });
  
  // Test endpoints
  app.get('/test/public', (req, res) => {
    res.json(TEST_DATA.public);
  });
  
  app.get('/test/internal', (req, res) => {
    res.json(TEST_DATA.internal);
  });
  
  app.get('/test/confidential', (req, res) => {
    res.json(TEST_DATA.confidential);
  });
  
  app.get('/test/restricted', (req, res) => {
    res.json(TEST_DATA.restricted);
  });
  
  app.get('/test/top-secret', (req, res) => {
    res.json(TEST_DATA.top_secret);
  });
  
  app.get('/test/mixed', (req, res) => {
    res.json({
      ...TEST_DATA.public,
      ...TEST_DATA.internal,
      ...TEST_DATA.confidential,
      ...TEST_DATA.restricted,
      ...TEST_DATA.top_secret
    });
  });
  
  app.get('/test/health', (req, res) => {
    res.json({ status: 'ok' });
  });
  
  return app;
}

/**
 * Test suite for endpoint security
 */
class EndpointSecurityTestSuite {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      tests: []
    };
  }
  
  /**
   * Run all security tests
   * @returns {Promise<Object>} Test results
   */
  async runAllTests() {
    logger.info('Starting endpoint security test suite');
    
    // Test different security levels
    for (const level of Object.values(SECURITY_LEVELS)) {
      await this.testSecurityLevel(level);
    }
    
    // Test role-based access control
    await this.testRoleBasedAccess();
    
    // Test data sanitization
    await this.testDataSanitization();
    
    // Test audit logging
    await this.testAuditLogging();
    
    // Test bypass mechanisms
    await this.testBypassMechanisms();
    
    logger.info(`Test suite completed. Passed: ${this.results.passed}/${this.results.total}`);
    
    return this.results;
  }
  
  /**
   * Test specific security level
   * @param {string} level - Security level
   */
  async testSecurityLevel(level) {
    const app = createTestApp(level);
    
    await this.runTest(`Security Level: ${level}`, async () => {
      const response = await request(app)
        .get('/test/mixed')
        .set('x-test-user-role', 'user')
        .expect(200);
      
      const data = response.body;
      
      // Check that sensitive data is properly handled based on security level
      switch (level) {
        case SECURITY_LEVELS.MINIMAL:
          // Should have basic sanitization
          this.assert(!data.password, 'Password should be sanitized');
          break;
          
        case SECURITY_LEVELS.STANDARD:
          // Should have access control and sanitization
          this.assert(!data.password, 'Password should be sanitized');
          this.assert(!data.api_key, 'API key should be sanitized');
          break;
          
        case SECURITY_LEVELS.ENHANCED:
          // Should have enhanced masking
          this.assert(!data.password, 'Password should be sanitized');
          this.assert(!data.ssn || data.ssn.includes('*'), 'SSN should be masked');
          break;
          
        case SECURITY_LEVELS.MAXIMUM:
          // Should have maximum protection
          this.assert(!data.password, 'Password should be sanitized');
          this.assert(!data.private_key, 'Private key should be sanitized');
          this.assert(!data.ssn || data.ssn.includes('*'), 'SSN should be masked');
          break;
      }
    });
  }
  
  /**
   * Test role-based access control
   */
  async testRoleBasedAccess() {
    const app = createTestApp(SECURITY_LEVELS.ENHANCED);
    
    // Test guest access
    await this.runTest('Guest Access Control', async () => {
      const response = await request(app)
        .get('/test/confidential')
        .set('x-test-user-role', 'guest')
        .expect(200);
      
      const data = response.body;
      this.assert(!data.phone, 'Guest should not see phone number');
      this.assert(!data.address, 'Guest should not see address');
    });
    
    // Test user access
    await this.runTest('User Access Control', async () => {
      const response = await request(app)
        .get('/test/internal')
        .set('x-test-user-role', 'user')
        .expect(200);
      
      const data = response.body;
      this.assert(data.email, 'User should see email');
      this.assert(data.username, 'User should see username');
    });
    
    // Test admin access
    await this.runTest('Admin Access Control', async () => {
      const response = await request(app)
        .get('/test/restricted')
        .set('x-test-user-role', 'admin')
        .expect(200);
      
      const data = response.body;
      this.assert(data.ssn || data.ssn === '[REDACTED]', 'Admin should see SSN (possibly redacted)');
    });
  }
  
  /**
   * Test data sanitization
   */
  async testDataSanitization() {
    const app = createTestApp(SECURITY_LEVELS.MAXIMUM);
    
    await this.runTest('Password Sanitization', async () => {
      const response = await request(app)
        .get('/test/top-secret')
        .set('x-test-user-role', 'user')
        .expect(200);
      
      const data = response.body;
      this.assert(!data.password || data.password === '[REDACTED]', 'Password should be redacted');
    });
    
    await this.runTest('API Key Sanitization', async () => {
      const response = await request(app)
        .get('/test/top-secret')
        .set('x-test-user-role', 'user')
        .expect(200);
      
      const data = response.body;
      this.assert(!data.api_key || data.api_key === '[REDACTED]', 'API key should be redacted');
    });
    
    await this.runTest('Credit Card Masking', async () => {
      const response = await request(app)
        .get('/test/restricted')
        .set('x-test-user-role', 'premium_user')
        .expect(200);
      
      const data = response.body;
      if (data.credit_card) {
        this.assert(data.credit_card.includes('*'), 'Credit card should be masked');
      }
    });
  }
  
  /**
   * Test audit logging
   */
  async testAuditLogging() {
    const app = createTestApp(SECURITY_LEVELS.ENHANCED);
    
    await this.runTest('Audit Logging', async () => {
      // Make request that should be audited
      await request(app)
        .get('/test/confidential')
        .set('x-test-user-role', 'user')
        .expect(200);
      
      // Check that audit log was created (this would require access to audit logs)
      // For now, we just verify the request completed successfully
      this.assert(true, 'Audit logging test completed');
    });
  }
  
  /**
   * Test bypass mechanisms
   */
  async testBypassMechanisms() {
    const app = createTestApp(SECURITY_LEVELS.STANDARD);
    
    await this.runTest('Admin Bypass', async () => {
      const response = await request(app)
        .get('/test/top-secret')
        .set('x-test-user-role', 'admin')
        .expect(200);
      
      // Admin should have more access than regular users
      this.assert(response.body, 'Admin should receive response');
    });
  }
  
  /**
   * Run individual test
   * @param {string} name - Test name
   * @param {Function} testFn - Test function
   */
  async runTest(name, testFn) {
    this.results.total++;
    
    try {
      await testFn();
      this.results.passed++;
      this.results.tests.push({
        name,
        status: 'PASSED',
        timestamp: new Date().toISOString()
      });
      logger.info(`✓ ${name}`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({
        name,
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      logger.error(`✗ ${name}: ${error.message}`);
    }
  }
  
  /**
   * Assert condition
   * @param {boolean} condition - Condition to check
   * @param {string} message - Error message
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }
}

/**
 * Run endpoint security tests
 * @returns {Promise<Object>} Test results
 */
async function runEndpointSecurityTests() {
  const testSuite = new EndpointSecurityTestSuite();
  return await testSuite.runAllTests();
}

// If script is run directly
if (require.main === module) {
  runEndpointSecurityTests()
    .then(results => {
      console.log('\n=== Endpoint Security Test Results ===');
      console.log(`Total Tests: ${results.total}`);
      console.log(`Passed: ${results.passed}`);
      console.log(`Failed: ${results.failed}`);
      console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
      
      if (results.failed > 0) {
        console.log('\nFailed Tests:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`- ${test.name}: ${test.error}`);
          });
      }
      
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('Test framework error:', error);
      process.exit(1);
    });
}

module.exports = {
  EndpointSecurityTestSuite,
  runEndpointSecurityTests,
  createTestApp,
  TEST_DATA,
  TEST_USERS
};
