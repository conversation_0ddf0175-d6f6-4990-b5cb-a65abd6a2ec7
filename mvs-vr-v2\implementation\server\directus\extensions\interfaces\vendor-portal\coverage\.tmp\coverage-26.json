{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/LivePreview.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 487, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 412, "endOffset": 458, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 539, "endOffset": 660, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 602, "endOffset": 651, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 705, "endOffset": 1174, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 2091, "endOffset": 2101, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 2163, "endOffset": 2171, "count": 0}], "isBlockCoverage": false}, {"functionName": "data", "ranges": [{"startOffset": 2252, "endOffset": 2482, "count": 0}], "isBlockCoverage": false}, {"functionName": "handlePreviewChange", "ranges": [{"startOffset": 2501, "endOffset": 2945, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3367, "endOffset": 5855, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3659, "endOffset": 3850, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3884, "endOffset": 3999, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4046, "endOffset": 4137, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4187, "endOffset": 4278, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4331, "endOffset": 4482, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4526, "endOffset": 4671, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4728, "endOffset": 5423, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5495, "endOffset": 5851, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/test-utils/component-test-utils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "createWrapper", "ranges": [{"startOffset": 902, "endOffset": 1797, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1899, "endOffset": 1928, "count": 0}], "isBlockCoverage": false}, {"functionName": "nextTick", "ranges": [{"startOffset": 2060, "endOffset": 2134, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2231, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "wait", "ranges": [{"startOffset": 2440, "endOffset": 2519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2612, "endOffset": 2632, "count": 0}], "isBlockCoverage": false}, {"functionName": "findAll", "ranges": [{"startOffset": 2982, "endOffset": 3573, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3669, "endOffset": 3692, "count": 0}], "isBlockCoverage": false}, {"functionName": "trigger", "ranges": [{"startOffset": 4111, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4800, "endOffset": 4823, "count": 0}], "isBlockCoverage": false}, {"functionName": "setValue", "ranges": [{"startOffset": 5049, "endOffset": 5320, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5417, "endOffset": 5441, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasClass", "ranges": [{"startOffset": 5652, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6246, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}]}]}