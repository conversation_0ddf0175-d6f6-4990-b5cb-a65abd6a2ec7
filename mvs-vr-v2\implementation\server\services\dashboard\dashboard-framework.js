/**
 * Advanced Dashboard Framework
 * Provides real-time data visualization and interactive dashboards
 */

const EventEmitter = require('events');

class DashboardWidget {
  constructor(config) {
    this.id = config.id;
    this.type = config.type; // chart, metric, table, map, etc.
    this.title = config.title;
    this.dataSource = config.dataSource;
    this.refreshInterval = config.refreshInterval || 30000;
    this.config = config.config || {};
    this.lastUpdate = null;
    this.data = null;
  }

  async fetchData() {
    try {
      if (typeof this.dataSource === 'function') {
        this.data = await this.dataSource();
      } else if (typeof this.dataSource === 'string') {
        // Assume it's an API endpoint
        const response = await fetch(this.dataSource);
        this.data = await response.json();
      }
      this.lastUpdate = new Date();
      return this.data;
    } catch (error) {
      console.error(`Error fetching data for widget ${this.id}:`, error);
      throw error;
    }
  }

  serialize() {
    return {
      id: this.id,
      type: this.type,
      title: this.title,
      config: this.config,
      data: this.data,
      lastUpdate: this.lastUpdate
    };
  }
}

class Dashboard extends EventEmitter {
  constructor(config) {
    super();
    this.id = config.id;
    this.title = config.title;
    this.layout = config.layout || [];
    this.widgets = new Map();
    this.refreshIntervals = new Map();
    this.isActive = false;
  }

  addWidget(widgetConfig) {
    const widget = new DashboardWidget(widgetConfig);
    this.widgets.set(widget.id, widget);
    
    // Set up auto-refresh if specified
    if (widget.refreshInterval > 0) {
      const interval = setInterval(async () => {
        if (this.isActive) {
          try {
            await widget.fetchData();
            this.emit('widgetUpdated', widget.id, widget.data);
          } catch (error) {
            this.emit('widgetError', widget.id, error);
          }
        }
      }, widget.refreshInterval);
      
      this.refreshIntervals.set(widget.id, interval);
    }
    
    this.emit('widgetAdded', widget.id);
    return widget;
  }

  removeWidget(widgetId) {
    if (this.refreshIntervals.has(widgetId)) {
      clearInterval(this.refreshIntervals.get(widgetId));
      this.refreshIntervals.delete(widgetId);
    }
    
    const removed = this.widgets.delete(widgetId);
    if (removed) {
      this.emit('widgetRemoved', widgetId);
    }
    return removed;
  }

  async refreshWidget(widgetId) {
    const widget = this.widgets.get(widgetId);
    if (!widget) {
      throw new Error(`Widget ${widgetId} not found`);
    }
    
    try {
      await widget.fetchData();
      this.emit('widgetUpdated', widgetId, widget.data);
      return widget.data;
    } catch (error) {
      this.emit('widgetError', widgetId, error);
      throw error;
    }
  }

  async refreshAll() {
    const promises = Array.from(this.widgets.keys()).map(id => 
      this.refreshWidget(id).catch(error => ({ id, error }))
    );
    
    const results = await Promise.all(promises);
    this.emit('dashboardRefreshed', results);
    return results;
  }

  activate() {
    this.isActive = true;
    this.emit('activated');
  }

  deactivate() {
    this.isActive = false;
    this.emit('deactivated');
  }

  updateLayout(layout) {
    this.layout = layout;
    this.emit('layoutUpdated', layout);
  }

  serialize() {
    return {
      id: this.id,
      title: this.title,
      layout: this.layout,
      widgets: Array.from(this.widgets.values()).map(w => w.serialize()),
      isActive: this.isActive
    };
  }
}

class DashboardManager extends EventEmitter {
  constructor() {
    super();
    this.dashboards = new Map();
    this.activeDashboard = null;
  }

  createDashboard(config) {
    const dashboard = new Dashboard(config);
    this.dashboards.set(dashboard.id, dashboard);
    
    // Forward dashboard events
    dashboard.on('widgetUpdated', (widgetId, data) => {
      this.emit('widgetUpdated', dashboard.id, widgetId, data);
    });
    
    dashboard.on('widgetError', (widgetId, error) => {
      this.emit('widgetError', dashboard.id, widgetId, error);
    });
    
    this.emit('dashboardCreated', dashboard.id);
    return dashboard;
  }

  getDashboard(dashboardId) {
    return this.dashboards.get(dashboardId);
  }

  deleteDashboard(dashboardId) {
    const dashboard = this.dashboards.get(dashboardId);
    if (dashboard) {
      dashboard.deactivate();
      // Clear all widget intervals
      for (const widgetId of dashboard.widgets.keys()) {
        dashboard.removeWidget(widgetId);
      }
      this.dashboards.delete(dashboardId);
      
      if (this.activeDashboard === dashboardId) {
        this.activeDashboard = null;
      }
      
      this.emit('dashboardDeleted', dashboardId);
      return true;
    }
    return false;
  }

  setActiveDashboard(dashboardId) {
    // Deactivate current dashboard
    if (this.activeDashboard) {
      const currentDashboard = this.dashboards.get(this.activeDashboard);
      if (currentDashboard) {
        currentDashboard.deactivate();
      }
    }
    
    // Activate new dashboard
    const newDashboard = this.dashboards.get(dashboardId);
    if (newDashboard) {
      newDashboard.activate();
      this.activeDashboard = dashboardId;
      this.emit('activeDashboardChanged', dashboardId);
      return true;
    }
    
    return false;
  }

  getAllDashboards() {
    return Array.from(this.dashboards.values()).map(d => d.serialize());
  }

  // Predefined widget types
  static getWidgetTypes() {
    return {
      metric: {
        name: 'Metric',
        description: 'Display a single metric value',
        configSchema: {
          format: 'string', // number, percentage, currency
          unit: 'string',
          threshold: 'object'
        }
      },
      lineChart: {
        name: 'Line Chart',
        description: 'Time series line chart',
        configSchema: {
          xAxis: 'string',
          yAxis: 'string',
          timeRange: 'string'
        }
      },
      barChart: {
        name: 'Bar Chart',
        description: 'Bar chart visualization',
        configSchema: {
          xAxis: 'string',
          yAxis: 'string',
          orientation: 'string'
        }
      },
      pieChart: {
        name: 'Pie Chart',
        description: 'Pie chart visualization',
        configSchema: {
          labelField: 'string',
          valueField: 'string'
        }
      },
      table: {
        name: 'Data Table',
        description: 'Tabular data display',
        configSchema: {
          columns: 'array',
          pagination: 'boolean',
          sorting: 'boolean'
        }
      },
      heatmap: {
        name: 'Heatmap',
        description: 'Heatmap visualization',
        configSchema: {
          xAxis: 'string',
          yAxis: 'string',
          valueField: 'string'
        }
      },
      map: {
        name: 'Geographic Map',
        description: 'Geographic data visualization',
        configSchema: {
          latField: 'string',
          lngField: 'string',
          valueField: 'string'
        }
      }
    };
  }
}

module.exports = {
  DashboardManager,
  Dashboard,
  DashboardWidget
};
