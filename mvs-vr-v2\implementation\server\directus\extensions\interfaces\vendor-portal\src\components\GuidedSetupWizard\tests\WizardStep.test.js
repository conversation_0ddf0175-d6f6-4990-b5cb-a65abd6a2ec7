import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import WizardStep from '../WizardStep.vue';

describe('WizardStep', () => {
  let wrapper;
  const mockStepData = {
    field1: 'value1',
    field2: 'value2'
  };
  
  const mockValidationSchema = {
    field1: {
      required: true,
      label: 'Field 1'
    },
    field2: {
      required: true,
      minLength: 5,
      label: 'Field 2'
    },
    field3: {
      required: false,
      pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
      patternMessage: 'Please enter a valid email address',
      label: 'Field 3'
    }
  };
  
  const mockHelpTips = [
    {
      title: 'Tip 1',
      text: 'This is tip 1'
    },
    {
      title: 'Tip 2',
      text: 'This is tip 2'
    }
  ];

  beforeEach(() => {
    wrapper = mount(WizardStep, {
      propsData: {
        title: 'Test Step',
        description: 'Test Description',
        stepData: mockStepData,
        validationSchema: mockValidationSchema,
        helpTips: mockHelpTips,
        showHelpTips: true
      },
      slots: {
        default: '<div class="test-content">Test Content</div>'
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('displays the correct title and description', () => {
    expect(wrapper.find('.step-title').text()).toBe('Test Step');
    expect(wrapper.find('.step-description').text()).toBe('Test Description');
  });

  it('renders the slot content', () => {
    expect(wrapper.find('.test-content').exists()).toBe(true);
    expect(wrapper.find('.test-content').text()).toBe('Test Content');
  });

  it('renders help tips when showHelpTips is true', () => {
    const helpTips = wrapper.findAll('.help-tip');
    expect(helpTips.length).toBe(2);
    
    expect(helpTips.at(0).find('.tip-title').text()).toBe('Tip 1');
    expect(helpTips.at(0).find('.tip-text').text()).toBe('This is tip 1');
    
    expect(helpTips.at(1).find('.tip-title').text()).toBe('Tip 2');
    expect(helpTips.at(1).find('.tip-text').text()).toBe('This is tip 2');
  });

  it('does not render help tips when showHelpTips is false', async () => {
    // Update props
    await wrapper.setProps({ showHelpTips: false });
    
    expect(wrapper.find('.help-tips').exists()).toBe(false);
  });

  it('initializes localStepData with stepData prop', () => {
    expect(wrapper.vm.localStepData).toEqual(mockStepData);
  });

  it('updates localStepData when stepData prop changes', async () => {
    // Arrange
    const newStepData = {
      field1: 'new value1',
      field2: 'new value2',
      field3: 'new field'
    };
    
    // Act
    await wrapper.setProps({ stepData: newStepData });
    
    // Assert
    expect(wrapper.vm.localStepData).toEqual(newStepData);
  });

  it('emits update:step-data when updateStepData is called', async () => {
    // Arrange
    const field = 'field1';
    const value = 'updated value';
    
    // Act
    wrapper.vm.updateStepData(field, value);
    
    // Assert
    expect(wrapper.emitted('update:step-data')).toBeTruthy();
    expect(wrapper.emitted('update:step-data')[0][0]).toEqual({
      ...mockStepData,
      [field]: value
    });
  });

  it('validates required fields correctly', async () => {
    // Arrange
    const invalidStepData = {
      field1: '',
      field2: 'value2'
    };
    
    await wrapper.setProps({ stepData: invalidStepData });
    
    // Act
    const isValid = wrapper.vm.validate();
    
    // Assert
    expect(isValid).toBe(false);
    expect(wrapper.vm.validationErrors).toContain('Field 1 is required');
    expect(wrapper.emitted('validate')).toBeTruthy();
    expect(wrapper.emitted('validate')[0][0]).toBe(false);
  });

  it('validates minLength correctly', async () => {
    // Arrange
    const invalidStepData = {
      field1: 'value1',
      field2: 'val'
    };
    
    await wrapper.setProps({ stepData: invalidStepData });
    
    // Act
    const isValid = wrapper.vm.validate();
    
    // Assert
    expect(isValid).toBe(false);
    expect(wrapper.vm.validationErrors).toContain('Field 2 must be at least 5 characters');
    expect(wrapper.emitted('validate')).toBeTruthy();
    expect(wrapper.emitted('validate')[0][0]).toBe(false);
  });

  it('validates pattern correctly', async () => {
    // Arrange
    const invalidStepData = {
      field1: 'value1',
      field2: 'value2',
      field3: 'invalid-email'
    };
    
    await wrapper.setProps({ stepData: invalidStepData });
    
    // Act
    const isValid = wrapper.vm.validate();
    
    // Assert
    expect(isValid).toBe(false);
    expect(wrapper.vm.validationErrors).toContain('Please enter a valid email address');
    expect(wrapper.emitted('validate')).toBeTruthy();
    expect(wrapper.emitted('validate')[0][0]).toBe(false);
  });

  it('validates successfully when all fields are valid', async () => {
    // Arrange
    const validStepData = {
      field1: 'value1',
      field2: 'value2 with enough length',
      field3: '<EMAIL>'
    };
    
    await wrapper.setProps({ stepData: validStepData });
    
    // Act
    const isValid = wrapper.vm.validate();
    
    // Assert
    expect(isValid).toBe(true);
    expect(wrapper.vm.validationErrors).toHaveLength(0);
    expect(wrapper.emitted('validate')).toBeTruthy();
    expect(wrapper.emitted('validate')[0][0]).toBe(true);
  });

  it('skips validation for empty non-required fields', async () => {
    // Arrange
    const stepData = {
      field1: 'value1',
      field2: 'value2 with enough length',
      field3: '' // Empty but not required
    };
    
    await wrapper.setProps({ stepData });
    
    // Act
    const isValid = wrapper.vm.validate();
    
    // Assert
    expect(isValid).toBe(true);
    expect(wrapper.vm.validationErrors).toHaveLength(0);
  });

  it('displays validation errors when there are any', async () => {
    // Arrange
    wrapper.vm.validationErrors = ['Error 1', 'Error 2'];
    await wrapper.vm.$nextTick();
    
    // Assert
    const errorSection = wrapper.find('.validation-errors');
    expect(errorSection.exists()).toBe(true);
    
    const errorItems = wrapper.findAll('.error-item');
    expect(errorItems.length).toBe(2);
    expect(errorItems.at(0).text()).toBe('Error 1');
    expect(errorItems.at(1).text()).toBe('Error 2');
  });

  it('does not display validation errors when there are none', () => {
    expect(wrapper.find('.validation-errors').exists()).toBe(false);
  });

  it('calls validate when validateStep is called', () => {
    // Arrange
    const validateSpy = vi.spyOn(wrapper.vm, 'validate');
    
    // Act
    wrapper.vm.validateStep();
    
    // Assert
    expect(validateSpy).toHaveBeenCalled();
  });

  it('resets validation errors when resetValidation is called', async () => {
    // Arrange
    wrapper.vm.validationErrors = ['Error 1', 'Error 2'];
    await wrapper.vm.$nextTick();
    
    // Act
    wrapper.vm.resetValidation();
    
    // Assert
    expect(wrapper.vm.validationErrors).toHaveLength(0);
  });
});
