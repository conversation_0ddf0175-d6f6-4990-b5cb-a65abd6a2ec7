{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2113", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/e2e/regression.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 63196, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 63196, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 532, "endOffset": 8066, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8108, "endOffset": 11734, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2427", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/e2e/integration-testing-framework.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7187, "count": 1}, {"startOffset": 477, "endOffset": 507, "count": 0}, {"startOffset": 564, "endOffset": 591, "count": 0}, {"startOffset": 639, "endOffset": 655, "count": 0}, {"startOffset": 719, "endOffset": 747, "count": 0}, {"startOffset": 796, "endOffset": 812, "count": 0}, {"startOffset": 876, "endOffset": 904, "count": 0}, {"startOffset": 953, "endOffset": 969, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateTestId", "ranges": [{"startOffset": 1225, "endOffset": 1265, "count": 0}], "isBlockCoverage": false}, {"functionName": "login", "ranges": [{"startOffset": 1324, "endOffset": 2164, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestAsset", "ranges": [{"startOffset": 2223, "endOffset": 3421, "count": 0}], "isBlockCoverage": false}, {"functionName": "waitForAssetProcessing", "ranges": [{"startOffset": 3505, "endOffset": 4175, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestShowroom", "ranges": [{"startOffset": 4240, "endOffset": 5122, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAssetToShowroom", "ranges": [{"startOffset": 5186, "endOffset": 6147, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateAnalyticsData", "ranges": [{"startOffset": 6221, "endOffset": 7140, "count": 0}], "isBlockCoverage": false}]}]}