#!/usr/bin/env node

/**
 * Advanced Features Installation Script
 * Installs and configures all advanced features for MVS-VR v2
 */

import { execSync } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

class AdvancedFeaturesInstaller {
  constructor() {
    this.features = [
      'Real-time WebSocket Integration',
      'Enhanced Security Measures',
      'Performance Monitoring and Optimization',
      'Machine Learning Integration',
      'Microservices Architecture',
      'Advanced Visualization',
      'Automated Testing and CI/CD Pipeline'
    ];
    
    this.dependencies = {
      production: [
        'ws@^8.14.2',
        'socket.io@^4.7.4',
        'socket.io-client@^4.7.4',
        'speakeasy@^2.0.0',
        'qrcode@^1.5.3',
        'helmet@^7.1.0',
        'cors@^2.8.5',
        'express-validator@^7.0.1',
        'prometheus-client@^15.1.0',
        'grafana-api@^1.0.0',
        '@opentelemetry/api@^1.7.0',
        '@opentelemetry/auto-instrumentations-node@^0.40.0',
        'tensorflow@^4.15.0',
        '@tensorflow/tfjs-node@^4.15.0',
        'ml-matrix@^6.10.7',
        'chart.js@^4.4.0',
        'three@^0.158.0',
        'd3@^7.8.5'
      ],
      development: [
        '@types/ws@^8.5.9',
        '@types/speakeasy@^2.0.10',
        '@types/qrcode@^1.5.5',
        '@types/three@^0.158.3',
        '@types/d3@^7.4.3',
        'playwright@^1.40.1',
        'k6@^0.47.0',
        'artillery@^2.0.0'
      ]
    };
  }

  async install() {
    console.log('🚀 Starting Advanced Features Installation for MVS-VR v2\n');
    
    try {
      await this.checkPrerequisites();
      await this.installDependencies();
      await this.createDirectoryStructure();
      await this.generateConfigurationFiles();
      await this.setupDockerServices();
      await this.initializeServices();
      await this.runTests();
      await this.generateDocumentation();
      
      console.log('\n✅ Advanced Features Installation Complete!');
      console.log('\n📋 Next Steps:');
      console.log('1. Review configuration files in ./config/');
      console.log('2. Start services: npm run start:services');
      console.log('3. Run tests: npm run test:advanced');
      console.log('4. Access monitoring dashboard: http://localhost:3001');
      console.log('5. Check documentation: ./docs/ADVANCED_FEATURES.md');
      
    } catch (error) {
      console.error('❌ Installation failed:', error.message);
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    const requirements = [
      { command: 'node --version', minVersion: '18.0.0', name: 'Node.js' },
      { command: 'npm --version', minVersion: '8.0.0', name: 'npm' },
      { command: 'docker --version', minVersion: '20.0.0', name: 'Docker' },
      { command: 'docker-compose --version', minVersion: '2.0.0', name: 'Docker Compose' }
    ];

    for (const req of requirements) {
      try {
        const output = execSync(req.command, { encoding: 'utf8' });
        const version = output.match(/\d+\.\d+\.\d+/)?.[0];
        
        if (version && this.compareVersions(version, req.minVersion) >= 0) {
          console.log(`  ✅ ${req.name}: ${version}`);
        } else {
          throw new Error(`${req.name} version ${req.minVersion} or higher required`);
        }
      } catch (error) {
        throw new Error(`${req.name} not found or version check failed`);
      }
    }
    
    console.log('✅ Prerequisites check passed\n');
  }

  async installDependencies() {
    console.log('📦 Installing dependencies...');
    
    // Install production dependencies
    console.log('  Installing production dependencies...');
    const prodDeps = this.dependencies.production.join(' ');
    execSync(`npm install ${prodDeps}`, { 
      cwd: projectRoot, 
      stdio: 'inherit' 
    });
    
    // Install development dependencies
    console.log('  Installing development dependencies...');
    const devDeps = this.dependencies.development.join(' ');
    execSync(`npm install --save-dev ${devDeps}`, { 
      cwd: projectRoot, 
      stdio: 'inherit' 
    });
    
    console.log('✅ Dependencies installed\n');
  }

  async createDirectoryStructure() {
    console.log('📁 Creating directory structure...');
    
    const directories = [
      'services/realtime',
      'services/security',
      'services/monitoring',
      'services/ml',
      'services/orchestration',
      'services/dashboard',
      'middleware/security',
      'middleware/monitoring',
      'api/websocket',
      'api/ml',
      'tests/realtime',
      'tests/security',
      'tests/monitoring',
      'tests/ml',
      'tests/e2e',
      'config/advanced',
      'docs/advanced',
      'k8s',
      'monitoring/prometheus',
      'monitoring/grafana/dashboards',
      'monitoring/grafana/provisioning',
      'monitoring/alertmanager',
      '.github/workflows'
    ];

    for (const dir of directories) {
      const fullPath = path.join(projectRoot, dir);
      await fs.mkdir(fullPath, { recursive: true });
      console.log(`  Created: ${dir}`);
    }
    
    console.log('✅ Directory structure created\n');
  }

  async generateConfigurationFiles() {
    console.log('⚙️ Generating configuration files...');
    
    // WebSocket configuration
    await this.writeFile('config/advanced/websocket.json', {
      port: 8080,
      maxConnections: 10000,
      heartbeatInterval: 30000,
      enableCompression: true,
      enableRateLimiting: true,
      redis: {
        host: 'localhost',
        port: 6379,
        password: process.env.REDIS_PASSWORD || 'redis_password'
      }
    });

    // Security configuration
    await this.writeFile('config/advanced/security.json', {
      encryption: {
        algorithm: 'aes-256-gcm',
        keyRotationInterval: 86400000,
        enableFieldEncryption: true
      },
      mfa: {
        serviceName: 'MVS-VR',
        totpWindow: 2,
        backupCodeCount: 10,
        sessionTimeout: 3600000
      },
      auth: {
        jwtExpiresIn: '1h',
        refreshTokenExpiresIn: '7d',
        maxSessionsPerUser: 5,
        enableMFA: true
      }
    });

    // Monitoring configuration
    await this.writeFile('config/advanced/monitoring.json', {
      prometheus: {
        port: 9090,
        scrapeInterval: '15s',
        retentionTime: '15d'
      },
      grafana: {
        port: 3001,
        adminPassword: process.env.GRAFANA_ADMIN_PASSWORD || 'admin'
      },
      alertmanager: {
        port: 9093,
        webhookUrl: process.env.ALERT_WEBHOOK_URL || ''
      }
    });

    // ML configuration
    await this.writeFile('config/advanced/ml.json', {
      tensorflow: {
        backend: 'cpu',
        enableGPU: false
      },
      models: {
        userBehavior: {
          enabled: true,
          updateInterval: 3600000
        },
        predictiveAnalytics: {
          enabled: true,
          trainingInterval: 86400000
        }
      }
    });

    console.log('✅ Configuration files generated\n');
  }

  async setupDockerServices() {
    console.log('🐳 Setting up Docker services...');
    
    // Enhanced docker-compose for advanced features
    const dockerCompose = {
      version: '3.8',
      services: {
        // Existing services...
        
        // Prometheus
        prometheus: {
          image: 'prom/prometheus:latest',
          ports: ['9090:9090'],
          volumes: [
            './monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml',
            'prometheus-data:/prometheus'
          ],
          command: [
            '--config.file=/etc/prometheus/prometheus.yml',
            '--storage.tsdb.path=/prometheus',
            '--storage.tsdb.retention.time=15d',
            '--web.enable-lifecycle'
          ],
          networks: ['mvs-vr-network'],
          restart: 'unless-stopped'
        },
        
        // Grafana
        grafana: {
          image: 'grafana/grafana:latest',
          ports: ['3001:3000'],
          volumes: [
            './monitoring/grafana/dashboards:/var/lib/grafana/dashboards',
            './monitoring/grafana/provisioning:/etc/grafana/provisioning',
            'grafana-data:/var/lib/grafana'
          ],
          environment: {
            GF_SECURITY_ADMIN_PASSWORD: '${GRAFANA_ADMIN_PASSWORD:-admin}',
            GF_USERS_ALLOW_SIGN_UP: 'false'
          },
          networks: ['mvs-vr-network'],
          restart: 'unless-stopped'
        },
        
        // Node Exporter
        'node-exporter': {
          image: 'prom/node-exporter:latest',
          ports: ['9100:9100'],
          volumes: [
            '/proc:/host/proc:ro',
            '/sys:/host/sys:ro',
            '/:/rootfs:ro'
          ],
          command: [
            '--path.procfs=/host/proc',
            '--path.sysfs=/host/sys',
            '--path.rootfs=/rootfs',
            '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
          ],
          networks: ['mvs-vr-network'],
          restart: 'unless-stopped'
        }
      },
      
      volumes: {
        'prometheus-data': null,
        'grafana-data': null
      },
      
      networks: {
        'mvs-vr-network': {
          name: 'mvs-vr-network'
        }
      }
    };

    await this.writeFile('docker-compose.advanced.yml', dockerCompose);
    
    // Prometheus configuration
    const prometheusConfig = {
      global: {
        scrape_interval: '15s',
        evaluation_interval: '15s'
      },
      scrape_configs: [
        {
          job_name: 'mvs-vr-server',
          static_configs: [
            { targets: ['server:3000'] }
          ]
        },
        {
          job_name: 'node-exporter',
          static_configs: [
            { targets: ['node-exporter:9100'] }
          ]
        }
      ]
    };

    await this.writeFile('monitoring/prometheus/prometheus.yml', prometheusConfig, 'yaml');
    
    console.log('✅ Docker services configured\n');
  }

  async initializeServices() {
    console.log('🔧 Initializing services...');
    
    // Create service initialization script
    const initScript = `#!/usr/bin/env node

import { WebSocketManager } from './services/realtime/websocket-manager.js';
import { EventDispatcher } from './services/realtime/event-dispatcher.js';
import { EncryptionService } from './services/security/encryption-service.js';
import { MFAService } from './services/security/mfa-service.js';
import { initializeRealtimeServices } from './api/websocket/realtime-endpoints.js';

async function initializeAdvancedFeatures() {
  console.log('🚀 Initializing Advanced Features...');
  
  try {
    // Load configuration
    const config = await import('./config/advanced/websocket.json', { assert: { type: 'json' } });
    
    // Initialize real-time services
    await initializeRealtimeServices(config.default);
    
    console.log('✅ Advanced Features initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Advanced Features:', error);
    process.exit(1);
  }
}

initializeAdvancedFeatures();
`;

    await this.writeFile('scripts/init-advanced-features.js', initScript);
    
    // Update package.json scripts
    const packageJsonPath = path.join(projectRoot, 'package.json');
    const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
    
    packageJson.scripts = {
      ...packageJson.scripts,
      'start:advanced': 'node scripts/init-advanced-features.js',
      'start:services': 'docker-compose -f docker-compose.yml -f docker-compose.advanced.yml up -d',
      'stop:services': 'docker-compose -f docker-compose.yml -f docker-compose.advanced.yml down',
      'test:advanced': 'vitest run tests/realtime tests/security tests/monitoring tests/ml',
      'test:e2e': 'playwright test',
      'test:performance': 'k6 run tests/performance/load-test.js',
      'monitor:logs': 'docker-compose logs -f',
      'monitor:metrics': 'open http://localhost:9090',
      'monitor:dashboard': 'open http://localhost:3001'
    };
    
    await fs.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2));
    
    console.log('✅ Services initialized\n');
  }

  async runTests() {
    console.log('🧪 Running initial tests...');
    
    try {
      // Run basic tests to ensure everything is working
      execSync('npm run test:advanced', { 
        cwd: projectRoot, 
        stdio: 'inherit' 
      });
      
      console.log('✅ Tests passed\n');
    } catch (error) {
      console.warn('⚠️ Some tests failed, but installation continues\n');
    }
  }

  async generateDocumentation() {
    console.log('📚 Generating documentation...');
    
    const advancedFeaturesDoc = `# Advanced Features Documentation

## Overview

This document describes the advanced features implemented in MVS-VR v2.

## Features Installed

${this.features.map(feature => `- ${feature}`).join('\n')}

## Quick Start

1. Start all services:
   \`\`\`bash
   npm run start:services
   \`\`\`

2. Initialize advanced features:
   \`\`\`bash
   npm run start:advanced
   \`\`\`

3. Access monitoring dashboard:
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3001

## Configuration

Configuration files are located in \`./config/advanced/\`:

- \`websocket.json\` - WebSocket server configuration
- \`security.json\` - Security and authentication settings
- \`monitoring.json\` - Monitoring and alerting configuration
- \`ml.json\` - Machine learning settings

## Testing

Run comprehensive tests:
\`\`\`bash
npm run test:advanced
npm run test:e2e
npm run test:performance
\`\`\`

## Monitoring

- **Metrics**: http://localhost:9090
- **Dashboards**: http://localhost:3001
- **Logs**: \`npm run monitor:logs\`

## Security

The system includes:
- AES-256-GCM encryption
- Multi-factor authentication (TOTP, SMS, Email)
- Advanced session management
- Rate limiting and abuse protection

## Real-time Features

- WebSocket connections with auto-reconnection
- Event-driven architecture with pub/sub
- Real-time data synchronization
- Conflict resolution

## Machine Learning

- User behavior analytics
- Predictive analytics
- Recommendation engine
- Anomaly detection

## Support

For issues and questions, please refer to the main documentation or create an issue in the repository.
`;

    await this.writeFile('docs/ADVANCED_FEATURES.md', advancedFeaturesDoc);
    
    console.log('✅ Documentation generated\n');
  }

  async writeFile(filePath, content, format = 'json') {
    const fullPath = path.join(projectRoot, filePath);
    const dir = path.dirname(fullPath);
    
    await fs.mkdir(dir, { recursive: true });
    
    let fileContent;
    if (format === 'json') {
      fileContent = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
    } else if (format === 'yaml') {
      // Simple YAML conversion for basic objects
      fileContent = this.objectToYaml(content);
    } else {
      fileContent = content;
    }
    
    await fs.writeFile(fullPath, fileContent);
  }

  objectToYaml(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';
    
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        yaml += `${spaces}${key}:\n${this.objectToYaml(value, indent + 1)}`;
      } else if (Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        value.forEach(item => {
          if (typeof item === 'object') {
            yaml += `${spaces}  - ${this.objectToYaml(item, indent + 2).trim()}\n`;
          } else {
            yaml += `${spaces}  - ${item}\n`;
          }
        });
      } else {
        yaml += `${spaces}${key}: ${value}\n`;
      }
    }
    
    return yaml;
  }

  compareVersions(version1, version2) {
    const v1parts = version1.split('.').map(Number);
    const v2parts = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
      const v1part = v1parts[i] || 0;
      const v2part = v2parts[i] || 0;
      
      if (v1part > v2part) return 1;
      if (v1part < v2part) return -1;
    }
    
    return 0;
  }
}

// Run the installer
const installer = new AdvancedFeaturesInstaller();
installer.install().catch(console.error);
