{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/tests/GuidedSetupService.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45686, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45686, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 356, "endOffset": 494, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 888, "endOffset": 9068, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 927, "endOffset": 1173, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1203, "endOffset": 1745, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1284, "endOffset": 1739, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1783, "endOffset": 3600, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1848, "endOffset": 2540, "count": 1}, {"startOffset": 2330, "endOffset": 2539, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2611, "endOffset": 3103, "count": 1}, {"startOffset": 2917, "endOffset": 3102, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3163, "endOffset": 3594, "count": 1}, {"startOffset": 3458, "endOffset": 3593, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3639, "endOffset": 7121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3698, "endOffset": 5102, "count": 1}, {"startOffset": 4652, "endOffset": 5101, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5175, "endOffset": 6433, "count": 1}, {"startOffset": 5973, "endOffset": 6432, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6493, "endOffset": 7115, "count": 1}, {"startOffset": 7019, "endOffset": 7114, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7160, "endOffset": 9064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7221, "endOffset": 8260, "count": 1}, {"startOffset": 7920, "endOffset": 8259, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8317, "endOffset": 9058, "count": 1}, {"startOffset": 8739, "endOffset": 9057, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1239", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/GuidedSetupService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42253, "count": 1}], "isBlockCoverage": true}, {"functionName": "GuidedSetupService", "ranges": [{"startOffset": 580, "endOffset": 927, "count": 1}], "isBlockCoverage": true}, {"functionName": "getOnboardingStatus", "ranges": [{"startOffset": 1089, "endOffset": 1451, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveOnboardingStatus", "ranges": [{"startOffset": 1746, "endOffset": 2750, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCompanyProfile", "ranges": [{"startOffset": 2963, "endOffset": 3750, "count": 0}], "isBlockCoverage": false}, {"functionName": "uploadCompanyLogo", "ranges": [{"startOffset": 3938, "endOffset": 4597, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveUserAccounts", "ranges": [{"startOffset": 4806, "endOffset": 6275, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveBranding", "ranges": [{"startOffset": 6473, "endOffset": 7179, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackWizardAnalytics", "ranges": [{"startOffset": 7384, "endOffset": 7935, "count": 0}], "isBlockCoverage": false}]}]}