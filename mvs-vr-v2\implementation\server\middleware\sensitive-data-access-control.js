/**
 * Sensitive Data Access Control Middleware
 * 
 * This middleware provides fine-grained access control for sensitive data
 * based on user roles, permissions, and data classification levels.
 */

const logger = require('../utils/logger').getLogger('sensitive-data-access-control');

// Data classification levels
const DATA_CLASSIFICATION = {
  PUBLIC: 0,
  INTERNAL: 1,
  CONFIDENTIAL: 2,
  RESTRICTED: 3,
  TOP_SECRET: 4
};

// User roles and their access levels
const ROLE_ACCESS_LEVELS = {
  guest: DATA_CLASSIFICATION.PUBLIC,
  user: DATA_CLASSIFICATION.INTERNAL,
  premium_user: DATA_CLASSIFICATION.CONFIDENTIAL,
  moderator: DATA_CLASSIFICATION.CONFIDENTIAL,
  admin: DATA_CLASSIFICATION.RESTRICTED,
  super_admin: DATA_CLASSIFICATION.TOP_SECRET,
  system: DATA_CLASSIFICATION.TOP_SECRET
};

// Field classification mapping
const FIELD_CLASSIFICATIONS = {
  // Public data
  id: DATA_CLASSIFICATION.PUBLIC,
  name: DATA_CLASSIFICATION.PUBLIC,
  title: DATA_CLASSIFICATION.PUBLIC,
  description: DATA_CLASSIFICATION.PUBLIC,
  created_at: DATA_CLASSIFICATION.PUBLIC,
  updated_at: DATA_CLASSIFICATION.PUBLIC,
  status: DATA_CLASSIFICATION.PUBLIC,
  
  // Internal data
  email: DATA_CLASSIFICATION.INTERNAL,
  username: DATA_CLASSIFICATION.INTERNAL,
  profile: DATA_CLASSIFICATION.INTERNAL,
  preferences: DATA_CLASSIFICATION.INTERNAL,
  
  // Confidential data
  phone: DATA_CLASSIFICATION.CONFIDENTIAL,
  address: DATA_CLASSIFICATION.CONFIDENTIAL,
  birth_date: DATA_CLASSIFICATION.CONFIDENTIAL,
  payment_method: DATA_CLASSIFICATION.CONFIDENTIAL,
  
  // Restricted data
  ssn: DATA_CLASSIFICATION.RESTRICTED,
  tax_id: DATA_CLASSIFICATION.RESTRICTED,
  credit_card: DATA_CLASSIFICATION.RESTRICTED,
  bank_account: DATA_CLASSIFICATION.RESTRICTED,
  medical_records: DATA_CLASSIFICATION.RESTRICTED,
  
  // Top secret data
  password: DATA_CLASSIFICATION.TOP_SECRET,
  secret: DATA_CLASSIFICATION.TOP_SECRET,
  private_key: DATA_CLASSIFICATION.TOP_SECRET,
  api_key: DATA_CLASSIFICATION.TOP_SECRET,
  token: DATA_CLASSIFICATION.TOP_SECRET,
  credential: DATA_CLASSIFICATION.TOP_SECRET
};

// Endpoint-specific access rules
const ENDPOINT_ACCESS_RULES = {
  '/api/admin/*': {
    requiredRole: 'admin',
    allowedClassifications: [
      DATA_CLASSIFICATION.PUBLIC,
      DATA_CLASSIFICATION.INTERNAL,
      DATA_CLASSIFICATION.CONFIDENTIAL,
      DATA_CLASSIFICATION.RESTRICTED
    ]
  },
  '/api/users/profile': {
    requiredRole: 'user',
    allowedClassifications: [
      DATA_CLASSIFICATION.PUBLIC,
      DATA_CLASSIFICATION.INTERNAL,
      DATA_CLASSIFICATION.CONFIDENTIAL
    ],
    ownershipRequired: true
  },
  '/api/users/*/sensitive': {
    requiredRole: 'admin',
    allowedClassifications: [
      DATA_CLASSIFICATION.PUBLIC,
      DATA_CLASSIFICATION.INTERNAL,
      DATA_CLASSIFICATION.CONFIDENTIAL,
      DATA_CLASSIFICATION.RESTRICTED
    ]
  },
  '/api/system/*': {
    requiredRole: 'super_admin',
    allowedClassifications: Object.values(DATA_CLASSIFICATION)
  }
};

// Special permissions
const SPECIAL_PERMISSIONS = {
  VIEW_ALL_USERS: 'view_all_users',
  VIEW_SENSITIVE_DATA: 'view_sensitive_data',
  VIEW_SYSTEM_DATA: 'view_system_data',
  BYPASS_ACCESS_CONTROL: 'bypass_access_control'
};

/**
 * Get user's access level
 * @param {Object} user - User object
 * @returns {number} Access level
 */
function getUserAccessLevel(user) {
  if (!user || !user.role) {
    return DATA_CLASSIFICATION.PUBLIC;
  }
  
  return ROLE_ACCESS_LEVELS[user.role] || DATA_CLASSIFICATION.PUBLIC;
}

/**
 * Check if user has special permission
 * @param {Object} user - User object
 * @param {string} permission - Permission to check
 * @returns {boolean} Whether user has permission
 */
function hasSpecialPermission(user, permission) {
  if (!user || !user.permissions) {
    return false;
  }
  
  return user.permissions.includes(permission);
}

/**
 * Get field classification level
 * @param {string} fieldName - Field name
 * @param {string} fieldPath - Full field path
 * @returns {number} Classification level
 */
function getFieldClassification(fieldName, fieldPath = '') {
  // Check exact field name match
  if (FIELD_CLASSIFICATIONS.hasOwnProperty(fieldName)) {
    return FIELD_CLASSIFICATIONS[fieldName];
  }
  
  // Check pattern matches
  const lowerFieldName = fieldName.toLowerCase();
  const lowerFieldPath = fieldPath.toLowerCase();
  
  // Top secret patterns
  if (lowerFieldName.includes('password') || 
      lowerFieldName.includes('secret') || 
      lowerFieldName.includes('token') || 
      lowerFieldName.includes('key')) {
    return DATA_CLASSIFICATION.TOP_SECRET;
  }
  
  // Restricted patterns
  if (lowerFieldName.includes('ssn') || 
      lowerFieldName.includes('credit') || 
      lowerFieldName.includes('bank') || 
      lowerFieldName.includes('medical')) {
    return DATA_CLASSIFICATION.RESTRICTED;
  }
  
  // Confidential patterns
  if (lowerFieldName.includes('phone') || 
      lowerFieldName.includes('address') || 
      lowerFieldName.includes('birth') || 
      lowerFieldName.includes('payment')) {
    return DATA_CLASSIFICATION.CONFIDENTIAL;
  }
  
  // Internal patterns
  if (lowerFieldName.includes('email') || 
      lowerFieldName.includes('profile') || 
      lowerFieldName.includes('preference')) {
    return DATA_CLASSIFICATION.INTERNAL;
  }
  
  // Default to public
  return DATA_CLASSIFICATION.PUBLIC;
}

/**
 * Check if user can access endpoint
 * @param {Object} req - Express request object
 * @param {Object} user - User object
 * @returns {Object} Access check result
 */
function checkEndpointAccess(req, user) {
  const path = req.path;
  const method = req.method;
  
  // Find matching endpoint rule
  let matchingRule = null;
  for (const [pattern, rule] of Object.entries(ENDPOINT_ACCESS_RULES)) {
    const regex = new RegExp(pattern.replace('*', '.*'));
    if (regex.test(path)) {
      matchingRule = rule;
      break;
    }
  }
  
  if (!matchingRule) {
    return { allowed: true, reason: 'No specific rule found' };
  }
  
  // Check role requirement
  const userRole = user?.role || 'guest';
  const requiredRole = matchingRule.requiredRole;
  
  if (requiredRole && !hasRoleOrHigher(userRole, requiredRole)) {
    return { 
      allowed: false, 
      reason: `Insufficient role. Required: ${requiredRole}, Current: ${userRole}` 
    };
  }
  
  // Check ownership requirement
  if (matchingRule.ownershipRequired) {
    const resourceUserId = extractUserIdFromPath(path);
    if (resourceUserId && resourceUserId !== user?.id && !hasSpecialPermission(user, SPECIAL_PERMISSIONS.VIEW_ALL_USERS)) {
      return { 
        allowed: false, 
        reason: 'Resource ownership required' 
      };
    }
  }
  
  return { allowed: true, rule: matchingRule };
}

/**
 * Check if user has role or higher
 * @param {string} userRole - User's role
 * @param {string} requiredRole - Required role
 * @returns {boolean} Whether user has sufficient role
 */
function hasRoleOrHigher(userRole, requiredRole) {
  const userLevel = ROLE_ACCESS_LEVELS[userRole] || 0;
  const requiredLevel = ROLE_ACCESS_LEVELS[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
}

/**
 * Extract user ID from path
 * @param {string} path - Request path
 * @returns {string|null} User ID or null
 */
function extractUserIdFromPath(path) {
  const userIdMatch = path.match(/\/users\/([^\/]+)/);
  return userIdMatch ? userIdMatch[1] : null;
}

/**
 * Filter data based on access control
 * @param {any} data - Data to filter
 * @param {Object} user - User object
 * @param {Object} accessRule - Access rule
 * @param {string} path - Current path
 * @returns {any} Filtered data
 */
function filterDataByAccess(data, user, accessRule, path = '') {
  if (data == null) {
    return data;
  }
  
  // Check for bypass permission
  if (hasSpecialPermission(user, SPECIAL_PERMISSIONS.BYPASS_ACCESS_CONTROL)) {
    return data;
  }
  
  const userAccessLevel = getUserAccessLevel(user);
  
  if (typeof data !== 'object') {
    return data;
  }
  
  // Handle arrays
  if (Array.isArray(data)) {
    return data.map((item, index) => 
      filterDataByAccess(item, user, accessRule, `${path}[${index}]`)
    );
  }
  
  // Handle objects
  const result = {};
  
  for (const [key, value] of Object.entries(data)) {
    const currentPath = path ? `${path}.${key}` : key;
    const fieldClassification = getFieldClassification(key, currentPath);
    
    // Check if user can access this field
    if (fieldClassification <= userAccessLevel) {
      // User has access to this field
      result[key] = filterDataByAccess(value, user, accessRule, currentPath);
    } else if (accessRule && accessRule.allowedClassifications && 
               accessRule.allowedClassifications.includes(fieldClassification)) {
      // Field is allowed by specific endpoint rule
      result[key] = filterDataByAccess(value, user, accessRule, currentPath);
    } else {
      // User doesn't have access - field is filtered out
      logger.debug(`Field ${currentPath} filtered due to insufficient access level`, {
        fieldClassification,
        userAccessLevel,
        user: user?.id || 'anonymous'
      });
    }
  }
  
  return result;
}

/**
 * Sensitive Data Access Control middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function sensitiveDataAccessControl(options = {}) {
  const {
    enabled = true,
    logAccess = true,
    strictMode = false,
    customClassifications = {},
    customRoles = {}
  } = options;
  
  // Merge custom configurations
  Object.assign(FIELD_CLASSIFICATIONS, customClassifications);
  Object.assign(ROLE_ACCESS_LEVELS, customRoles);
  
  return (req, res, next) => {
    if (!enabled) {
      return next();
    }
    
    const user = req.user;
    
    // Check endpoint access
    const endpointAccess = checkEndpointAccess(req, user);
    
    if (!endpointAccess.allowed) {
      if (logAccess) {
        logger.warn('Access denied to sensitive endpoint', {
          path: req.path,
          method: req.method,
          user: user?.id || 'anonymous',
          reason: endpointAccess.reason
        });
      }
      
      return res.status(403).json({
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: strictMode ? endpointAccess.reason : 'Access denied to this resource'
        }
      });
    }
    
    // Store original res.json method
    const originalJson = res.json;
    
    // Override res.json method
    res.json = function(data) {
      try {
        // Apply access control filtering
        const filteredData = filterDataByAccess(data, user, endpointAccess.rule);
        
        if (logAccess) {
          logger.info('Sensitive data access control applied', {
            path: req.path,
            method: req.method,
            user: user?.id || 'anonymous',
            userRole: user?.role || 'guest',
            accessLevel: getUserAccessLevel(user)
          });
        }
        
        // Call original json method with filtered data
        return originalJson.call(this, filteredData);
        
      } catch (error) {
        logger.error('Error in sensitive data access control:', error);
        return originalJson.call(this, data);
      }
    };
    
    next();
  };
}

/**
 * Get access control statistics
 * @returns {Object} Access control statistics
 */
function getAccessControlStats() {
  return {
    dataClassificationLevels: Object.keys(DATA_CLASSIFICATION).length,
    supportedRoles: Object.keys(ROLE_ACCESS_LEVELS).length,
    fieldClassifications: Object.keys(FIELD_CLASSIFICATIONS).length,
    endpointRules: Object.keys(ENDPOINT_ACCESS_RULES).length,
    specialPermissions: Object.keys(SPECIAL_PERMISSIONS).length
  };
}

module.exports = {
  sensitiveDataAccessControl,
  getAccessControlStats,
  getUserAccessLevel,
  getFieldClassification,
  checkEndpointAccess,
  DATA_CLASSIFICATION,
  ROLE_ACCESS_LEVELS,
  SPECIAL_PERMISSIONS
};
