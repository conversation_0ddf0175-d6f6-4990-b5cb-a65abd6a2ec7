#!/usr/bin/env node

/**
 * Simple Functionality Test
 * 
 * This script tests the core functionality of all implemented systems
 */

import process from 'node:process';

async function runSimpleTests() {
  console.log('🧪 Running Simple Functionality Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  // Test 1: Predictive Monitoring
  try {
    console.log('📊 Testing Predictive Monitoring...');
    
    const { EnhancedAnomalyDetectionService } = await import('./services/monitoring/enhanced-anomaly-detection.js');
    const { PredictiveAlertManager } = await import('./services/monitoring/predictive-alert-manager.js');
    
    // Create services
    const anomalyService = new EnhancedAnomalyDetectionService();
    const alertManager = new PredictiveAlertManager();
    
    // Test anomaly detection with sample data
    const sampleData = [];
    for (let i = 0; i < 50; i++) {
      sampleData.push({
        timestamp: new Date(Date.now() - (50 - i) * 60000).toISOString(),
        value: 50 + Math.random() * 10 + (i > 40 ? 50 : 0) // Add anomaly at the end
      });
    }
    
    const anomalies = await anomalyService.detectAnomalies(sampleData, {
      metric: 'test_metric',
      algorithms: ['z-score', 'mad']
    });
    
    console.log(`   ✅ Anomaly detection: Found ${anomalies.length} anomalies`);
    
    // Test alert generation
    if (anomalies.length > 0) {
      const alert = alertManager.generateAnomalyAlert(anomalies[0], {
        metric: 'test_metric'
      });
      console.log(`   ✅ Alert generation: Created alert ${alert.id}`);
    }
    
    console.log('✅ Predictive Monitoring tests passed\n');
    passed++;
    
  } catch (error) {
    console.error('❌ Predictive Monitoring tests failed:', error.message);
    failed++;
  }
  
  // Test 2: Business Metrics
  try {
    console.log('📈 Testing Business Metrics...');
    
    const { BusinessMetricsCollector } = await import('./services/metrics/business-metrics-collector.js');
    const { BusinessMetricsAnalytics } = await import('./services/metrics/business-metrics-analytics.js');
    
    // Create services
    const collector = new BusinessMetricsCollector({
      configPath: './config/business-metrics.json',
      outputPath: './temp/test-metrics'
    });
    
    const analytics = new BusinessMetricsAnalytics();
    analytics.setMetricsCollector(collector);
    
    // Add test metrics
    collector.addCustomMetric('cpu_usage', 75.5, {
      name: 'CPU Usage',
      category: 'performance',
      unit: 'percentage'
    });
    
    collector.addCustomMetric('memory_usage', 68.2, {
      name: 'Memory Usage',
      category: 'performance',
      unit: 'percentage'
    });
    
    // Add trending data
    for (let i = 0; i < 20; i++) {
      collector.addCustomMetric('trending_metric', i * 2 + Math.random() * 5, {
        name: 'Trending Metric',
        category: 'performance'
      });
    }
    
    // Test analytics
    await analytics.runAnalytics();
    const results = analytics.getAnalyticsResults();
    
    console.log(`   ✅ Metrics collection: ${collector.getCurrentMetrics().length} metrics`);
    console.log(`   ✅ Trend analysis: ${results.summary.trendsCount} trends identified`);
    console.log(`   ✅ Forecasting: ${results.summary.forecastsCount} forecasts generated`);
    
    // Test business insights
    const insights = collector.getBusinessInsights();
    console.log(`   ✅ Business insights: ${insights.alerts.length} alerts, ${insights.recommendations.length} recommendations`);
    
    console.log('✅ Business Metrics tests passed\n');
    passed++;
    
  } catch (error) {
    console.error('❌ Business Metrics tests failed:', error.message);
    failed++;
  }
  
  // Test 3: Integration Test
  try {
    console.log('🔗 Testing Integration...');
    
    const { BusinessMetricsCollector } = await import('./services/metrics/business-metrics-collector.js');
    const { EnhancedAnomalyDetectionService } = await import('./services/monitoring/enhanced-anomaly-detection.js');
    const { PredictiveAlertManager } = await import('./services/monitoring/predictive-alert-manager.js');
    
    // Create integrated system
    const collector = new BusinessMetricsCollector();
    const anomalyService = new EnhancedAnomalyDetectionService();
    const alertManager = new PredictiveAlertManager();
    
    // Simulate metric collection with anomaly
    const metricHistory = [];
    for (let i = 0; i < 30; i++) {
      const value = 50 + Math.random() * 10 + (i > 25 ? 40 : 0); // Anomaly at end
      const metric = {
        timestamp: new Date(Date.now() - (30 - i) * 60000).toISOString(),
        value
      };
      metricHistory.push(metric);
      
      collector.addCustomMetric('integrated_metric', value, {
        name: 'Integrated Test Metric',
        category: 'performance'
      });
    }
    
    // Detect anomalies in the collected data
    const anomalies = await anomalyService.detectAnomalies(metricHistory, {
      metric: 'integrated_metric'
    });
    
    // Generate alerts for anomalies
    let alertsGenerated = 0;
    for (const anomaly of anomalies) {
      const alert = alertManager.generateAnomalyAlert(anomaly, {
        metric: 'integrated_metric'
      });
      if (alert) alertsGenerated++;
    }
    
    console.log(`   ✅ Integration: ${metricHistory.length} metrics → ${anomalies.length} anomalies → ${alertsGenerated} alerts`);
    console.log('✅ Integration tests passed\n');
    passed++;
    
  } catch (error) {
    console.error('❌ Integration tests failed:', error.message);
    failed++;
  }
  
  // Test 4: Performance Test
  try {
    console.log('⚡ Testing Performance...');
    
    const { BusinessMetricsCollector } = await import('./services/metrics/business-metrics-collector.js');
    
    const collector = new BusinessMetricsCollector();
    
    // Performance test: Add 1000 metrics
    const startTime = Date.now();
    
    for (let i = 0; i < 1000; i++) {
      collector.addCustomMetric(`perf_metric_${i}`, Math.random() * 100, {
        name: `Performance Metric ${i}`,
        category: 'performance'
      });
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const currentMetrics = collector.getCurrentMetrics();
    
    console.log(`   ✅ Performance: Added 1000 metrics in ${duration}ms`);
    console.log(`   ✅ Storage: ${currentMetrics.length} metrics stored`);
    console.log(`   ✅ Rate: ${(1000 / duration * 1000).toFixed(0)} metrics/second`);
    
    if (duration < 5000 && currentMetrics.length >= 1000) {
      console.log('✅ Performance tests passed\n');
      passed++;
    } else {
      throw new Error(`Performance below threshold: ${duration}ms for 1000 metrics`);
    }
    
  } catch (error) {
    console.error('❌ Performance tests failed:', error.message);
    failed++;
  }
  
  // Results
  const total = passed + failed;
  console.log('🎯 Simple Test Results');
  console.log('======================');
  console.log(`Total Tests: ${total}`);
  console.log(`Passed: ${passed} ✅`);
  console.log(`Failed: ${failed} ❌`);
  console.log(`Success Rate: ${total > 0 ? ((passed / total) * 100).toFixed(1) : 0}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The system is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the error messages above.');
  }
  
  return { total, passed, failed };
}

// Run tests
runSimpleTests()
  .then(results => {
    const exitCode = results.failed === 0 ? 0 : 1;
    console.log(`\n🏁 Tests completed with exit code: ${exitCode}`);
    process.exit(exitCode);
  })
  .catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
