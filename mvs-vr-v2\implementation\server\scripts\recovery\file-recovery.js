/**
 * File Recovery Script
 * 
 * This script handles recovery of file storage from backups.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync } = require('child_process');

const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);
const statAsync = promisify(fs.stat);

const logger = require('../../utils/logger').getLogger('file-recovery');

// Configuration
const config = {
  backupPath: process.env.FILES_BACKUP_PATH || '/backups/files',
  uploadPath: process.env.UPLOAD_PATH || path.join(__dirname, '../../uploads'),
  tempPath: path.join(__dirname, '../../temp/file-recovery')
};

/**
 * Recover files from backup
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverFiles(options = {}) {
  const {
    source = null,
    target = config.uploadPath,
    test = false,
    selective = null // Array of specific paths to recover
  } = options;
  
  logger.info('Starting file recovery');
  
  try {
    // Ensure target directory exists
    if (!await existsAsync(target)) {
      await mkdirAsync(target, { recursive: true });
    }
    
    // Ensure temp directory exists
    if (!await existsAsync(config.tempPath)) {
      await mkdirAsync(config.tempPath, { recursive: true });
    }
    
    let backupSource = source;
    
    // If no source specified, find latest backup
    if (!backupSource) {
      backupSource = await findLatestBackup();
    }
    
    if (!backupSource) {
      throw new Error('No file backup found');
    }
    
    logger.info(`Recovering files from: ${backupSource}`);
    
    // Extract backup if it's an archive
    let extractedPath = backupSource;
    if (backupSource.endsWith('.tar.gz') || backupSource.endsWith('.zip')) {
      extractedPath = await extractBackup(backupSource);
    }
    
    if (test) {
      logger.info('[TEST] Would perform file recovery');
      const fileCount = await countFiles(extractedPath);
      return {
        success: true,
        test: true,
        source: backupSource,
        target,
        estimatedFiles: fileCount
      };
    }
    
    // Perform file recovery
    const result = await performFileRestore(extractedPath, target, selective);
    
    // Validate recovered files
    const validation = await validateFiles(target);
    
    logger.info(`File recovery completed. Recovered ${result.copiedFiles} files`);
    
    return {
      success: true,
      source: backupSource,
      target,
      validation,
      ...result
    };
    
  } catch (error) {
    logger.error(`File recovery failed: ${error.message}`, { error });
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Find latest file backup
 * @returns {Promise<string|null>} Latest backup path
 */
async function findLatestBackup() {
  try {
    if (!await existsAsync(config.backupPath)) {
      return null;
    }
    
    // List backup files
    const files = await fs.promises.readdir(config.backupPath);
    const fileBackups = files
      .filter(file => file.startsWith('files-') && (file.endsWith('.tar.gz') || file.endsWith('.zip')))
      .map(file => ({
        name: file,
        path: path.join(config.backupPath, file),
        mtime: fs.statSync(path.join(config.backupPath, file)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);
    
    return fileBackups.length > 0 ? fileBackups[0].path : null;
  } catch (error) {
    logger.error(`Failed to find latest backup: ${error.message}`);
    return null;
  }
}

/**
 * Extract backup archive
 * @param {string} backupPath - Backup file path
 * @returns {Promise<string>} Extracted directory path
 */
async function extractBackup(backupPath) {
  const extractDir = path.join(config.tempPath, `extract-${Date.now()}`);
  
  try {
    await mkdirAsync(extractDir, { recursive: true });
    
    if (backupPath.endsWith('.tar.gz')) {
      execSync(`tar -xzf "${backupPath}" -C "${extractDir}"`, { stdio: 'inherit' });
    } else if (backupPath.endsWith('.zip')) {
      execSync(`unzip -q "${backupPath}" -d "${extractDir}"`, { stdio: 'inherit' });
    }
    
    logger.info(`Extracted backup to: ${extractDir}`);
    return extractDir;
  } catch (error) {
    logger.error(`Failed to extract backup: ${error.message}`);
    throw error;
  }
}

/**
 * Perform file restore
 * @param {string} source - Source directory
 * @param {string} target - Target directory
 * @param {Array|null} selective - Selective paths to restore
 * @returns {Promise<Object>} Restore result
 */
async function performFileRestore(source, target, selective = null) {
  const startTime = Date.now();
  let copiedFiles = 0;
  let copiedSize = 0;
  const errors = [];
  
  try {
    if (selective && selective.length > 0) {
      // Selective restore
      for (const selectivePath of selective) {
        const sourcePath = path.join(source, selectivePath);
        const targetPath = path.join(target, selectivePath);
        
        if (await existsAsync(sourcePath)) {
          const result = await copyPath(sourcePath, targetPath);
          copiedFiles += result.files;
          copiedSize += result.size;
          errors.push(...result.errors);
        } else {
          errors.push(`Selective path not found: ${selectivePath}`);
        }
      }
    } else {
      // Full restore
      const result = await copyPath(source, target);
      copiedFiles = result.files;
      copiedSize = result.size;
      errors.push(...result.errors);
    }
    
    const duration = Date.now() - startTime;
    logger.info(`File restore completed in ${duration}ms. Copied ${copiedFiles} files (${formatBytes(copiedSize)})`);
    
    return {
      duration,
      copiedFiles,
      copiedSize,
      errors
    };
    
  } catch (error) {
    logger.error(`File restore failed: ${error.message}`);
    throw error;
  }
}

/**
 * Copy files/directories from source to target
 * @param {string} source - Source path
 * @param {string} target - Target path
 * @returns {Promise<Object>} Copy result
 */
async function copyPath(source, target) {
  let files = 0;
  let size = 0;
  const errors = [];
  
  try {
    const stats = await statAsync(source);
    
    if (stats.isFile()) {
      // Copy single file
      const targetDir = path.dirname(target);
      if (!await existsAsync(targetDir)) {
        await mkdirAsync(targetDir, { recursive: true });
      }
      
      await fs.promises.copyFile(source, target);
      files = 1;
      size = stats.size;
      
    } else if (stats.isDirectory()) {
      // Copy directory recursively
      await copyDirectory(source, target, (fileCount, fileSize, error) => {
        files += fileCount;
        size += fileSize;
        if (error) errors.push(error);
      });
    }
    
  } catch (error) {
    errors.push(`Failed to copy ${source}: ${error.message}`);
  }
  
  return { files, size, errors };
}

/**
 * Copy directory recursively
 * @param {string} source - Source directory
 * @param {string} target - Target directory
 * @param {Function} callback - Progress callback
 */
async function copyDirectory(source, target, callback) {
  try {
    if (!await existsAsync(target)) {
      await mkdirAsync(target, { recursive: true });
    }
    
    const items = await fs.promises.readdir(source);
    
    for (const item of items) {
      const sourcePath = path.join(source, item);
      const targetPath = path.join(target, item);
      
      try {
        const stats = await statAsync(sourcePath);
        
        if (stats.isFile()) {
          await fs.promises.copyFile(sourcePath, targetPath);
          callback(1, stats.size, null);
        } else if (stats.isDirectory()) {
          await copyDirectory(sourcePath, targetPath, callback);
        }
      } catch (error) {
        callback(0, 0, `Failed to copy ${sourcePath}: ${error.message}`);
      }
    }
  } catch (error) {
    callback(0, 0, `Failed to read directory ${source}: ${error.message}`);
  }
}

/**
 * Count files in directory
 * @param {string} directory - Directory path
 * @returns {Promise<number>} File count
 */
async function countFiles(directory) {
  let count = 0;
  
  try {
    const items = await fs.promises.readdir(directory);
    
    for (const item of items) {
      const itemPath = path.join(directory, item);
      const stats = await statAsync(itemPath);
      
      if (stats.isFile()) {
        count++;
      } else if (stats.isDirectory()) {
        count += await countFiles(itemPath);
      }
    }
  } catch (error) {
    logger.error(`Failed to count files in ${directory}: ${error.message}`);
  }
  
  return count;
}

/**
 * Validate recovered files
 * @param {string} targetPath - Target directory path
 * @returns {Promise<Object>} Validation result
 */
async function validateFiles(targetPath) {
  const validation = {
    valid: true,
    errors: [],
    warnings: [],
    stats: {}
  };
  
  try {
    if (!await existsAsync(targetPath)) {
      validation.valid = false;
      validation.errors.push('Target directory does not exist');
      return validation;
    }
    
    // Get directory statistics
    const fileCount = await countFiles(targetPath);
    const dirStats = await statAsync(targetPath);
    
    validation.stats = {
      fileCount,
      lastModified: dirStats.mtime,
      accessible: true
    };
    
    // Check if directory is writable
    try {
      const testFile = path.join(targetPath, '.write-test');
      await fs.promises.writeFile(testFile, 'test');
      await fs.promises.unlink(testFile);
      validation.stats.writable = true;
    } catch (error) {
      validation.warnings.push('Target directory is not writable');
      validation.stats.writable = false;
    }
    
  } catch (error) {
    validation.valid = false;
    validation.errors.push(`File validation failed: ${error.message}`);
  }
  
  return validation;
}

/**
 * Format bytes to human readable string
 * @param {number} bytes - Bytes
 * @returns {string} Formatted string
 */
function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// If script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    source: args.find(arg => arg.startsWith('--source='))?.split('=')[1],
    target: args.find(arg => arg.startsWith('--target='))?.split('=')[1],
    selective: args.find(arg => arg.startsWith('--selective='))?.split('=')[1]?.split(',')
  };
  
  recoverFiles(options)
    .then(result => {
      if (result.success) {
        console.log('File recovery completed successfully');
        if (result.copiedFiles !== undefined) {
          console.log(`Recovered ${result.copiedFiles} files`);
        }
        process.exit(0);
      } else {
        console.error('File recovery failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  recoverFiles,
  findLatestBackup,
  validateFiles
};
