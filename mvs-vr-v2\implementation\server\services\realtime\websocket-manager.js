/**
 * Enhanced WebSocket Manager Service
 * Provides centralized WebSocket connection management with advanced features
 */

import { WebSocketServer } from 'ws';
import { EventEmitter } from 'events';
import { createHash } from 'crypto';
import { performance } from 'perf_hooks';
import Redis from 'ioredis';

export class WebSocketManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      port: options.port || 8080,
      maxConnections: options.maxConnections || 10000,
      heartbeatInterval: options.heartbeatInterval || 30000,
      connectionTimeout: options.connectionTimeout || 60000,
      maxReconnectAttempts: options.maxReconnectAttempts || 5,
      reconnectDelay: options.reconnectDelay || 1000,
      enableCompression: options.enableCompression || true,
      enableRateLimiting: options.enableRateLimiting || true,
      rateLimit: options.rateLimit || { windowMs: 60000, max: 100 },
      redis: options.redis || null,
      ...options
    };

    // Connection management
    this.connections = new Map();
    this.connectionPools = new Map();
    this.server = null;
    this.redis = null;
    
    // Performance metrics
    this.metrics = {
      totalConnections: 0,
      activeConnections: 0,
      messagesPerSecond: 0,
      averageLatency: 0,
      errorRate: 0,
      lastReset: Date.now()
    };

    // Rate limiting
    this.rateLimiters = new Map();
    
    this.initialize();
  }

  async initialize() {
    try {
      // Initialize Redis for clustering support
      if (this.options.redis) {
        this.redis = new Redis(this.options.redis);
        await this.redis.ping();
        console.log('✅ Redis connection established for WebSocket clustering');
      }

      // Create WebSocket server
      this.server = new WebSocketServer({
        port: this.options.port,
        perMessageDeflate: this.options.enableCompression,
        maxPayload: 16 * 1024 * 1024, // 16MB max payload
        clientTracking: true
      });

      this.setupEventHandlers();
      this.startMetricsCollection();
      this.startHealthCheck();

      console.log(`🚀 WebSocket Manager started on port ${this.options.port}`);
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket Manager:', error);
      this.emit('error', error);
    }
  }

  setupEventHandlers() {
    this.server.on('connection', (ws, request) => {
      this.handleConnection(ws, request);
    });

    this.server.on('error', (error) => {
      console.error('WebSocket Server Error:', error);
      this.emit('error', error);
    });

    // Handle graceful shutdown
    process.on('SIGTERM', () => this.shutdown());
    process.on('SIGINT', () => this.shutdown());
  }

  async handleConnection(ws, request) {
    const connectionId = this.generateConnectionId();
    const clientInfo = this.extractClientInfo(request);
    
    // Rate limiting check
    if (this.options.enableRateLimiting && !this.checkRateLimit(clientInfo.ip)) {
      ws.close(1008, 'Rate limit exceeded');
      return;
    }

    // Connection limit check
    if (this.connections.size >= this.options.maxConnections) {
      ws.close(1013, 'Server overloaded');
      return;
    }

    const connection = {
      id: connectionId,
      ws,
      clientInfo,
      connectedAt: Date.now(),
      lastActivity: Date.now(),
      subscriptions: new Set(),
      authenticated: false,
      userId: null,
      metadata: {},
      messageCount: 0,
      bytesReceived: 0,
      bytesSent: 0
    };

    this.connections.set(connectionId, connection);
    this.metrics.totalConnections++;
    this.metrics.activeConnections++;

    // Setup connection event handlers
    this.setupConnectionHandlers(connection);

    // Send welcome message
    this.sendToConnection(connectionId, {
      type: 'connection',
      connectionId,
      timestamp: Date.now(),
      serverInfo: {
        version: '2.0.0',
        features: ['compression', 'heartbeat', 'clustering']
      }
    });

    console.log(`🔗 New WebSocket connection: ${connectionId} from ${clientInfo.ip}`);
    this.emit('connection', { connectionId, clientInfo });
  }

  setupConnectionHandlers(connection) {
    const { ws, id } = connection;

    ws.on('message', async (data) => {
      try {
        await this.handleMessage(connection, data);
      } catch (error) {
        console.error(`Message handling error for ${id}:`, error);
        this.sendError(id, 'MESSAGE_PROCESSING_ERROR', error.message);
      }
    });

    ws.on('close', (code, reason) => {
      this.handleDisconnection(id, code, reason);
    });

    ws.on('error', (error) => {
      console.error(`WebSocket error for ${id}:`, error);
      this.handleDisconnection(id, 1011, 'Internal error');
    });

    ws.on('pong', () => {
      connection.lastActivity = Date.now();
    });

    // Start heartbeat
    this.startHeartbeat(connection);
  }

  async handleMessage(connection, data) {
    const startTime = performance.now();
    connection.lastActivity = Date.now();
    connection.messageCount++;
    connection.bytesReceived += data.length;

    let message;
    try {
      message = JSON.parse(data.toString());
    } catch (error) {
      this.sendError(connection.id, 'INVALID_JSON', 'Message must be valid JSON');
      return;
    }

    // Validate message structure
    if (!message.type) {
      this.sendError(connection.id, 'MISSING_TYPE', 'Message must have a type field');
      return;
    }

    // Handle different message types
    switch (message.type) {
      case 'authenticate':
        await this.handleAuthentication(connection, message);
        break;
      case 'subscribe':
        await this.handleSubscription(connection, message);
        break;
      case 'unsubscribe':
        await this.handleUnsubscription(connection, message);
        break;
      case 'ping':
        this.sendToConnection(connection.id, { type: 'pong', timestamp: Date.now() });
        break;
      case 'broadcast':
        await this.handleBroadcast(connection, message);
        break;
      default:
        this.emit('message', { connection, message });
    }

    // Update latency metrics
    const latency = performance.now() - startTime;
    this.updateLatencyMetrics(latency);
  }

  async handleAuthentication(connection, message) {
    try {
      // Validate authentication token
      const { token, userId } = message.payload || {};
      
      if (!token) {
        this.sendError(connection.id, 'MISSING_TOKEN', 'Authentication token required');
        return;
      }

      // Here you would validate the token with your auth service
      // For now, we'll simulate validation
      const isValid = await this.validateAuthToken(token);
      
      if (isValid) {
        connection.authenticated = true;
        connection.userId = userId;
        
        this.sendToConnection(connection.id, {
          type: 'authenticated',
          userId,
          timestamp: Date.now()
        });

        console.log(`🔐 Connection ${connection.id} authenticated for user ${userId}`);
      } else {
        this.sendError(connection.id, 'INVALID_TOKEN', 'Authentication failed');
      }
    } catch (error) {
      console.error('Authentication error:', error);
      this.sendError(connection.id, 'AUTH_ERROR', 'Authentication service error');
    }
  }

  async handleSubscription(connection, message) {
    const { channel, filters } = message.payload || {};
    
    if (!channel) {
      this.sendError(connection.id, 'MISSING_CHANNEL', 'Channel name required for subscription');
      return;
    }

    // Check if user has permission to subscribe to this channel
    if (!await this.checkSubscriptionPermission(connection, channel)) {
      this.sendError(connection.id, 'PERMISSION_DENIED', `No permission to subscribe to ${channel}`);
      return;
    }

    const subscription = {
      channel,
      filters: filters || {},
      subscribedAt: Date.now()
    };

    connection.subscriptions.add(JSON.stringify(subscription));

    // Add to Redis for clustering
    if (this.redis) {
      await this.redis.sadd(`subscriptions:${channel}`, connection.id);
    }

    this.sendToConnection(connection.id, {
      type: 'subscribed',
      channel,
      timestamp: Date.now()
    });

    console.log(`📡 Connection ${connection.id} subscribed to ${channel}`);
    this.emit('subscription', { connectionId: connection.id, channel, filters });
  }

  async handleUnsubscription(connection, message) {
    const { channel } = message.payload || {};
    
    if (!channel) {
      this.sendError(connection.id, 'MISSING_CHANNEL', 'Channel name required for unsubscription');
      return;
    }

    // Remove subscription
    const subscriptions = Array.from(connection.subscriptions);
    const subscription = subscriptions.find(sub => {
      const parsed = JSON.parse(sub);
      return parsed.channel === channel;
    });

    if (subscription) {
      connection.subscriptions.delete(subscription);
      
      // Remove from Redis
      if (this.redis) {
        await this.redis.srem(`subscriptions:${channel}`, connection.id);
      }

      this.sendToConnection(connection.id, {
        type: 'unsubscribed',
        channel,
        timestamp: Date.now()
      });

      console.log(`📡 Connection ${connection.id} unsubscribed from ${channel}`);
    }
  }

  async handleBroadcast(connection, message) {
    if (!connection.authenticated) {
      this.sendError(connection.id, 'NOT_AUTHENTICATED', 'Authentication required for broadcasting');
      return;
    }

    const { channel, data, targetUsers } = message.payload || {};
    
    if (!channel || !data) {
      this.sendError(connection.id, 'INVALID_BROADCAST', 'Channel and data required for broadcast');
      return;
    }

    await this.broadcast(channel, data, { 
      excludeConnection: connection.id,
      targetUsers 
    });
  }

  generateConnectionId() {
    return createHash('sha256')
      .update(`${Date.now()}-${Math.random()}-${process.pid}`)
      .digest('hex')
      .substring(0, 16);
  }

  extractClientInfo(request) {
    const forwarded = request.headers['x-forwarded-for'];
    const ip = forwarded ? forwarded.split(',')[0] : request.socket.remoteAddress;
    
    return {
      ip: ip?.replace('::ffff:', '') || 'unknown',
      userAgent: request.headers['user-agent'] || 'unknown',
      origin: request.headers.origin || 'unknown',
      protocol: request.headers['sec-websocket-protocol'] || 'unknown'
    };
  }

  checkRateLimit(ip) {
    const now = Date.now();
    const windowMs = this.options.rateLimit.windowMs;
    const maxRequests = this.options.rateLimit.max;
    
    if (!this.rateLimiters.has(ip)) {
      this.rateLimiters.set(ip, { count: 1, resetTime: now + windowMs });
      return true;
    }

    const limiter = this.rateLimiters.get(ip);
    
    if (now > limiter.resetTime) {
      limiter.count = 1;
      limiter.resetTime = now + windowMs;
      return true;
    }

    if (limiter.count >= maxRequests) {
      return false;
    }

    limiter.count++;
    return true;
  }

  startHeartbeat(connection) {
    const interval = setInterval(() => {
      if (connection.ws.readyState === connection.ws.OPEN) {
        const timeSinceLastActivity = Date.now() - connection.lastActivity;
        
        if (timeSinceLastActivity > this.options.connectionTimeout) {
          console.log(`⏰ Connection ${connection.id} timed out`);
          connection.ws.close(1000, 'Connection timeout');
          clearInterval(interval);
        } else {
          connection.ws.ping();
        }
      } else {
        clearInterval(interval);
      }
    }, this.options.heartbeatInterval);

    connection.heartbeatInterval = interval;
  }

  sendToConnection(connectionId, message) {
    const connection = this.connections.get(connectionId);
    if (!connection || connection.ws.readyState !== connection.ws.OPEN) {
      return false;
    }

    try {
      const data = JSON.stringify(message);
      connection.ws.send(data);
      connection.bytesSent += data.length;
      return true;
    } catch (error) {
      console.error(`Failed to send message to ${connectionId}:`, error);
      return false;
    }
  }

  sendError(connectionId, code, message) {
    this.sendToConnection(connectionId, {
      type: 'error',
      error: { code, message },
      timestamp: Date.now()
    });
  }

  async broadcast(channel, data, options = {}) {
    const message = {
      type: 'broadcast',
      channel,
      data,
      timestamp: Date.now()
    };

    let targetConnections = [];

    if (this.redis) {
      // Get subscribers from Redis for clustering
      const subscribers = await this.redis.smembers(`subscriptions:${channel}`);
      targetConnections = subscribers
        .map(id => this.connections.get(id))
        .filter(conn => conn && conn.ws.readyState === conn.ws.OPEN);
    } else {
      // Local connections only
      targetConnections = Array.from(this.connections.values())
        .filter(conn => {
          if (conn.ws.readyState !== conn.ws.OPEN) return false;
          if (options.excludeConnection === conn.id) return false;
          
          // Check if connection is subscribed to channel
          const subscriptions = Array.from(conn.subscriptions);
          return subscriptions.some(sub => {
            const parsed = JSON.parse(sub);
            return parsed.channel === channel;
          });
        });
    }

    // Filter by target users if specified
    if (options.targetUsers && options.targetUsers.length > 0) {
      targetConnections = targetConnections.filter(conn => 
        options.targetUsers.includes(conn.userId)
      );
    }

    // Send to all target connections
    let successCount = 0;
    for (const connection of targetConnections) {
      if (this.sendToConnection(connection.id, message)) {
        successCount++;
      }
    }

    console.log(`📢 Broadcast to ${channel}: ${successCount}/${targetConnections.length} delivered`);
    return { sent: successCount, total: targetConnections.length };
  }

  handleDisconnection(connectionId, code, reason) {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    // Clear heartbeat interval
    if (connection.heartbeatInterval) {
      clearInterval(connection.heartbeatInterval);
    }

    // Remove subscriptions from Redis
    if (this.redis) {
      connection.subscriptions.forEach(async (sub) => {
        const parsed = JSON.parse(sub);
        await this.redis.srem(`subscriptions:${parsed.channel}`, connectionId);
      });
    }

    // Remove connection
    this.connections.delete(connectionId);
    this.metrics.activeConnections--;

    console.log(`🔌 Connection ${connectionId} disconnected: ${code} ${reason}`);
    this.emit('disconnection', { connectionId, code, reason, connection });
  }

  async validateAuthToken(token) {
    // Implement your token validation logic here
    // This could involve JWT verification, database lookup, etc.
    return token && token.length > 10; // Simplified validation
  }

  async checkSubscriptionPermission(connection, channel) {
    // Implement your permission checking logic here
    // This could involve role-based access control, etc.
    return connection.authenticated; // Simplified check
  }

  startMetricsCollection() {
    setInterval(() => {
      this.updateMetrics();
    }, 5000); // Update metrics every 5 seconds
  }

  updateMetrics() {
    const now = Date.now();
    const timeDiff = now - this.metrics.lastReset;
    
    // Calculate messages per second
    const totalMessages = Array.from(this.connections.values())
      .reduce((sum, conn) => sum + conn.messageCount, 0);
    
    this.metrics.messagesPerSecond = totalMessages / (timeDiff / 1000);
    this.metrics.lastReset = now;

    // Reset message counts
    this.connections.forEach(conn => {
      conn.messageCount = 0;
    });

    this.emit('metrics', this.metrics);
  }

  updateLatencyMetrics(latency) {
    // Simple moving average for latency
    this.metrics.averageLatency = (this.metrics.averageLatency * 0.9) + (latency * 0.1);
  }

  startHealthCheck() {
    setInterval(() => {
      this.performHealthCheck();
    }, 30000); // Health check every 30 seconds
  }

  performHealthCheck() {
    const healthStatus = {
      status: 'healthy',
      timestamp: Date.now(),
      connections: this.metrics.activeConnections,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      redis: this.redis ? 'connected' : 'disconnected'
    };

    // Check for unhealthy conditions
    if (this.metrics.activeConnections > this.options.maxConnections * 0.9) {
      healthStatus.status = 'warning';
      healthStatus.warning = 'High connection count';
    }

    if (this.metrics.errorRate > 0.1) {
      healthStatus.status = 'unhealthy';
      healthStatus.error = 'High error rate';
    }

    this.emit('health', healthStatus);
  }

  getMetrics() {
    return {
      ...this.metrics,
      connections: this.connections.size,
      uptime: process.uptime(),
      memory: process.memoryUsage()
    };
  }

  getConnectionInfo(connectionId) {
    const connection = this.connections.get(connectionId);
    if (!connection) return null;

    return {
      id: connection.id,
      clientInfo: connection.clientInfo,
      connectedAt: connection.connectedAt,
      lastActivity: connection.lastActivity,
      authenticated: connection.authenticated,
      userId: connection.userId,
      subscriptions: Array.from(connection.subscriptions).map(sub => JSON.parse(sub)),
      messageCount: connection.messageCount,
      bytesReceived: connection.bytesReceived,
      bytesSent: connection.bytesSent
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down WebSocket Manager...');
    
    // Close all connections gracefully
    for (const [connectionId, connection] of this.connections) {
      if (connection.ws.readyState === connection.ws.OPEN) {
        connection.ws.close(1001, 'Server shutting down');
      }
    }

    // Close server
    if (this.server) {
      this.server.close();
    }

    // Close Redis connection
    if (this.redis) {
      await this.redis.quit();
    }

    console.log('✅ WebSocket Manager shutdown complete');
  }
}

export default WebSocketManager;
