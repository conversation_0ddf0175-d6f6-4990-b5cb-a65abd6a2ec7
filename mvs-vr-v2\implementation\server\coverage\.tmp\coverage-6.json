{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1753", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/cross-region-backup-replication.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 80782, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 80782, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 944, "endOffset": 14972, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1754", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/services/backup/enhanced-verification-service.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19096, "count": 1}, {"startOffset": 874, "endOffset": 888, "count": 0}, {"startOffset": 938, "endOffset": 952, "count": 0}, {"startOffset": 1028, "endOffset": 1050, "count": 0}, {"startOffset": 1101, "endOffset": 1126, "count": 0}, {"startOffset": 1195, "endOffset": 1219, "count": 0}, {"startOffset": 1273, "endOffset": 1300, "count": 0}, {"startOffset": 1371, "endOffset": 1397, "count": 0}, {"startOffset": 1452, "endOffset": 1481, "count": 0}, {"startOffset": 1577, "endOffset": 1582, "count": 0}], "isBlockCoverage": true}, {"functionName": "EnhancedVerificationService", "ranges": [{"startOffset": 2106, "endOffset": 2342, "count": 0}], "isBlockCoverage": false}, {"functionName": "initialize", "ranges": [{"startOffset": 2399, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}, {"functionName": "ensureDirectory", "ranges": [{"startOffset": 2680, "endOffset": 2815, "count": 0}], "isBlockCoverage": false}, {"functionName": "runComprehensiveVerification", "ranges": [{"startOffset": 2888, "endOffset": 4750, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyBucketType", "ranges": [{"startOffset": 4801, "endOffset": 5599, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyReplication", "ranges": [{"startOffset": 5684, "endOffset": 7770, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyIntegrity", "ranges": [{"startOffset": 7843, "endOffset": 10073, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyObjectIntegrity", "ranges": [{"startOffset": 10156, "endOffset": 11667, "count": 0}], "isBlockCoverage": false}, {"functionName": "performChecksumVerification", "ranges": [{"startOffset": 11753, "endOffset": 13233, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateFileChecksum", "ranges": [{"startOffset": 13278, "endOffset": 13515, "count": 0}], "isBlockCoverage": false}, {"functionName": "downloadObject", "ranges": [{"startOffset": 13560, "endOffset": 13940, "count": 0}], "isBlockCoverage": false}, {"functionName": "getObjectMetadata", "ranges": [{"startOffset": 13981, "endOffset": 14143, "count": 0}], "isBlockCoverage": false}, {"functionName": "listBucketObjects", "ranges": [{"startOffset": 14187, "endOffset": 14703, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyMetadata", "ranges": [{"startOffset": 14752, "endOffset": 15126, "count": 0}], "isBlockCoverage": false}, {"functionName": "determineBucketStatus", "ranges": [{"startOffset": 15201, "endOffset": 15742, "count": 0}], "isBlockCoverage": false}, {"functionName": "calculateOverallResults", "ranges": [{"startOffset": 15802, "endOffset": 17349, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateRecommendations", "ranges": [{"startOffset": 17425, "endOffset": 18449, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveVerificationReport", "ranges": [{"startOffset": 18495, "endOffset": 19030, "count": 0}], "isBlockCoverage": false}]}]}