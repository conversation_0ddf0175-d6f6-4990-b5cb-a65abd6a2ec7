/**
 * Comprehensive Audit Logger
 * 
 * This middleware provides comprehensive audit logging for sensitive data access,
 * security events, and compliance requirements.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { promisify } = require('util');

const writeFileAsync = promisify(fs.writeFile);
const appendFileAsync = promisify(fs.appendFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

const logger = require('../utils/logger').getLogger('comprehensive-audit-logger');

// Audit event types
const AUDIT_EVENT_TYPES = {
  DATA_ACCESS: 'data_access',
  SENSITIVE_DATA_ACCESS: 'sensitive_data_access',
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  DATA_MODIFICATION: 'data_modification',
  SECURITY_VIOLATION: 'security_violation',
  SYSTEM_EVENT: 'system_event',
  COMPLIANCE_EVENT: 'compliance_event'
};

// Audit severity levels
const AUDIT_SEVERITY = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
};

// Compliance frameworks
const COMPLIANCE_FRAMEWORKS = {
  GDPR: 'gdpr',
  HIPAA: 'hipaa',
  SOX: 'sox',
  PCI_DSS: 'pci_dss',
  ISO27001: 'iso27001'
};

// Audit configuration
const AUDIT_CONFIG = {
  enabled: process.env.AUDIT_LOGGING_ENABLED !== 'false',
  logPath: process.env.AUDIT_LOG_PATH || path.join(__dirname, '../logs/audit'),
  retentionDays: parseInt(process.env.AUDIT_RETENTION_DAYS || '2555', 10), // 7 years default
  encryptLogs: process.env.AUDIT_ENCRYPT_LOGS === 'true',
  complianceMode: process.env.AUDIT_COMPLIANCE_MODE || 'standard',
  realTimeAlerts: process.env.AUDIT_REAL_TIME_ALERTS === 'true'
};

// Audit statistics
let auditStats = {
  totalEvents: 0,
  eventsByType: {},
  eventsBySeverity: {},
  lastEvent: null,
  startTime: Date.now()
};

/**
 * Initialize audit logging system
 */
async function initializeAuditLogging() {
  try {
    if (!await existsAsync(AUDIT_CONFIG.logPath)) {
      await mkdirAsync(AUDIT_CONFIG.logPath, { recursive: true });
    }
    
    // Create audit log structure
    const logStructure = {
      events: path.join(AUDIT_CONFIG.logPath, 'events'),
      security: path.join(AUDIT_CONFIG.logPath, 'security'),
      compliance: path.join(AUDIT_CONFIG.logPath, 'compliance'),
      access: path.join(AUDIT_CONFIG.logPath, 'access')
    };
    
    for (const dir of Object.values(logStructure)) {
      if (!await existsAsync(dir)) {
        await mkdirAsync(dir, { recursive: true });
      }
    }
    
    logger.info('Comprehensive audit logging system initialized');
  } catch (error) {
    logger.error('Failed to initialize audit logging system:', error);
  }
}

/**
 * Generate audit event
 * @param {Object} eventData - Event data
 * @returns {Object} Audit event
 */
function generateAuditEvent(eventData) {
  const event = {
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    type: eventData.type || AUDIT_EVENT_TYPES.SYSTEM_EVENT,
    severity: eventData.severity || AUDIT_SEVERITY.INFO,
    source: eventData.source || 'api',
    user: {
      id: eventData.user?.id || 'anonymous',
      role: eventData.user?.role || 'unknown',
      ip: eventData.ip || 'unknown',
      userAgent: eventData.userAgent || 'unknown'
    },
    request: {
      method: eventData.method || 'unknown',
      path: eventData.path || 'unknown',
      query: eventData.query || {},
      headers: eventData.headers || {}
    },
    response: {
      statusCode: eventData.statusCode || 0,
      size: eventData.responseSize || 0
    },
    data: {
      action: eventData.action || 'unknown',
      resource: eventData.resource || 'unknown',
      resourceId: eventData.resourceId || null,
      sensitiveFields: eventData.sensitiveFields || [],
      dataClassification: eventData.dataClassification || 'public'
    },
    security: {
      riskLevel: eventData.riskLevel || 'low',
      threatIndicators: eventData.threatIndicators || [],
      complianceFrameworks: eventData.complianceFrameworks || []
    },
    metadata: {
      sessionId: eventData.sessionId || null,
      correlationId: eventData.correlationId || null,
      environment: process.env.NODE_ENV || 'development',
      version: eventData.version || '1.0.0'
    }
  };
  
  // Add compliance-specific fields
  if (AUDIT_CONFIG.complianceMode !== 'standard') {
    event.compliance = {
      framework: AUDIT_CONFIG.complianceMode,
      requirements: getComplianceRequirements(event),
      retention: calculateRetentionPeriod(event)
    };
  }
  
  return event;
}

/**
 * Get compliance requirements for event
 * @param {Object} event - Audit event
 * @returns {Array} Compliance requirements
 */
function getComplianceRequirements(event) {
  const requirements = [];
  
  // GDPR requirements
  if (event.data.sensitiveFields.some(field => 
    field.includes('email') || field.includes('personal') || field.includes('address'))) {
    requirements.push({
      framework: COMPLIANCE_FRAMEWORKS.GDPR,
      article: 'Article 30',
      description: 'Records of processing activities'
    });
  }
  
  // HIPAA requirements
  if (event.data.sensitiveFields.some(field => 
    field.includes('medical') || field.includes('health'))) {
    requirements.push({
      framework: COMPLIANCE_FRAMEWORKS.HIPAA,
      section: '164.312',
      description: 'Access control and audit controls'
    });
  }
  
  // PCI DSS requirements
  if (event.data.sensitiveFields.some(field => 
    field.includes('credit') || field.includes('payment'))) {
    requirements.push({
      framework: COMPLIANCE_FRAMEWORKS.PCI_DSS,
      requirement: '10.2',
      description: 'Audit trail for access to cardholder data'
    });
  }
  
  return requirements;
}

/**
 * Calculate retention period for event
 * @param {Object} event - Audit event
 * @returns {Object} Retention information
 */
function calculateRetentionPeriod(event) {
  let retentionDays = AUDIT_CONFIG.retentionDays;
  let reason = 'Standard retention policy';
  
  // Extend retention for high-risk events
  if (event.severity === AUDIT_SEVERITY.CRITICAL) {
    retentionDays = Math.max(retentionDays, 3650); // 10 years
    reason = 'Critical security event';
  }
  
  // Compliance-specific retention
  if (event.security.complianceFrameworks.includes(COMPLIANCE_FRAMEWORKS.SOX)) {
    retentionDays = Math.max(retentionDays, 2555); // 7 years
    reason = 'SOX compliance requirement';
  }
  
  return {
    days: retentionDays,
    expiresAt: new Date(Date.now() + (retentionDays * 24 * 60 * 60 * 1000)).toISOString(),
    reason
  };
}

/**
 * Write audit event to log
 * @param {Object} event - Audit event
 */
async function writeAuditEvent(event) {
  try {
    const dateStr = new Date().toISOString().split('T')[0];
    const logFile = path.join(AUDIT_CONFIG.logPath, 'events', `audit-${dateStr}.jsonl`);
    
    let logEntry = JSON.stringify(event);
    
    // Encrypt log entry if required
    if (AUDIT_CONFIG.encryptLogs) {
      logEntry = encryptLogEntry(logEntry);
    }
    
    await appendFileAsync(logFile, logEntry + '\n');
    
    // Write to specialized logs
    await writeSpecializedLogs(event);
    
    // Update statistics
    updateAuditStats(event);
    
    // Trigger real-time alerts if enabled
    if (AUDIT_CONFIG.realTimeAlerts && shouldTriggerAlert(event)) {
      await triggerRealTimeAlert(event);
    }
    
  } catch (error) {
    logger.error('Failed to write audit event:', error);
  }
}

/**
 * Write specialized audit logs
 * @param {Object} event - Audit event
 */
async function writeSpecializedLogs(event) {
  const dateStr = new Date().toISOString().split('T')[0];
  
  // Security events log
  if (event.type === AUDIT_EVENT_TYPES.SECURITY_VIOLATION || 
      event.severity === AUDIT_SEVERITY.CRITICAL) {
    const securityLogFile = path.join(AUDIT_CONFIG.logPath, 'security', `security-${dateStr}.jsonl`);
    await appendFileAsync(securityLogFile, JSON.stringify(event) + '\n');
  }
  
  // Compliance events log
  if (event.compliance || event.type === AUDIT_EVENT_TYPES.COMPLIANCE_EVENT) {
    const complianceLogFile = path.join(AUDIT_CONFIG.logPath, 'compliance', `compliance-${dateStr}.jsonl`);
    await appendFileAsync(complianceLogFile, JSON.stringify(event) + '\n');
  }
  
  // Access events log
  if (event.type === AUDIT_EVENT_TYPES.DATA_ACCESS || 
      event.type === AUDIT_EVENT_TYPES.SENSITIVE_DATA_ACCESS) {
    const accessLogFile = path.join(AUDIT_CONFIG.logPath, 'access', `access-${dateStr}.jsonl`);
    await appendFileAsync(accessLogFile, JSON.stringify(event) + '\n');
  }
}

/**
 * Encrypt log entry
 * @param {string} logEntry - Log entry to encrypt
 * @returns {string} Encrypted log entry
 */
function encryptLogEntry(logEntry) {
  const algorithm = 'aes-256-gcm';
  const key = crypto.scryptSync(process.env.AUDIT_ENCRYPTION_KEY || 'default-key', 'salt', 32);
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, key);
  cipher.setAAD(Buffer.from('audit-log'));
  
  let encrypted = cipher.update(logEntry, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  
  return JSON.stringify({
    encrypted: true,
    algorithm,
    iv: iv.toString('hex'),
    authTag: authTag.toString('hex'),
    data: encrypted
  });
}

/**
 * Update audit statistics
 * @param {Object} event - Audit event
 */
function updateAuditStats(event) {
  auditStats.totalEvents++;
  auditStats.lastEvent = event.timestamp;
  
  // Update by type
  if (!auditStats.eventsByType[event.type]) {
    auditStats.eventsByType[event.type] = 0;
  }
  auditStats.eventsByType[event.type]++;
  
  // Update by severity
  if (!auditStats.eventsBySeverity[event.severity]) {
    auditStats.eventsBySeverity[event.severity] = 0;
  }
  auditStats.eventsBySeverity[event.severity]++;
}

/**
 * Check if event should trigger alert
 * @param {Object} event - Audit event
 * @returns {boolean} Whether to trigger alert
 */
function shouldTriggerAlert(event) {
  return event.severity === AUDIT_SEVERITY.CRITICAL ||
         event.type === AUDIT_EVENT_TYPES.SECURITY_VIOLATION ||
         event.security.riskLevel === 'high';
}

/**
 * Trigger real-time alert
 * @param {Object} event - Audit event
 */
async function triggerRealTimeAlert(event) {
  try {
    logger.error('AUDIT ALERT: Critical security event detected', {
      eventId: event.id,
      type: event.type,
      severity: event.severity,
      user: event.user.id,
      action: event.data.action
    });
    
    // TODO: Integrate with alerting systems (email, Slack, PagerDuty, etc.)
    
  } catch (error) {
    logger.error('Failed to trigger real-time alert:', error);
  }
}

/**
 * Comprehensive Audit Logger middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function comprehensiveAuditLogger(options = {}) {
  const {
    enabled = AUDIT_CONFIG.enabled,
    logLevel = 'all', // 'all', 'sensitive_only', 'security_only'
    includeRequestBody = false,
    includeResponseBody = false,
    maxBodySize = 1024
  } = options;
  
  // Initialize audit logging
  initializeAuditLogging();
  
  return (req, res, next) => {
    if (!enabled) {
      return next();
    }
    
    const startTime = Date.now();
    
    // Store original res.json method
    const originalJson = res.json;
    
    // Override res.json method
    res.json = function(data) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      try {
        // Determine if this request should be audited
        const shouldAudit = shouldAuditRequest(req, res, logLevel);
        
        if (shouldAudit) {
          // Generate audit event
          const eventData = {
            type: determineEventType(req, res),
            severity: determineSeverity(req, res),
            user: req.user,
            ip: req.ip || req.connection?.remoteAddress,
            userAgent: req.get('User-Agent'),
            method: req.method,
            path: req.path,
            query: req.query,
            headers: sanitizeHeaders(req.headers),
            statusCode: res.statusCode,
            responseSize: JSON.stringify(data).length,
            action: `${req.method} ${req.path}`,
            resource: extractResourceFromPath(req.path),
            resourceId: extractResourceId(req.path),
            sensitiveFields: extractSensitiveFields(data),
            dataClassification: determineDataClassification(data),
            riskLevel: assessRiskLevel(req, res, data),
            threatIndicators: detectThreatIndicators(req, res),
            complianceFrameworks: determineComplianceFrameworks(data),
            sessionId: req.sessionID,
            correlationId: req.headers['x-correlation-id'],
            duration
          };
          
          // Include request/response bodies if configured
          if (includeRequestBody && req.body) {
            eventData.requestBody = truncateBody(req.body, maxBodySize);
          }
          
          if (includeResponseBody) {
            eventData.responseBody = truncateBody(data, maxBodySize);
          }
          
          const auditEvent = generateAuditEvent(eventData);
          writeAuditEvent(auditEvent);
        }
        
      } catch (error) {
        logger.error('Error in comprehensive audit logging:', error);
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };
    
    next();
  };
}

/**
 * Helper functions for audit event generation
 */

function shouldAuditRequest(req, res, logLevel) {
  if (logLevel === 'all') return true;
  if (logLevel === 'sensitive_only') return containsSensitiveData(req, res);
  if (logLevel === 'security_only') return isSecurityEvent(req, res);
  return false;
}

function determineEventType(req, res) {
  if (req.path.includes('/auth')) return AUDIT_EVENT_TYPES.AUTHENTICATION;
  if (res.statusCode === 403) return AUDIT_EVENT_TYPES.AUTHORIZATION;
  if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) return AUDIT_EVENT_TYPES.DATA_MODIFICATION;
  return AUDIT_EVENT_TYPES.DATA_ACCESS;
}

function determineSeverity(req, res) {
  if (res.statusCode >= 500) return AUDIT_SEVERITY.ERROR;
  if (res.statusCode === 403 || res.statusCode === 401) return AUDIT_SEVERITY.WARNING;
  if (res.statusCode >= 400) return AUDIT_SEVERITY.WARNING;
  return AUDIT_SEVERITY.INFO;
}

function sanitizeHeaders(headers) {
  const sanitized = { ...headers };
  delete sanitized.authorization;
  delete sanitized.cookie;
  delete sanitized['x-api-key'];
  return sanitized;
}

function extractResourceFromPath(path) {
  const parts = path.split('/').filter(Boolean);
  return parts[1] || 'unknown';
}

function extractResourceId(path) {
  const match = path.match(/\/([a-f\d-]{36}|\d+)(?:\/|$)/i);
  return match ? match[1] : null;
}

function extractSensitiveFields(data) {
  // This would integrate with the response sanitization middleware
  // to get the list of sensitive fields that were detected
  return [];
}

function determineDataClassification(data) {
  // Determine data classification based on content
  return 'internal';
}

function assessRiskLevel(req, res, data) {
  if (res.statusCode === 403) return 'high';
  if (res.statusCode >= 400) return 'medium';
  return 'low';
}

function detectThreatIndicators(req, res) {
  const indicators = [];
  
  if (res.statusCode === 429) indicators.push('rate_limit_exceeded');
  if (res.statusCode === 403) indicators.push('unauthorized_access_attempt');
  
  return indicators;
}

function determineComplianceFrameworks(data) {
  // Determine applicable compliance frameworks based on data content
  return [];
}

function containsSensitiveData(req, res) {
  // Check if request/response contains sensitive data
  return false;
}

function isSecurityEvent(req, res) {
  return res.statusCode === 401 || res.statusCode === 403 || res.statusCode === 429;
}

function truncateBody(body, maxSize) {
  const str = JSON.stringify(body);
  return str.length > maxSize ? str.substring(0, maxSize) + '...' : str;
}

/**
 * Get audit statistics
 * @returns {Object} Audit statistics
 */
function getAuditStats() {
  return {
    ...auditStats,
    uptime: Date.now() - auditStats.startTime
  };
}

module.exports = {
  comprehensiveAuditLogger,
  getAuditStats,
  AUDIT_EVENT_TYPES,
  AUDIT_SEVERITY,
  COMPLIANCE_FRAMEWORKS
};
