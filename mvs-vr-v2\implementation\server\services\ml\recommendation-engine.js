/**
 * ML-Powered Recommendation Engine
 * Advanced recommendation system using collaborative filtering, content-based filtering, and hybrid approaches
 */

import * as tf from '@tensorflow/tfjs-node';
import { EventEmitter } from 'events';
import { Matrix } from 'ml-matrix';

export class RecommendationEngine extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enableCollaborativeFiltering: options.enableCollaborativeFiltering !== false,
      enableContentBasedFiltering: options.enableContentBasedFiltering !== false,
      enableHybridRecommendations: options.enableHybridRecommendations !== false,
      enableDeepLearning: options.enableDeepLearning !== false,
      enableRealTimeRecommendations: options.enableRealTimeRecommendations !== false,
      maxRecommendations: options.maxRecommendations || 10,
      minRating: options.minRating || 1,
      maxRating: options.maxRating || 5,
      similarityThreshold: options.similarityThreshold || 0.5,
      updateFrequency: options.updateFrequency || 3600000, // 1 hour
      enableExplainability: options.enableExplainability !== false,
      enableDiversification: options.enableDiversification !== false,
      ...options
    };

    // Recommendation models
    this.models = {
      collaborativeFiltering: {
        matrixFactorization: null,
        neuralCollaborativeFiltering: null,
        autoencoder: null
      },
      contentBased: {
        contentSimilarity: null,
        deepContent: null,
        textEmbedding: null
      },
      hybrid: {
        weightedHybrid: null,
        switchingHybrid: null,
        mixedHybrid: null
      },
      deepLearning: {
        wideAndDeep: null,
        deepFM: null,
        neuralFactorizationMachine: null
      }
    };

    // Data storage
    this.userItemMatrix = null;
    this.itemFeatures = new Map();
    this.userFeatures = new Map();
    this.interactions = new Map();
    this.itemSimilarity = new Map();
    this.userSimilarity = new Map();
    
    // Embeddings
    this.userEmbeddings = new Map();
    this.itemEmbeddings = new Map();
    this.contentEmbeddings = new Map();
    
    // Recommendations cache
    this.recommendationsCache = new Map();
    this.explanations = new Map();
    
    // Performance metrics
    this.metrics = {
      precision: new Map(),
      recall: new Map(),
      f1Score: new Map(),
      diversity: new Map(),
      coverage: new Map(),
      novelty: new Map()
    };

    this.initialize();
  }

  async initialize() {
    try {
      console.log('🎯 Initializing ML Recommendation Engine...');
      
      // Initialize TensorFlow
      await tf.ready();
      
      // Initialize models
      await this.initializeModels();
      
      // Setup similarity computation
      this.setupSimilarityComputation();
      
      // Start real-time processing
      if (this.options.enableRealTimeRecommendations) {
        this.startRealTimeProcessing();
      }
      
      // Setup recommendation updates
      this.setupRecommendationUpdates();
      
      console.log('✅ ML Recommendation Engine initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize ML Recommendation Engine:', error);
      this.emit('error', error);
    }
  }

  async initializeModels() {
    // Collaborative Filtering Models
    if (this.options.enableCollaborativeFiltering) {
      this.models.collaborativeFiltering.matrixFactorization = this.createMatrixFactorizationModel();
      this.models.collaborativeFiltering.neuralCollaborativeFiltering = this.createNeuralCFModel();
      this.models.collaborativeFiltering.autoencoder = this.createAutoencoderModel();
    }

    // Content-Based Models
    if (this.options.enableContentBasedFiltering) {
      this.models.contentBased.contentSimilarity = this.createContentSimilarityModel();
      this.models.contentBased.deepContent = this.createDeepContentModel();
      this.models.contentBased.textEmbedding = this.createTextEmbeddingModel();
    }

    // Deep Learning Models
    if (this.options.enableDeepLearning) {
      this.models.deepLearning.wideAndDeep = this.createWideAndDeepModel();
      this.models.deepLearning.deepFM = this.createDeepFMModel();
      this.models.deepLearning.neuralFactorizationMachine = this.createNFMModel();
    }

    // Hybrid Models
    if (this.options.enableHybridRecommendations) {
      this.models.hybrid.weightedHybrid = this.createWeightedHybridModel();
      this.models.hybrid.switchingHybrid = this.createSwitchingHybridModel();
    }
  }

  createMatrixFactorizationModel() {
    // Neural Matrix Factorization
    const userInput = tf.input({ shape: [1], name: 'user_input' });
    const itemInput = tf.input({ shape: [1], name: 'item_input' });
    
    const userEmbedding = tf.layers.embedding({
      inputDim: 10000, // max users
      outputDim: 50,
      name: 'user_embedding'
    }).apply(userInput);
    
    const itemEmbedding = tf.layers.embedding({
      inputDim: 10000, // max items
      outputDim: 50,
      name: 'item_embedding'
    }).apply(itemInput);
    
    const userVec = tf.layers.flatten().apply(userEmbedding);
    const itemVec = tf.layers.flatten().apply(itemEmbedding);
    
    const dotProduct = tf.layers.dot({ axes: 1 }).apply([userVec, itemVec]);
    const output = tf.layers.dense({ units: 1, activation: 'sigmoid' }).apply(dotProduct);
    
    const model = tf.model({ inputs: [userInput, itemInput], outputs: output });
    
    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['meanAbsoluteError']
    });
    
    return model;
  }

  createNeuralCFModel() {
    // Neural Collaborative Filtering
    const userInput = tf.input({ shape: [1], name: 'user_input' });
    const itemInput = tf.input({ shape: [1], name: 'item_input' });
    
    // GMF (Generalized Matrix Factorization) path
    const userEmbeddingGMF = tf.layers.embedding({
      inputDim: 10000,
      outputDim: 32,
      name: 'user_embedding_gmf'
    }).apply(userInput);
    
    const itemEmbeddingGMF = tf.layers.embedding({
      inputDim: 10000,
      outputDim: 32,
      name: 'item_embedding_gmf'
    }).apply(itemInput);
    
    const userVecGMF = tf.layers.flatten().apply(userEmbeddingGMF);
    const itemVecGMF = tf.layers.flatten().apply(itemEmbeddingGMF);
    
    const gmfVector = tf.layers.multiply().apply([userVecGMF, itemVecGMF]);
    
    // MLP (Multi-Layer Perceptron) path
    const userEmbeddingMLP = tf.layers.embedding({
      inputDim: 10000,
      outputDim: 32,
      name: 'user_embedding_mlp'
    }).apply(userInput);
    
    const itemEmbeddingMLP = tf.layers.embedding({
      inputDim: 10000,
      outputDim: 32,
      name: 'item_embedding_mlp'
    }).apply(itemInput);
    
    const userVecMLP = tf.layers.flatten().apply(userEmbeddingMLP);
    const itemVecMLP = tf.layers.flatten().apply(itemEmbeddingMLP);
    
    const mlpVector = tf.layers.concatenate().apply([userVecMLP, itemVecMLP]);
    
    let mlpLayers = mlpVector;
    const mlpUnits = [128, 64, 32];
    
    for (const units of mlpUnits) {
      mlpLayers = tf.layers.dense({
        units,
        activation: 'relu',
        kernelRegularizer: tf.regularizers.l2({ l2: 0.01 })
      }).apply(mlpLayers);
      mlpLayers = tf.layers.dropout({ rate: 0.2 }).apply(mlpLayers);
    }
    
    // Combine GMF and MLP
    const combined = tf.layers.concatenate().apply([gmfVector, mlpLayers]);
    const output = tf.layers.dense({ units: 1, activation: 'sigmoid' }).apply(combined);
    
    const model = tf.model({ inputs: [userInput, itemInput], outputs: output });
    
    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });
    
    return model;
  }

  createDeepContentModel() {
    // Deep content-based model
    const model = tf.sequential({
      layers: [
        tf.layers.dense({ inputShape: [100], units: 256, activation: 'relu' }),
        tf.layers.batchNormalization(),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({ units: 128, activation: 'relu' }),
        tf.layers.batchNormalization(),
        tf.layers.dropout({ rate: 0.3 }),
        tf.layers.dense({ units: 64, activation: 'relu' }),
        tf.layers.dropout({ rate: 0.2 }),
        tf.layers.dense({ units: 32, activation: 'relu' }),
        tf.layers.dense({ units: 1, activation: 'sigmoid' })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy']
    });

    return model;
  }

  createWideAndDeepModel() {
    // Wide & Deep Learning model
    const wideInput = tf.input({ shape: [50], name: 'wide_input' });
    const deepInput = tf.input({ shape: [100], name: 'deep_input' });
    
    // Wide component (linear model)
    const wideOutput = tf.layers.dense({
      units: 1,
      activation: 'linear',
      name: 'wide_output'
    }).apply(wideInput);
    
    // Deep component (neural network)
    let deepLayers = deepInput;
    const deepUnits = [512, 256, 128, 64];
    
    for (const units of deepUnits) {
      deepLayers = tf.layers.dense({
        units,
        activation: 'relu',
        kernelRegularizer: tf.regularizers.l2({ l2: 0.01 })
      }).apply(deepLayers);
      deepLayers = tf.layers.dropout({ rate: 0.2 }).apply(deepLayers);
    }
    
    const deepOutput = tf.layers.dense({
      units: 1,
      activation: 'linear',
      name: 'deep_output'
    }).apply(deepLayers);
    
    // Combine wide and deep
    const combined = tf.layers.add().apply([wideOutput, deepOutput]);
    const finalOutput = tf.layers.activation({ activation: 'sigmoid' }).apply(combined);
    
    const model = tf.model({ inputs: [wideInput, deepInput], outputs: finalOutput });
    
    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'binaryCrossentropy',
      metrics: ['accuracy', 'auc']
    });
    
    return model;
  }

  /**
   * Generate recommendations for a user
   * @param {string} userId - User identifier
   * @param {Object} options - Recommendation options
   */
  async generateRecommendations(userId, options = {}) {
    try {
      const {
        count = this.options.maxRecommendations,
        method = 'hybrid',
        includeExplanations = this.options.enableExplainability,
        diversify = this.options.enableDiversification,
        excludeInteracted = true
      } = options;

      // Check cache first
      const cacheKey = `${userId}_${method}_${count}`;
      if (this.recommendationsCache.has(cacheKey)) {
        const cached = this.recommendationsCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 300000) { // 5 minutes
          return cached.recommendations;
        }
      }

      let recommendations = [];

      switch (method) {
        case 'collaborative':
          recommendations = await this.generateCollaborativeRecommendations(userId, count);
          break;
        case 'content':
          recommendations = await this.generateContentBasedRecommendations(userId, count);
          break;
        case 'deep':
          recommendations = await this.generateDeepLearningRecommendations(userId, count);
          break;
        case 'hybrid':
        default:
          recommendations = await this.generateHybridRecommendations(userId, count);
          break;
      }

      // Apply diversification
      if (diversify) {
        recommendations = this.diversifyRecommendations(recommendations);
      }

      // Exclude already interacted items
      if (excludeInteracted) {
        recommendations = this.filterInteractedItems(userId, recommendations);
      }

      // Add explanations
      if (includeExplanations) {
        recommendations = await this.addExplanations(userId, recommendations);
      }

      // Cache recommendations
      this.recommendationsCache.set(cacheKey, {
        recommendations,
        timestamp: Date.now()
      });

      this.emit('recommendationsGenerated', {
        userId,
        method,
        count: recommendations.length,
        timestamp: Date.now()
      });

      return recommendations;
    } catch (error) {
      console.error('Recommendation generation error:', error);
      throw error;
    }
  }

  async generateCollaborativeRecommendations(userId, count) {
    // Find similar users
    const similarUsers = await this.findSimilarUsers(userId);
    
    // Get items liked by similar users
    const candidateItems = new Map();
    
    for (const [similarUserId, similarity] of similarUsers) {
      const userInteractions = this.interactions.get(similarUserId) || [];
      
      for (const interaction of userInteractions) {
        if (interaction.rating >= 4) { // High rating threshold
          const currentScore = candidateItems.get(interaction.itemId) || 0;
          candidateItems.set(interaction.itemId, currentScore + similarity * interaction.rating);
        }
      }
    }
    
    // Sort by score and return top recommendations
    return Array.from(candidateItems.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([itemId, score]) => ({
        itemId,
        score,
        method: 'collaborative',
        confidence: Math.min(score / 5, 1) // Normalize to 0-1
      }));
  }

  async generateContentBasedRecommendations(userId, count) {
    // Get user's interaction history
    const userInteractions = this.interactions.get(userId) || [];
    const likedItems = userInteractions.filter(i => i.rating >= 4);
    
    if (likedItems.length === 0) {
      return this.generatePopularityBasedRecommendations(count);
    }
    
    // Calculate user profile based on liked items
    const userProfile = this.calculateUserProfile(likedItems);
    
    // Find similar items
    const candidateItems = new Map();
    
    for (const [itemId, features] of this.itemFeatures) {
      if (!likedItems.some(i => i.itemId === itemId)) {
        const similarity = this.calculateContentSimilarity(userProfile, features);
        if (similarity > this.options.similarityThreshold) {
          candidateItems.set(itemId, similarity);
        }
      }
    }
    
    return Array.from(candidateItems.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([itemId, score]) => ({
        itemId,
        score,
        method: 'content',
        confidence: score
      }));
  }

  async generateDeepLearningRecommendations(userId, count) {
    if (!this.models.deepLearning.wideAndDeep) {
      return this.generateCollaborativeRecommendations(userId, count);
    }

    const userFeatures = this.userFeatures.get(userId);
    if (!userFeatures) {
      return this.generatePopularityBasedRecommendations(count);
    }

    const recommendations = [];
    const candidateItems = Array.from(this.itemFeatures.keys()).slice(0, 1000); // Sample items

    for (const itemId of candidateItems) {
      const itemFeatures = this.itemFeatures.get(itemId);
      if (!itemFeatures) continue;

      // Prepare features for Wide & Deep model
      const wideFeatures = this.prepareWideFeatures(userFeatures, itemFeatures);
      const deepFeatures = this.prepareDeepFeatures(userFeatures, itemFeatures);

      const wideTensor = tf.tensor2d([wideFeatures]);
      const deepTensor = tf.tensor2d([deepFeatures]);

      const prediction = await this.models.deepLearning.wideAndDeep.predict([wideTensor, deepTensor]);
      const score = await prediction.data();

      recommendations.push({
        itemId,
        score: score[0],
        method: 'deep_learning',
        confidence: score[0]
      });

      // Cleanup tensors
      wideTensor.dispose();
      deepTensor.dispose();
      prediction.dispose();
    }

    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, count);
  }

  async generateHybridRecommendations(userId, count) {
    // Generate recommendations from multiple methods
    const collaborativeRecs = await this.generateCollaborativeRecommendations(userId, count * 2);
    const contentRecs = await this.generateContentBasedRecommendations(userId, count * 2);
    const deepRecs = await this.generateDeepLearningRecommendations(userId, count * 2);

    // Combine recommendations with weights
    const weights = {
      collaborative: 0.4,
      content: 0.3,
      deep_learning: 0.3
    };

    const combinedScores = new Map();

    // Process collaborative recommendations
    for (const rec of collaborativeRecs) {
      const currentScore = combinedScores.get(rec.itemId) || 0;
      combinedScores.set(rec.itemId, currentScore + rec.score * weights.collaborative);
    }

    // Process content-based recommendations
    for (const rec of contentRecs) {
      const currentScore = combinedScores.get(rec.itemId) || 0;
      combinedScores.set(rec.itemId, currentScore + rec.score * weights.content);
    }

    // Process deep learning recommendations
    for (const rec of deepRecs) {
      const currentScore = combinedScores.get(rec.itemId) || 0;
      combinedScores.set(rec.itemId, currentScore + rec.score * weights.deep_learning);
    }

    return Array.from(combinedScores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, count)
      .map(([itemId, score]) => ({
        itemId,
        score,
        method: 'hybrid',
        confidence: Math.min(score, 1)
      }));
  }

  /**
   * Record user interaction
   * @param {string} userId - User identifier
   * @param {string} itemId - Item identifier
   * @param {number} rating - User rating (1-5)
   * @param {Object} context - Interaction context
   */
  recordInteraction(userId, itemId, rating, context = {}) {
    if (!this.interactions.has(userId)) {
      this.interactions.set(userId, []);
    }

    const interaction = {
      itemId,
      rating,
      timestamp: Date.now(),
      context
    };

    this.interactions.get(userId).push(interaction);

    // Update real-time recommendations if enabled
    if (this.options.enableRealTimeRecommendations) {
      this.updateRealTimeRecommendations(userId, interaction);
    }

    this.emit('interactionRecorded', { userId, itemId, rating, context });
  }

  /**
   * Update item features
   * @param {string} itemId - Item identifier
   * @param {Object} features - Item features
   */
  updateItemFeatures(itemId, features) {
    this.itemFeatures.set(itemId, features);
    
    // Invalidate related caches
    this.invalidateRecommendationCache(itemId);
    
    this.emit('itemFeaturesUpdated', { itemId, features });
  }

  /**
   * Update user features
   * @param {string} userId - User identifier
   * @param {Object} features - User features
   */
  updateUserFeatures(userId, features) {
    this.userFeatures.set(userId, features);
    
    // Invalidate user's recommendation cache
    this.invalidateUserRecommendationCache(userId);
    
    this.emit('userFeaturesUpdated', { userId, features });
  }

  setupSimilarityComputation() {
    // Compute item-item similarity matrix
    setInterval(() => {
      this.computeItemSimilarity();
    }, 3600000); // Every hour

    // Compute user-user similarity matrix
    setInterval(() => {
      this.computeUserSimilarity();
    }, 7200000); // Every 2 hours
  }

  startRealTimeProcessing() {
    // Process real-time recommendation updates
    setInterval(() => {
      this.processRealTimeUpdates();
    }, 60000); // Every minute
  }

  setupRecommendationUpdates() {
    // Update recommendations periodically
    setInterval(() => {
      this.updateRecommendations();
    }, this.options.updateFrequency);
  }

  getMetrics() {
    return {
      models: {
        collaborativeFiltering: Object.keys(this.models.collaborativeFiltering).filter(k => this.models.collaborativeFiltering[k] !== null).length,
        contentBased: Object.keys(this.models.contentBased).filter(k => this.models.contentBased[k] !== null).length,
        hybrid: Object.keys(this.models.hybrid).filter(k => this.models.hybrid[k] !== null).length,
        deepLearning: Object.keys(this.models.deepLearning).filter(k => this.models.deepLearning[k] !== null).length
      },
      data: {
        users: this.userFeatures.size,
        items: this.itemFeatures.size,
        interactions: Array.from(this.interactions.values()).reduce((sum, interactions) => sum + interactions.length, 0),
        userEmbeddings: this.userEmbeddings.size,
        itemEmbeddings: this.itemEmbeddings.size
      },
      cache: {
        recommendations: this.recommendationsCache.size,
        explanations: this.explanations.size
      },
      performance: {
        precision: this.calculateAveragePrecision(),
        recall: this.calculateAverageRecall(),
        diversity: this.calculateAverageDiversity(),
        coverage: this.calculateCoverage()
      }
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down ML Recommendation Engine...');
    
    // Save models and data
    await this.saveModels();
    await this.saveData();
    
    // Dispose of all models
    for (const category of Object.values(this.models)) {
      for (const model of Object.values(category)) {
        if (model && typeof model.dispose === 'function') {
          model.dispose();
        }
      }
    }
    
    // Clear data
    this.userFeatures.clear();
    this.itemFeatures.clear();
    this.interactions.clear();
    this.userEmbeddings.clear();
    this.itemEmbeddings.clear();
    this.recommendationsCache.clear();
    this.explanations.clear();
    
    console.log('✅ ML Recommendation Engine shutdown complete');
  }
}

export default RecommendationEngine;
