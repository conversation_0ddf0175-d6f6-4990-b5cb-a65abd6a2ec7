/**
 * Dependency Injection Container
 * 
 * This module provides a simple dependency injection container to improve
 * testability by allowing dependencies to be injected rather than
 * imported directly at module level.
 */

class DIContainer {
  constructor() {
    this.dependencies = new Map();
    this.singletons = new Map();
  }

  /**
   * Register a dependency
   * @param {string} name - Dependency name
   * @param {Function|Object} factory - Factory function or object
   * @param {Object} options - Registration options
   */
  register(name, factory, options = {}) {
    this.dependencies.set(name, {
      factory,
      singleton: options.singleton || false,
      dependencies: options.dependencies || []
    });
  }

  /**
   * Resolve a dependency
   * @param {string} name - Dependency name
   * @returns {*} Resolved dependency
   */
  resolve(name) {
    const dependency = this.dependencies.get(name);
    
    if (!dependency) {
      throw new Error(`Dependency '${name}' not found`);
    }

    // Return singleton instance if already created
    if (dependency.singleton && this.singletons.has(name)) {
      return this.singletons.get(name);
    }

    // Resolve dependencies
    const resolvedDeps = dependency.dependencies.map(dep => this.resolve(dep));

    // Create instance
    let instance;
    if (typeof dependency.factory === 'function') {
      instance = dependency.factory(...resolvedDeps);
    } else {
      instance = dependency.factory;
    }

    // Store singleton
    if (dependency.singleton) {
      this.singletons.set(name, instance);
    }

    return instance;
  }

  /**
   * Check if dependency is registered
   * @param {string} name - Dependency name
   * @returns {boolean}
   */
  has(name) {
    return this.dependencies.has(name);
  }

  /**
   * Clear all dependencies (useful for testing)
   */
  clear() {
    this.dependencies.clear();
    this.singletons.clear();
  }

  /**
   * Create a child container for testing
   * @returns {DIContainer}
   */
  createChild() {
    const child = new DIContainer();
    // Copy parent dependencies
    for (const [name, dep] of this.dependencies) {
      child.dependencies.set(name, { ...dep });
    }
    return child;
  }
}

// Global container instance
const container = new DIContainer();

// Register default dependencies
container.register('logger', () => {
  const { logger } = require('../../api/middleware/auth-middleware');
  return logger;
}, { singleton: true });

container.register('supabaseClient', () => {
  const { createClient } = require('@supabase/supabase-js');
  return createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_ANON_KEY
  );
}, { singleton: true });

container.register('redisClient', () => {
  const Redis = require('ioredis');
  return new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
}, { singleton: true });

/**
 * Helper function to create testable modules
 * @param {Function} moduleFactory - Factory function that receives dependencies
 * @param {Array} dependencies - Array of dependency names
 * @returns {Function} Module factory
 */
function createTestableModule(moduleFactory, dependencies = []) {
  return (customContainer = null) => {
    const activeContainer = customContainer || container;
    const resolvedDeps = dependencies.map(dep => activeContainer.resolve(dep));
    return moduleFactory(...resolvedDeps);
  };
}

module.exports = {
  DIContainer,
  container,
  createTestableModule
};
