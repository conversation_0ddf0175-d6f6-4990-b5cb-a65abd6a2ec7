{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2059", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/cross-region-replication.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 74505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 74505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 764, "endOffset": 12927, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2060", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/scripts/backup/cross-region-replication.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14417, "count": 1}, {"startOffset": 618, "endOffset": 632, "count": 0}, {"startOffset": 682, "endOffset": 696, "count": 0}, {"startOffset": 772, "endOffset": 794, "count": 0}, {"startOffset": 845, "endOffset": 870, "count": 0}, {"startOffset": 940, "endOffset": 964, "count": 0}, {"startOffset": 1018, "endOffset": 1045, "count": 0}, {"startOffset": 1117, "endOffset": 1143, "count": 0}, {"startOffset": 1198, "endOffset": 1227, "count": 0}, {"startOffset": 9913, "endOffset": 14416, "count": 0}], "isBlockCoverage": true}, {"functionName": "createS3Client", "ranges": [{"startOffset": 1509, "endOffset": 1720, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadReplicationLog", "ranges": [{"startOffset": 1791, "endOffset": 2144, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveReplicationLog", "ranges": [{"startOffset": 2219, "endOffset": 2443, "count": 0}], "isBlockCoverage": false}, {"functionName": "listObjects", "ranges": [{"startOffset": 2597, "endOffset": 2796, "count": 0}], "isBlockCoverage": false}, {"functionName": "replicateBucket", "ranges": [{"startOffset": 2974, "endOffset": 5052, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyReplication", "ranges": [{"startOffset": 5253, "endOffset": 7227, "count": 0}], "isBlockCoverage": false}, {"functionName": "runReplication", "ranges": [{"startOffset": 7331, "endOffset": 8676, "count": 0}], "isBlockCoverage": false}, {"functionName": "verifyAllReplications", "ranges": [{"startOffset": 8784, "endOffset": 9836, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10062, "endOffset": 10184, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10199, "endOffset": 10282, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10362, "endOffset": 10485, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10500, "endOffset": 10583, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10875, "endOffset": 10931, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10945, "endOffset": 11167, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11182, "endOffset": 11265, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11573, "endOffset": 11632, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11646, "endOffset": 11861, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11876, "endOffset": 11959, "count": 0}], "isBlockCoverage": false}, {"functionName": "runFullTest", "ranges": [{"startOffset": 12314, "endOffset": 13983, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14009, "endOffset": 14086, "count": 0}], "isBlockCoverage": false}]}]}