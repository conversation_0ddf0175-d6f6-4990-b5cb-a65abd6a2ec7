/**
 * Performance Optimization Middleware Tests
 *
 * This file contains tests for the performance optimization middleware.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import Redis from 'ioredis';
import zlib from 'zlib';
import { Buffer } from 'node:buffer';

// Mock dependencies
vi.mock('ioredis', () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      get: vi.fn(),
      set: vi.fn(),
      exists: vi.fn(),
      incr: vi.fn(),
      expire: vi.fn(),
      lpush: vi.fn(),
      ltrim: vi.fn(),
    })),
  };
});

// Mock logger
const mockLogger = {
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock auth-middleware
vi.mock('../../api/middleware/auth-middleware', () => ({
  logger: mockLogger,
}));

// Import the module under test
import {
  cache,
  etag,
  compress,
  generateCacheKey,
  generateETag,
} from '../../api/middleware/performance-optimization.js';

describe('Performance Optimization Middleware', () => {
  // Define type for Redis mock
  type MockRedis = {
    get: ReturnType<typeof vi.fn>;
    set: ReturnType<typeof vi.fn>;
    exists: ReturnType<typeof vi.fn>;
    incr: ReturnType<typeof vi.fn>;
    expire: ReturnType<typeof vi.fn>;
    lpush: ReturnType<typeof vi.fn>;
    ltrim: ReturnType<typeof vi.fn>;
  };

  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;
  let mockRedis: MockRedis;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Setup mock request and response
    mockRequest = {
      method: 'GET',
      originalUrl: '/api/test',
      url: '/api/test',
      headers: {},
      query: {},
      body: {},
      user: { id: 'user-123' },
    };

    mockResponse = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis(),
      set: vi.fn().mockReturnThis(),
      getHeaders: vi.fn().mockReturnValue({}),
    };

    nextFunction = vi.fn();

    // Get Redis mock
    mockRedis = new Redis();
  });

  describe('generateCacheKey', () => {
    it('should generate a cache key from request', () => {
      const key = generateCacheKey(mockRequest as Request);

      expect(key).toBeDefined();
      expect(typeof key).toBe('string');
      expect(key.length).toBe(64); // SHA-256 hash length
    });

    it('should generate different keys for different requests', () => {
      const key1 = generateCacheKey(mockRequest as Request);

      const modifiedRequest = {
        ...mockRequest,
        originalUrl: '/api/different',
      };

      const key2 = generateCacheKey(modifiedRequest as Request);

      expect(key1).not.toBe(key2);
    });

    it('should generate different keys for different users', () => {
      const key1 = generateCacheKey(mockRequest as Request);

      const modifiedRequest = {
        ...mockRequest,
        user: { id: 'user-456' },
      };

      const key2 = generateCacheKey(modifiedRequest as Request);

      expect(key1).not.toBe(key2);
    });
  });

  describe('generateETag', () => {
    it('should generate an ETag for string data', () => {
      const etag = generateETag('test data');

      expect(etag).toBeDefined();
      expect(typeof etag).toBe('string');
      expect(etag.length).toBe(32); // MD5 hash length
    });

    it('should generate an ETag for object data', () => {
      const etag = generateETag({ test: 'data' });

      expect(etag).toBeDefined();
      expect(typeof etag).toBe('string');
      expect(etag.length).toBe(32); // MD5 hash length
    });

    it('should generate different ETags for different data', () => {
      const etag1 = generateETag('test data 1');
      const etag2 = generateETag('test data 2');

      expect(etag1).not.toBe(etag2);
    });
  });

  describe('cache middleware', () => {
    it('should skip caching for non-cacheable methods', async () => {
      mockRequest.method = 'POST';

      const middleware = cache();

      await middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockRedis.get).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    it('should skip caching based on custom condition', async () => {
      const middleware = cache({
        condition: () => false,
      });

      await middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockRedis.get).not.toHaveBeenCalled();
      expect(nextFunction).toHaveBeenCalled();
    });

    it('should return cached response if available', async () => {
      const cachedResponse = {
        data: { test: 'data' },
        headers: { 'content-type': 'application/json' },
        status: 200,
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(cachedResponse));

      const middleware = cache();

      await middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockRedis.get).toHaveBeenCalled();
      expect(mockResponse.set).toHaveBeenCalledWith('content-type', 'application/json');
      expect(mockResponse.set).toHaveBeenCalledWith('X-Cache', 'HIT');
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.send).toHaveBeenCalledWith({ test: 'data' });
      expect(nextFunction).not.toHaveBeenCalled();
    });

    it('should cache response on cache miss', async () => {
      mockRedis.get.mockResolvedValue(null);
      mockResponse.statusCode = 200;

      const middleware = cache();

      await middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(mockRedis.get).toHaveBeenCalled();
      expect(mockResponse.set).toHaveBeenCalledWith('X-Cache', 'MISS');
      expect(nextFunction).toHaveBeenCalled();

      // The middleware should have modified the send method
      // Simulate calling the modified send method
      const sendMethod = mockResponse.send as ReturnType<typeof vi.fn>;
      sendMethod({ test: 'data' });

      // Should cache the response
      expect(mockRedis.set).toHaveBeenCalled();
    });
  });

  describe('etag middleware', () => {
    it('should add ETag header to response', () => {
      const middleware = etag();

      middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(nextFunction).toHaveBeenCalled();

      // The middleware should have modified the send method
      // Simulate calling the modified send method
      const sendMethod = mockResponse.send as ReturnType<typeof vi.fn>;
      sendMethod({ test: 'data' });

      // Should add ETag header
      expect(mockResponse.set).toHaveBeenCalledWith('ETag', expect.any(String));
    });

    it('should return 304 Not Modified if ETag matches', () => {
      // Generate ETag for test data
      const testData = { test: 'data' };
      const testETag = `"${generateETag(testData)}"`;

      // Set If-None-Match header
      mockRequest.headers = {
        'if-none-match': testETag,
      };

      const middleware = etag();

      middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(nextFunction).toHaveBeenCalled();

      // The middleware should have modified the send method
      // Simulate calling the modified send method
      const sendMethod = mockResponse.send as ReturnType<typeof vi.fn>;
      sendMethod(testData);

      // Should return 304 Not Modified
      expect(mockResponse.status).toHaveBeenCalledWith(304);
    });
  });

  describe('compress middleware', () => {
    it('should skip compression if not supported by client', () => {
      mockRequest.headers = {};

      const middleware = compress();

      middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(nextFunction).toHaveBeenCalled();
    });

    it('should compress response with gzip if supported', () => {
      // Mock zlib.gzipSync
      const gzipSyncSpy = vi
        .spyOn(zlib, 'gzipSync')
        .mockImplementation(() => Buffer.from('compressed'));

      // Set Accept-Encoding header
      mockRequest.headers = {
        'accept-encoding': 'gzip',
      };

      const middleware = compress();

      middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(nextFunction).toHaveBeenCalled();

      // The middleware should have modified the send method
      // Simulate calling the modified send method with large enough data
      const sendMethod = mockResponse.send as ReturnType<typeof vi.fn>;
      const largeData = 'a'.repeat(2000);
      sendMethod(largeData);

      // Should compress the response
      expect(gzipSyncSpy).toHaveBeenCalled();
      expect(mockResponse.set).toHaveBeenCalledWith('Content-Encoding', 'gzip');
      expect(mockResponse.set).toHaveBeenCalledWith('Vary', 'Accept-Encoding');

      // Clean up
      gzipSyncSpy.mockRestore();
    });

    it('should skip compression for small responses', () => {
      // Set Accept-Encoding header
      mockRequest.headers = {
        'accept-encoding': 'gzip',
      };

      const middleware = compress();

      middleware(mockRequest as Request, mockResponse as Response, nextFunction);

      expect(nextFunction).toHaveBeenCalled();

      // The middleware should have modified the send method
      // Simulate calling the modified send method with small data
      const sendMethod = mockResponse.send as ReturnType<typeof vi.fn>;
      const smallData = 'small';
      sendMethod(smallData);

      // Should not compress the response
      expect(mockResponse.set).not.toHaveBeenCalledWith('Content-Encoding', expect.any(String));
    });
  });
});
