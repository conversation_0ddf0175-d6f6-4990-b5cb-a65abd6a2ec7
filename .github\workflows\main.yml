name: MVS-VR CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - development
          - staging
          - production

env:
  DOCKER_REGISTRY: ghcr.io
  DOCKER_REGISTRY_USERNAME: ${{ github.actor }}
  DOCKER_REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_DB: mvs_test
  TEST_OUTPUT_DIR: ${{ github.workspace }}/test-results
  COVERAGE_OUTPUT_DIR: ${{ github.workspace }}/coverage
  PERFORMANCE_TEST_OUTPUT: ${{ github.workspace }}/performance-results

jobs:
  lint:
    name: Lint Code
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 black isort mypy
          pip install -r requirements.txt

      - name: Run linters
        run: |
          flake8 mvs_project tests
          black --check mvs_project tests
          isort --check-only --profile black mvs_project tests
          mypy mvs_project

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Lint frontend code
        run: |
          cd mvs_project/frontend
          npm ci
          npm run lint

  test:
    name: Run Tests
    needs: lint
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: ${{ env.POSTGRES_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRES_PASSWORD }}
          POSTGRES_DB: ${{ env.POSTGRES_DB }}
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-xdist

      - name: Run unit tests
        run: |
          mkdir -p ${{ env.TEST_OUTPUT_DIR }}
          mkdir -p ${{ env.COVERAGE_OUTPUT_DIR }}
          pytest mvs_project/tests/unit/ \
            --cov=mvs_project \
            --cov-report=xml:${{ env.COVERAGE_OUTPUT_DIR }}/coverage.xml \
            --cov-report=html:${{ env.COVERAGE_OUTPUT_DIR }}/html \
            --junitxml=${{ env.TEST_OUTPUT_DIR }}/unit-tests.xml
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_TEST_SERVICE_ROLE_KEY }}

      - name: Run integration tests
        run: |
          pytest mvs_project/tests/integration/ \
            --junitxml=${{ env.TEST_OUTPUT_DIR }}/integration-tests.xml
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_TEST_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_TEST_SERVICE_ROLE_KEY }}

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: '**/package-lock.json'

      - name: Run frontend tests
        run: |
          cd mvs_project
          npm ci
          npm run test:coverage
          # Generate JUnit XML report for test results
          npx vitest run --reporter=junit --outputFile=${{ env.TEST_OUTPUT_DIR }}/frontend-tests.xml
          # Move coverage reports
          mv coverage ${{ env.COVERAGE_OUTPUT_DIR }}/frontend

      - name: Run server integration tests
        run: |
          cd mvs-vr-v2/implementation/server
          npm ci
          npm run test:coverage
          # Generate JUnit XML report for server tests
          npx vitest run --reporter=junit --outputFile=${{ env.TEST_OUTPUT_DIR }}/server-tests.xml
          # Move server coverage reports
          mv coverage ${{ env.COVERAGE_OUTPUT_DIR }}/server

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: ${{ env.TEST_OUTPUT_DIR }}

      - name: Upload coverage reports
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: coverage-reports
          path: ${{ env.COVERAGE_OUTPUT_DIR }}

      - name: Check coverage thresholds
        run: |
          python -c "
          import xml.etree.ElementTree as ET
          tree = ET.parse('${{ env.COVERAGE_OUTPUT_DIR }}/coverage.xml')
          root = tree.getroot()
          coverage = float(root.attrib['line-rate']) * 100
          print(f'Line coverage: {coverage:.2f}%')
          assert coverage >= 80, f'Coverage {coverage:.2f}% is below the 80% threshold'
          "

  build:
    name: Build Docker Images
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ env.DOCKER_REGISTRY_USERNAME }}
          password: ${{ env.DOCKER_REGISTRY_PASSWORD }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,format=short

      - name: Build and push API image
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push frontend image
        uses: docker/build-push-action@v4
        with:
          context: ./mvs_project/frontend
          file: ./mvs_project/frontend/Dockerfile
          push: true
          tags: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/frontend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop' || github.event.inputs.environment == 'staging'
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - uses: actions/checkout@v3

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.25.0'

      - name: Set up Kubernetes config
        run: |
          mkdir -p ~/.kube
          echo "${{ secrets.KUBE_CONFIG }}" > ~/.kube/config
          chmod 600 ~/.kube/config

      - name: Update deployment manifests
        run: |
          sed -i "s|image: .*|image: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}:${{ github.sha }}|g" kubernetes/staging/api-deployment.yaml
          sed -i "s|image: .*|image: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/frontend:${{ github.sha }}|g" kubernetes/staging/frontend-deployment.yaml

      - name: Deploy to staging
        run: |
          kubectl apply -f kubernetes/staging/

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/api -n mvs-vr-staging
          kubectl rollout status deployment/frontend -n mvs-vr-staging

      - name: Run smoke tests
        run: |
          pip install -r requirements.txt
          python -m pytest tests/smoke/ --base-url=https://staging-api.mvs-vr.com

      - name: Notify deployment success
        if: success()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "✅ Staging deployment successful for ${{ github.repository }} (${{ github.sha }})"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify deployment failure
        if: failure()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "❌ Staging deployment failed for ${{ github.repository }} (${{ github.sha }})"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy-production:
    name: Deploy to Production
    needs: deploy-staging
    if: github.event_name == 'push' && github.ref == 'refs/heads/main' || github.event.inputs.environment == 'production'
    runs-on: ubuntu-latest
    environment: production

    steps:
      - uses: actions/checkout@v3

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.25.0'

      - name: Set up Kubernetes config
        run: |
          mkdir -p ~/.kube
          echo "${{ secrets.KUBE_CONFIG }}" > ~/.kube/config
          chmod 600 ~/.kube/config

      - name: Update deployment manifests
        run: |
          sed -i "s|image: .*|image: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}:${{ github.sha }}|g" kubernetes/production/api-deployment.yaml
          sed -i "s|image: .*|image: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/frontend:${{ github.sha }}|g" kubernetes/production/frontend-deployment.yaml

      - name: Deploy to production
        run: |
          kubectl apply -f kubernetes/production/

      - name: Verify deployment
        run: |
          kubectl rollout status deployment/api -n mvs-vr-production
          kubectl rollout status deployment/frontend -n mvs-vr-production

      - name: Run smoke tests
        run: |
          pip install -r requirements.txt
          python -m pytest tests/smoke/ --base-url=https://api.mvs-vr.com

      - name: Notify deployment success
        if: success()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "✅ Production deployment successful for ${{ github.repository }} (${{ github.sha }})"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify deployment failure
        if: failure()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "❌ Production deployment failed for ${{ github.repository }} (${{ github.sha }})"
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
