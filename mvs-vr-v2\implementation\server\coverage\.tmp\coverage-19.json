{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2502", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/e2e/complete-user-journey.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 56451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 56451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1229, "endOffset": 11929, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1314, "endOffset": 1916, "count": 1}, {"startOffset": 1532, "endOffset": 1620, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1979, "endOffset": 3434, "count": 1}, {"startOffset": 2074, "endOffset": 3433, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3068, "endOffset": 3188, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3501, "endOffset": 4801, "count": 1}, {"startOffset": 3664, "endOffset": 4800, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4870, "endOffset": 6302, "count": 1}, {"startOffset": 5015, "endOffset": 6301, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6365, "endOffset": 8034, "count": 1}, {"startOffset": 6510, "endOffset": 8033, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7668, "endOffset": 7788, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8117, "endOffset": 9563, "count": 1}, {"startOffset": 8262, "endOffset": 9562, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9625, "endOffset": 10833, "count": 1}, {"startOffset": 9830, "endOffset": 10832, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10897, "endOffset": 11796, "count": 1}, {"startOffset": 10992, "endOffset": 11795, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11833, "endOffset": 11925, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "2508", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/e2e/integration-testing-framework.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40799, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40799, "count": 1}], "isBlockCoverage": true}, {"functionName": "generateTestId", "ranges": [{"startOffset": 1410, "endOffset": 1450, "count": 0}], "isBlockCoverage": false}, {"functionName": "login", "ranges": [{"startOffset": 1509, "endOffset": 2349, "count": 2}, {"startOffset": 1659, "endOffset": 1722, "count": 0}, {"startOffset": 1729, "endOffset": 1792, "count": 0}, {"startOffset": 1799, "endOffset": 1866, "count": 0}, {"startOffset": 1960, "endOffset": 2348, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2228, "endOffset": 2301, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestAsset", "ranges": [{"startOffset": 2408, "endOffset": 3606, "count": 0}], "isBlockCoverage": false}, {"functionName": "waitForAssetProcessing", "ranges": [{"startOffset": 3690, "endOffset": 4360, "count": 0}], "isBlockCoverage": false}, {"functionName": "createTestShowroom", "ranges": [{"startOffset": 4425, "endOffset": 5307, "count": 0}], "isBlockCoverage": false}, {"functionName": "addAssetToShowroom", "ranges": [{"startOffset": 5371, "endOffset": 6332, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateAnalyticsData", "ranges": [{"startOffset": 6406, "endOffset": 7325, "count": 1}, {"startOffset": 6484, "endOffset": 6489, "count": 0}, {"startOffset": 6533, "endOffset": 6565, "count": 0}, {"startOffset": 7315, "endOffset": 7324, "count": 0}], "isBlockCoverage": true}]}]}