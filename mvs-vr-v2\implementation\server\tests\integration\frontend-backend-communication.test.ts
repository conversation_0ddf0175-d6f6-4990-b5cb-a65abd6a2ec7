/**
 * Frontend-Backend Communication Integration Tests
 * 
 * Tests the communication between frontend components and backend APIs
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createServer } from 'http';
import { AddressInfo } from 'net';
import axios from 'axios';

// Mock Directus API endpoints
const mockDirectusEndpoints = {
  '/items/showroom_layouts': {
    GET: (req, res) => {
      const vendorId = req.url.includes('vendor_id') ? 'vendor-1' : null;
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        data: vendorId ? [
          {
            id: 'layout-1',
            name: 'Test Layout',
            vendor_id: vendorId,
            template: 'template_1',
            items: []
          }
        ] : []
      }));
    },
    POST: (req, res) => {
      let body = '';
      req.on('data', chunk => body += chunk);
      req.on('end', () => {
        const data = JSON.parse(body);
        res.writeHead(201, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          data: {
            id: 'new-layout-id',
            ...data
          }
        }));
      });
    },
    PATCH: (req, res) => {
      let body = '';
      req.on('data', chunk => body += chunk);
      req.on('end', () => {
        const data = JSON.parse(body);
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          data: {
            id: req.url.split('/').pop(),
            ...data
          }
        }));
      });
    }
  },
  '/items/products': {
    GET: (req, res) => {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        data: [
          {
            id: 'product-1',
            name: 'Test Product',
            vendor_id: 'vendor-1',
            thumbnail: '/test-thumbnail.jpg'
          }
        ]
      }));
    }
  },
  '/items/materials': {
    GET: (req, res) => {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        data: [
          {
            id: 'material-1',
            name: 'Test Material',
            vendor_id: 'vendor-1',
            type: 'Standard',
            textures: {}
          }
        ]
      }));
    }
  },
  '/files': {
    POST: (req, res) => {
      res.writeHead(201, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        data: {
          id: 'file-1',
          filename_download: 'test-texture.jpg',
          data: {
            full_url: 'http://localhost/assets/test-texture.jpg'
          }
        }
      }));
    }
  }
};

describe('Frontend-Backend Communication Integration', () => {
  let server;
  let baseURL;

  beforeEach(async () => {
    // Create mock server
    server = createServer((req, res) => {
      // Enable CORS
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PATCH, DELETE, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      // Route requests to mock endpoints
      const path = req.url.split('?')[0];
      const endpoint = mockDirectusEndpoints[path];

      if (endpoint && endpoint[req.method]) {
        endpoint[req.method](req, res);
      } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
      }
    });

    // Start server on random port
    await new Promise((resolve) => {
      server.listen(0, () => {
        const address = server.address() as AddressInfo;
        baseURL = `http://localhost:${address.port}`;
        resolve(undefined);
      });
    });
  });

  afterEach(async () => {
    if (server) {
      await new Promise((resolve) => {
        server.close(resolve);
      });
    }
  });

  describe('Showroom Layout API Communication', () => {
    it('should fetch showroom layouts for a vendor', async () => {
      const response = await axios.get(`${baseURL}/items/showroom_layouts?filter[vendor_id][_eq]=vendor-1`);
      
      expect(response.status).toBe(200);
      expect(response.data.data).toHaveLength(1);
      expect(response.data.data[0]).toMatchObject({
        id: 'layout-1',
        name: 'Test Layout',
        vendor_id: 'vendor-1'
      });
    });

    it('should create a new showroom layout', async () => {
      const layoutData = {
        name: 'New Layout',
        vendor_id: 'vendor-1',
        template: 'template_2',
        items: []
      };

      const response = await axios.post(`${baseURL}/items/showroom_layouts`, layoutData);
      
      expect(response.status).toBe(201);
      expect(response.data.data).toMatchObject({
        id: 'new-layout-id',
        name: 'New Layout',
        vendor_id: 'vendor-1'
      });
    });

    it('should update an existing showroom layout', async () => {
      const updateData = {
        name: 'Updated Layout',
        items: [
          {
            id: 'item-1',
            productId: 'product-1',
            x: 100,
            y: 100,
            rotation: 0
          }
        ]
      };

      const response = await axios.patch(`${baseURL}/items/showroom_layouts/layout-1`, updateData);
      
      expect(response.status).toBe(200);
      expect(response.data.data).toMatchObject({
        id: 'layout-1',
        name: 'Updated Layout'
      });
    });
  });

  describe('Product API Communication', () => {
    it('should fetch products for a vendor', async () => {
      const response = await axios.get(`${baseURL}/items/products?filter[vendor_id][_eq]=vendor-1`);
      
      expect(response.status).toBe(200);
      expect(response.data.data).toHaveLength(1);
      expect(response.data.data[0]).toMatchObject({
        id: 'product-1',
        name: 'Test Product',
        vendor_id: 'vendor-1'
      });
    });
  });

  describe('Material API Communication', () => {
    it('should fetch materials for a vendor', async () => {
      const response = await axios.get(`${baseURL}/items/materials?filter[vendor_id][_eq]=vendor-1`);
      
      expect(response.status).toBe(200);
      expect(response.data.data).toHaveLength(1);
      expect(response.data.data[0]).toMatchObject({
        id: 'material-1',
        name: 'Test Material',
        vendor_id: 'vendor-1'
      });
    });
  });

  describe('File Upload Communication', () => {
    it('should handle texture file uploads', async () => {
      // Create mock file data
      const formData = new FormData();
      const blob = new Blob(['fake texture data'], { type: 'image/jpeg' });
      formData.append('file', blob, 'test-texture.jpg');

      const response = await axios.post(`${baseURL}/files`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      expect(response.status).toBe(201);
      expect(response.data.data).toMatchObject({
        id: 'file-1',
        filename_download: 'test-texture.jpg'
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 errors gracefully', async () => {
      try {
        await axios.get(`${baseURL}/items/nonexistent`);
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data).toMatchObject({
          error: 'Not found'
        });
      }
    });

    it('should handle network timeouts', async () => {
      const client = axios.create({
        timeout: 1,
        baseURL
      });

      try {
        await client.get('/items/showroom_layouts');
      } catch (error) {
        expect(error.code).toBe('ECONNABORTED');
      }
    });
  });

  describe('Authentication Integration', () => {
    it('should include authentication headers in requests', async () => {
      const authToken = 'test-auth-token';
      
      const response = await axios.get(`${baseURL}/items/showroom_layouts`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      expect(response.status).toBe(200);
    });
  });

  describe('Data Validation', () => {
    it('should validate required fields in POST requests', async () => {
      const invalidData = {
        // Missing required vendor_id
        name: 'Invalid Layout'
      };

      try {
        await axios.post(`${baseURL}/items/showroom_layouts`, invalidData);
      } catch (error) {
        // In a real implementation, this would return a validation error
        // For now, we just verify the request was made
        expect(error.response?.status).toBeDefined();
      }
    });
  });
});
