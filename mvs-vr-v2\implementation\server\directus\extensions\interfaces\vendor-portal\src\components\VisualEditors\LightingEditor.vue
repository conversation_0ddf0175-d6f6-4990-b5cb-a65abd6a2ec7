<template>
  <div class="lighting-editor">
    <!-- Basic structure for the Lighting Editor -->
    <div class="editor-header">
      <div class="editor-title-section">
        <h3 class="editor-title">Lighting Editor</h3>
        <div class="editor-subtitle">Create and manage lighting for your showroom</div>
      </div>
      <div class="editor-actions">
        <button class="action-button" @click="saveLighting" :disabled="!hasChanges">
          <i class="material-icons">save</i>
          <span>Save</span>
        </button>
        <button class="action-button" @click="resetLighting" :disabled="!hasChanges">
          <i class="material-icons">refresh</i>
          <span>Reset</span>
        </button>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <div class="sidebar-section">
          <h4 class="sidebar-title">Lights</h4>

          <div class="light-types">
            <div class="light-types-title">Add Light</div>
            <div class="light-type-buttons">
              <button class="light-type-button" @click="createLight('point')">
                <i class="material-icons">lightbulb</i>
                <span>Point</span>
              </button>
              <button class="light-type-button" @click="createLight('spot')">
                <i class="material-icons">highlight</i>
                <span>Spot</span>
              </button>
              <button class="light-type-button" @click="createLight('directional')">
                <i class="material-icons">wb_sunny</i>
                <span>Directional</span>
              </button>
              <button class="light-type-button" @click="createLight('ambient')">
                <i class="material-icons">brightness_low</i>
                <span>Ambient</span>
              </button>
            </div>
          </div>

          <div class="light-list">
            <div v-if="lights.length === 0" class="no-lights">
              <i class="material-icons">lightbulb_outline</i>
              <p>No lights added yet</p>
              <p>Add a light using the buttons above</p>
            </div>

            <div
              v-for="light in lights"
              :key="light.id"
              class="light-item"
              :class="{ active: selectedLightId === light.id }"
              @click="selectLight(light.id)"
            >
              <div class="light-icon">
                <i class="material-icons">
                  {{ getLightIcon(light.type) }}
                </i>
              </div>
              <div class="light-info">
                <div class="light-name">{{ light.name }}</div>
                <div class="light-type">{{ getLightTypeName(light.type) }}</div>
              </div>
              <div class="light-actions">
                <button class="light-action-button" @click.stop="deleteLight(light.id)">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="editor-main">
        <div v-if="!selectedLight" class="no-light-selected">
          <i class="material-icons">lightbulb_outline</i>
          <p>Select a light to edit or create a new one</p>
        </div>

        <div v-else class="light-editor">
          <div class="light-properties">
            <div class="property-section">
              <h4 class="property-section-title">Basic Properties</h4>

              <div class="property-row">
                <div class="property-group">
                  <label for="light-name">Name</label>
                  <input
                    type="text"
                    id="light-name"
                    v-model="selectedLight.name"
                    placeholder="Enter light name"
                  >
                </div>

                <div class="property-group">
                  <label for="light-enabled">Enabled</label>
                  <div class="toggle-switch">
                    <input
                      type="checkbox"
                      id="light-enabled"
                      v-model="selectedLight.enabled"
                    >
                    <label for="light-enabled"></label>
                  </div>
                </div>
              </div>

              <div class="property-row">
                <div class="property-group">
                  <label for="light-color">Color</label>
                  <div class="color-input">
                    <input
                      type="color"
                      id="light-color"
                      v-model="selectedLight.color"
                    >
                    <input
                      type="text"
                      v-model="selectedLight.color"
                      placeholder="#FFFFFF"
                      class="color-text-input"
                    >
                  </div>
                </div>

                <div class="property-group">
                  <label for="light-intensity">Intensity</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="light-intensity"
                      v-model.number="selectedLight.intensity"
                      min="0"
                      max="10"
                      step="0.1"
                    >
                    <span class="slider-value">{{ selectedLight.intensity?.toFixed(1) || '1.0' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Point Light Properties -->
            <div v-if="selectedLight.type === 'point'" class="property-section">
              <h4 class="property-section-title">Point Light Properties</h4>

              <div class="property-row">
                <div class="property-group">
                  <label for="point-distance">Distance</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="point-distance"
                      v-model.number="selectedLight.distance"
                      min="0"
                      max="100"
                      step="1"
                    >
                    <span class="slider-value">{{ selectedLight.distance }}</span>
                  </div>
                </div>

                <div class="property-group">
                  <label for="point-decay">Decay</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="point-decay"
                      v-model.number="selectedLight.decay"
                      min="0"
                      max="2"
                      step="0.1"
                    >
                    <span class="slider-value">{{ selectedLight.decay?.toFixed(1) || '1.0' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Spot Light Properties -->
            <div v-if="selectedLight.type === 'spot'" class="property-section">
              <h4 class="property-section-title">Spot Light Properties</h4>

              <div class="property-row">
                <div class="property-group">
                  <label for="spot-distance">Distance</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="spot-distance"
                      v-model.number="selectedLight.distance"
                      min="0"
                      max="100"
                      step="1"
                    >
                    <span class="slider-value">{{ selectedLight.distance }}</span>
                  </div>
                </div>

                <div class="property-group">
                  <label for="spot-angle">Angle</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="spot-angle"
                      v-model.number="selectedLight.angle"
                      min="0"
                      max="90"
                      step="1"
                    >
                    <span class="slider-value">{{ selectedLight.angle }}°</span>
                  </div>
                </div>
              </div>

              <div class="property-row">
                <div class="property-group">
                  <label for="spot-penumbra">Penumbra</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="spot-penumbra"
                      v-model.number="selectedLight.penumbra"
                      min="0"
                      max="1"
                      step="0.01"
                    >
                    <span class="slider-value">{{ selectedLight.penumbra?.toFixed(2) || '0.25' }}</span>
                  </div>
                </div>

                <div class="property-group">
                  <label for="spot-decay">Decay</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="spot-decay"
                      v-model.number="selectedLight.decay"
                      min="0"
                      max="2"
                      step="0.1"
                    >
                    <span class="slider-value">{{ selectedLight.decay?.toFixed(1) || '1.0' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Directional Light Properties -->
            <div v-if="selectedLight.type === 'directional'" class="property-section">
              <h4 class="property-section-title">Directional Light Properties</h4>

              <div class="property-row">
                <div class="property-group">
                  <label>Direction</label>
                  <div class="direction-controls">
                    <div class="direction-row">
                      <label for="direction-x">X</label>
                      <input
                        type="range"
                        id="direction-x"
                        v-model.number="selectedLight.direction.x"
                        min="-1"
                        max="1"
                        step="0.1"
                      >
                      <span class="direction-value">{{ selectedLight.direction?.x?.toFixed(1) || '0.0' }}</span>
                    </div>
                    <div class="direction-row">
                      <label for="direction-y">Y</label>
                      <input
                        type="range"
                        id="direction-y"
                        v-model.number="selectedLight.direction.y"
                        min="-1"
                        max="1"
                        step="0.1"
                      >
                      <span class="direction-value">{{ selectedLight.direction?.y?.toFixed(1) || '-1.0' }}</span>
                    </div>
                    <div class="direction-row">
                      <label for="direction-z">Z</label>
                      <input
                        type="range"
                        id="direction-z"
                        v-model.number="selectedLight.direction.z"
                        min="-1"
                        max="1"
                        step="0.1"
                      >
                      <span class="direction-value">{{ selectedLight.direction?.z?.toFixed(1) || '0.0' }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Ambient Light Properties -->
            <div v-if="selectedLight.type === 'ambient'" class="property-section">
              <h4 class="property-section-title">Ambient Light Properties</h4>

              <div class="property-row">
                <div class="property-group">
                  <label for="ambient-ground-color">Ground Color</label>
                  <div class="color-input">
                    <input
                      type="color"
                      id="ambient-ground-color"
                      v-model="selectedLight.groundColor"
                    >
                    <input
                      type="text"
                      v-model="selectedLight.groundColor"
                      placeholder="#000000"
                      class="color-text-input"
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="light-preview">
            <h4 class="preview-title">Preview</h4>
            <div class="preview-container">
              <div class="preview-scene">
                <div class="preview-placeholder">
                  <i class="material-icons">visibility</i>
                  <p>3D Preview</p>
                  <p class="placeholder-subtitle">Coming soon</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LightingEditor',

  props: {
    vendorId: {
      type: String,
      required: true
    },
    showroomId: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      lights: [],
      selectedLightId: null,
      originalLighting: null,
      isLoading: false
    };
  },

  computed: {
    selectedLight() {
      return this.lights.find(light => light.id === this.selectedLightId) || null;
    },

    hasChanges() {
      return this.originalLighting &&
             JSON.stringify(this.lights) !== JSON.stringify(this.originalLighting);
    }
  },

  created() {
    this.loadData();
  },

  methods: {
    async loadData() {
      if (!this.showroomId) return;

      this.isLoading = true;

      try {
        // Load lighting data for the showroom
        const response = await this.$api.get(`/items/showroom_lighting?filter[showroom_id][_eq]=${this.showroomId}`);

        if (response.data.data && response.data.data.length > 0) {
          this.lights = response.data.data[0].lights || [];
        } else {
          // Create default lighting if none exists
          this.lights = this.getDefaultLighting();
        }

        this.originalLighting = JSON.parse(JSON.stringify(this.lights));

        // Select the first light if available
        if (this.lights.length > 0) {
          this.selectedLightId = this.lights[0].id;
        }
      } catch (error) {
        console.error('Error loading lighting data:', error);
      } finally {
        this.isLoading = false;
      }
    },

    getDefaultLighting() {
      return [
        {
          id: 'ambient_1',
          name: 'Ambient Light',
          type: 'ambient',
          enabled: true,
          color: '#FFFFFF',
          intensity: 0.5,
          groundColor: '#000000'
        },
        {
          id: 'directional_1',
          name: 'Main Light',
          type: 'directional',
          enabled: true,
          color: '#FFFFFF',
          intensity: 1.0,
          direction: { x: 0.5, y: -1, z: -0.5 }
        }
      ];
    },

    getLightIcon(type) {
      switch (type) {
        case 'point': return 'lightbulb';
        case 'spot': return 'highlight';
        case 'directional': return 'wb_sunny';
        case 'ambient': return 'brightness_low';
        default: return 'lightbulb_outline';
      }
    },

    getLightTypeName(type) {
      switch (type) {
        case 'point': return 'Point Light';
        case 'spot': return 'Spot Light';
        case 'directional': return 'Directional Light';
        case 'ambient': return 'Ambient Light';
        default: return 'Unknown';
      }
    },

    createLight(type) {
      const newLight = {
        id: `${type}_${Date.now()}`,
        name: `New ${this.getLightTypeName(type)}`,
        type,
        enabled: true,
        color: '#FFFFFF',
        intensity: 1.0
      };

      // Add type-specific properties
      switch (type) {
        case 'point':
          newLight.distance = 50;
          newLight.decay = 1.0;
          break;
        case 'spot':
          newLight.distance = 50;
          newLight.angle = 45;
          newLight.penumbra = 0.25;
          newLight.decay = 1.0;
          break;
        case 'directional':
          newLight.direction = { x: 0, y: -1, z: 0 };
          break;
        case 'ambient':
          newLight.groundColor = '#000000';
          break;
      }

      this.lights.push(newLight);
      this.selectedLightId = newLight.id;
    },

    selectLight(lightId) {
      this.selectedLightId = lightId;
    },

    deleteLight(lightId) {
      const index = this.lights.findIndex(light => light.id === lightId);

      if (index !== -1) {
        // If deleting the selected light, select another one
        if (this.selectedLightId === lightId) {
          if (this.lights.length > 1) {
            this.selectedLightId = this.lights[index === 0 ? 1 : index - 1].id;
          } else {
            this.selectedLightId = null;
          }
        }

        this.lights.splice(index, 1);
      }
    },

    async saveLighting() {
      if (!this.showroomId) return;

      this.isLoading = true;

      try {
        // Check if lighting data exists for this showroom
        const response = await this.$api.get(`/items/showroom_lighting?filter[showroom_id][_eq]=${this.showroomId}`);

        if (response.data.data && response.data.data.length > 0) {
          // Update existing lighting data
          const lightingId = response.data.data[0].id;
          await this.$api.patch(`/items/showroom_lighting/${lightingId}`, {
            lights: this.lights
          });
        } else {
          // Create new lighting data
          await this.$api.post('/items/showroom_lighting', {
            showroom_id: this.showroomId,
            lights: this.lights
          });
        }

        this.originalLighting = JSON.parse(JSON.stringify(this.lights));
        this.$emit('update', this.lights);
      } catch (error) {
        console.error('Error saving lighting data:', error);
      } finally {
        this.isLoading = false;
      }
    },

    resetLighting() {
      if (this.originalLighting) {
        this.lights = JSON.parse(JSON.stringify(this.originalLighting));

        // Ensure selected light still exists
        if (this.selectedLightId) {
          const lightExists = this.lights.some(light => light.id === this.selectedLightId);
          if (!lightExists && this.lights.length > 0) {
            this.selectedLightId = this.lights[0].id;
          } else if (!lightExists) {
            this.selectedLightId = null;
          }
        }
      }
    }
  }
};
</script>

<style scoped>
.lighting-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.editor-subtitle {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  border-right: 1px solid var(--theme--border-color);
  overflow-y: auto;
  background-color: var(--theme--background-subdued);
}

.sidebar-section {
  padding: 16px;
}

.sidebar-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.light-types {
  margin-bottom: 16px;
}

.light-types-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.light-type-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.light-type-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.light-type-button:hover {
  background-color: var(--theme--background-accent);
}

.light-type-button i {
  font-size: 24px;
  margin-bottom: 4px;
}

.light-type-button span {
  font-size: 12px;
}

.light-list {
  max-height: 400px;
  overflow-y: auto;
}

.no-lights {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-lights i {
  font-size: 32px;
  margin-bottom: 8px;
}

.no-lights p {
  margin: 4px 0;
  font-size: 14px;
}

.light-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  background-color: var(--theme--background);
}

.light-item:hover {
  background-color: var(--theme--background-accent);
}

.light-item.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.light-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.light-info {
  flex: 1;
}

.light-name {
  font-size: 14px;
  font-weight: 500;
}

.light-type {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.light-item.active .light-type {
  color: var(--theme--primary-background);
  opacity: 0.8;
}

.light-actions {
  display: flex;
}

.light-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground);
  cursor: pointer;
}

.light-item.active .light-action-button {
  color: var(--theme--primary-background);
}

.light-action-button:hover {
  background-color: var(--theme--danger);
  color: var(--theme--danger-background);
}

.editor-main {
  flex: 1;
  overflow: auto;
  background-color: var(--theme--background);
}

.no-light-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.no-light-selected i {
  font-size: 48px;
  margin-bottom: 16px;
}

.light-editor {
  display: flex;
  padding: 24px;
  gap: 24px;
}

.light-properties {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.property-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 16px;
}

.property-section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.property-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.property-row:last-child {
  margin-bottom: 0;
}

.property-group {
  flex: 1;
}

.property-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
}

.property-group input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: 0.4s;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--theme--foreground-subdued);
  border-radius: 50%;
  transition: 0.4s;
}

.toggle-switch input:checked + label {
  background-color: var(--theme--primary);
}

.toggle-switch input:checked + label:before {
  transform: translateX(16px);
  background-color: var(--theme--primary-background);
}

.color-input {
  display: flex;
  gap: 8px;
}

.color-input input[type="color"] {
  width: 40px;
  height: 32px;
  padding: 0;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
}

.color-text-input {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.slider-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider-input input[type="range"] {
  flex: 1;
}

.slider-value {
  width: 40px;
  text-align: right;
  font-size: 14px;
}

.direction-controls {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.direction-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.direction-row label {
  width: 16px;
  margin: 0;
  text-align: center;
}

.direction-row input[type="range"] {
  flex: 1;
}

.direction-value {
  width: 40px;
  text-align: right;
  font-size: 14px;
}

.light-preview {
  width: 300px;
}

.preview-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.preview-container {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 16px;
}

.preview-scene {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
  border-radius: 4px;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
  text-align: center;
}

.preview-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
}

.preview-placeholder p {
  margin: 4px 0;
  font-size: 14px;
}

.placeholder-subtitle {
  font-size: 12px;
  opacity: 0.7;
}
</style>
