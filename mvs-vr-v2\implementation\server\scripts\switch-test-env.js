#!/usr/bin/env node

/**
 * Test Environment Switcher
 * Usage: node scripts/switch-test-env.js [local|staging|production]
 */

const fs = require('fs');
const path = require('path');

const environments = ['local', 'staging', 'production'];
const envFilePath = path.join(__dirname, '../.env.test');

function updateEnvFile(environment) {
  if (!environments.includes(environment)) {
    console.error(`❌ Invalid environment: ${environment}`);
    console.error(`   Valid options: ${environments.join(', ')}`);
    process.exit(1);
  }

  try {
    // Read current .env.test file
    let envContent = fs.readFileSync(envFilePath, 'utf8');
    
    // Update TEST_ENV line
    envContent = envContent.replace(
      /^TEST_ENV=.*$/m,
      `TEST_ENV=${environment}`
    );
    
    // Write back to file
    fs.writeFileSync(envFilePath, envContent);
    
    console.log(`✅ Test environment switched to: ${environment}`);
    console.log(`📝 Updated: ${envFilePath}`);
    
    // Show current configuration
    showCurrentConfig(environment);
    
  } catch (error) {
    console.error(`❌ Error updating environment file:`, error.message);
    process.exit(1);
  }
}

function showCurrentConfig(environment) {
  const configs = {
    local: {
      server: 'http://localhost:3000',
      supabase: 'http://localhost:54321',
      redis: 'redis://localhost:6379',
      description: 'Local development with mocked services'
    },
    staging: {
      server: 'https://mvs.kanousai.com',
      supabase: 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
      redis: 'redis://staging-redis:6379',
      description: 'Staging environment with real services (safe for testing)'
    },
    production: {
      server: 'https://mvs.kanousai.com',
      supabase: 'https://production.supabase.co',
      redis: 'redis://production-redis:6379',
      description: '⚠️  Production environment (use with caution!)'
    }
  };
  
  const config = configs[environment];
  
  console.log('\n📋 Current Configuration:');
  console.log(`   Environment: ${environment}`);
  console.log(`   Description: ${config.description}`);
  console.log(`   Server: ${config.server}`);
  console.log(`   Supabase: ${config.supabase}`);
  console.log(`   Redis: ${config.redis}`);
  
  if (environment === 'production') {
    console.log('\n⚠️  WARNING: You are now targeting PRODUCTION!');
    console.log('   Please be extra careful when running tests.');
  }
  
  console.log('\n🚀 Run tests with: npm test');
  console.log('🔍 Run specific tests: npm test -- tests/unit/specific-test.ts');
}

function showUsage() {
  console.log('🔧 Test Environment Switcher');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/switch-test-env.js <environment>');
  console.log('');
  console.log('Environments:');
  console.log('  local      - Local development (mocked services)');
  console.log('  staging    - Staging server (real services, safe for testing)');
  console.log('  production - Production server (⚠️  use with caution)');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/switch-test-env.js local');
  console.log('  node scripts/switch-test-env.js staging');
  console.log('');
}

// Main execution
const environment = process.argv[2];

if (!environment) {
  showUsage();
  process.exit(1);
}

updateEnvFile(environment);
