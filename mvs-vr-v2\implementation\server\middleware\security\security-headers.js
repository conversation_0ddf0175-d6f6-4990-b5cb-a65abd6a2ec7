/**
 * Security Headers Middleware
 * Implements comprehensive security headers for web application protection
 */

class SecurityHeaders {
  constructor(options = {}) {
    this.options = {
      // Content Security Policy
      csp: {
        enabled: options.csp?.enabled !== false,
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
          ...options.csp?.directives
        },
        reportOnly: options.csp?.reportOnly || false,
        reportUri: options.csp?.reportUri
      },

      // HTTP Strict Transport Security
      hsts: {
        enabled: options.hsts?.enabled !== false,
        maxAge: options.hsts?.maxAge || 31536000, // 1 year
        includeSubDomains: options.hsts?.includeSubDomains !== false,
        preload: options.hsts?.preload || false
      },

      // X-Frame-Options
      frameOptions: {
        enabled: options.frameOptions?.enabled !== false,
        action: options.frameOptions?.action || 'DENY' // DENY, SAMEORIGIN, ALLOW-FROM
      },

      // X-Content-Type-Options
      noSniff: {
        enabled: options.noSniff?.enabled !== false
      },

      // X-XSS-Protection
      xssProtection: {
        enabled: options.xssProtection?.enabled !== false,
        mode: options.xssProtection?.mode || '1; mode=block'
      },

      // Referrer Policy
      referrerPolicy: {
        enabled: options.referrerPolicy?.enabled !== false,
        policy: options.referrerPolicy?.policy || 'strict-origin-when-cross-origin'
      },

      // Permissions Policy (Feature Policy)
      permissionsPolicy: {
        enabled: options.permissionsPolicy?.enabled !== false,
        directives: {
          camera: ['self'],
          microphone: ['self'],
          geolocation: ['self'],
          ...options.permissionsPolicy?.directives
        }
      },

      // Cross-Origin Embedder Policy
      coep: {
        enabled: options.coep?.enabled || false,
        policy: options.coep?.policy || 'require-corp'
      },

      // Cross-Origin Opener Policy
      coop: {
        enabled: options.coop?.enabled || false,
        policy: options.coop?.policy || 'same-origin'
      },

      // Cross-Origin Resource Policy
      corp: {
        enabled: options.corp?.enabled || false,
        policy: options.corp?.policy || 'same-origin'
      }
    };
  }

  /**
   * Build Content Security Policy header value
   */
  buildCSPHeader() {
    const directives = [];
    
    for (const [directive, sources] of Object.entries(this.options.csp.directives)) {
      const kebabDirective = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
      directives.push(`${kebabDirective} ${sources.join(' ')}`);
    }

    return directives.join('; ');
  }

  /**
   * Build HSTS header value
   */
  buildHSTSHeader() {
    let value = `max-age=${this.options.hsts.maxAge}`;
    
    if (this.options.hsts.includeSubDomains) {
      value += '; includeSubDomains';
    }
    
    if (this.options.hsts.preload) {
      value += '; preload';
    }
    
    return value;
  }

  /**
   * Build Permissions Policy header value
   */
  buildPermissionsPolicyHeader() {
    const directives = [];
    
    for (const [feature, allowlist] of Object.entries(this.options.permissionsPolicy.directives)) {
      const kebabFeature = feature.replace(/([A-Z])/g, '-$1').toLowerCase();
      const sources = allowlist.map(source => 
        source === 'self' ? 'self' : `"${source}"`
      ).join(' ');
      directives.push(`${kebabFeature}=(${sources})`);
    }

    return directives.join(', ');
  }

  /**
   * Security headers middleware
   */
  middleware() {
    return (req, res, next) => {
      try {
        // Content Security Policy
        if (this.options.csp.enabled) {
          const cspHeader = this.buildCSPHeader();
          const headerName = this.options.csp.reportOnly ? 
            'Content-Security-Policy-Report-Only' : 
            'Content-Security-Policy';
          res.setHeader(headerName, cspHeader);
        }

        // HTTP Strict Transport Security
        if (this.options.hsts.enabled && req.secure) {
          res.setHeader('Strict-Transport-Security', this.buildHSTSHeader());
        }

        // X-Frame-Options
        if (this.options.frameOptions.enabled) {
          res.setHeader('X-Frame-Options', this.options.frameOptions.action);
        }

        // X-Content-Type-Options
        if (this.options.noSniff.enabled) {
          res.setHeader('X-Content-Type-Options', 'nosniff');
        }

        // X-XSS-Protection
        if (this.options.xssProtection.enabled) {
          res.setHeader('X-XSS-Protection', this.options.xssProtection.mode);
        }

        // Referrer Policy
        if (this.options.referrerPolicy.enabled) {
          res.setHeader('Referrer-Policy', this.options.referrerPolicy.policy);
        }

        // Permissions Policy
        if (this.options.permissionsPolicy.enabled) {
          res.setHeader('Permissions-Policy', this.buildPermissionsPolicyHeader());
        }

        // Cross-Origin Embedder Policy
        if (this.options.coep.enabled) {
          res.setHeader('Cross-Origin-Embedder-Policy', this.options.coep.policy);
        }

        // Cross-Origin Opener Policy
        if (this.options.coop.enabled) {
          res.setHeader('Cross-Origin-Opener-Policy', this.options.coop.policy);
        }

        // Cross-Origin Resource Policy
        if (this.options.corp.enabled) {
          res.setHeader('Cross-Origin-Resource-Policy', this.options.corp.policy);
        }

        // Remove potentially dangerous headers
        res.removeHeader('X-Powered-By');
        res.removeHeader('Server');

        next();
      } catch (error) {
        console.error('Security Headers Error:', error);
        next(); // Continue even if security headers fail
      }
    };
  }

  /**
   * Get security headers status
   */
  getStatus() {
    return {
      csp: this.options.csp.enabled,
      hsts: this.options.hsts.enabled,
      frameOptions: this.options.frameOptions.enabled,
      noSniff: this.options.noSniff.enabled,
      xssProtection: this.options.xssProtection.enabled,
      referrerPolicy: this.options.referrerPolicy.enabled,
      permissionsPolicy: this.options.permissionsPolicy.enabled,
      coep: this.options.coep.enabled,
      coop: this.options.coop.enabled,
      corp: this.options.corp.enabled
    };
  }
}

module.exports = SecurityHeaders;
