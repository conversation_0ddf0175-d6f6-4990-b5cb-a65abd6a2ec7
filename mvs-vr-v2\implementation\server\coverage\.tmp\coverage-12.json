{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2063", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/sprint7-enhancements.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70457, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 70457, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 826, "endOffset": 12139, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2064", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/middleware/response-sanitization.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8063, "count": 1}], "isBlockCoverage": true}, {"functionName": "isSensitiveField", "ranges": [{"startOffset": 1622, "endOffset": 1883, "count": 0}], "isBlockCoverage": false}, {"functionName": "sanitizeData", "ranges": [{"startOffset": 2145, "endOffset": 3389, "count": 0}], "isBlockCoverage": false}, {"functionName": "findSensitiveFields", "ranges": [{"startOffset": 3598, "endOffset": 4169, "count": 0}], "isBlockCoverage": false}, {"functionName": "isAuthorizedForBypass", "ranges": [{"startOffset": 4363, "endOffset": 5109, "count": 0}], "isBlockCoverage": false}, {"functionName": "responseSanitization", "ranges": [{"startOffset": 5245, "endOffset": 8022, "count": 0}], "isBlockCoverage": false}]}]}