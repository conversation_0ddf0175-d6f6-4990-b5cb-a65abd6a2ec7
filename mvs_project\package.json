{"name": "mvs-vr", "version": "1.0.0", "description": "MVS-VR Testing Environment", "scripts": {"test": "npm run test:unit && npm run test:e2e", "test:unit": "vitest run", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "devDependencies": {"@playwright/test": "^1.51.1", "@supabase/supabase-js": "^2.49.4", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^20.11.0", "@types/sharp": "^0.32.0", "@types/uuid": "^10.0.0", "sharp": "^0.33.0", "typescript": "^5.3.0", "vitest": "^1.2.0"}}