import { defineConfig } from 'vitest/config';
import { createVuePlugin } from 'vite-plugin-vue2';

export default defineConfig({
  plugins: [createVuePlugin()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.setup.js'],
    include: ['**/*.{test,spec,vitest}.?(c|m)[jt]s?(x)', '**/tests/**/*.?(c|m)[jt]s?(x)'],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/cypress/**',
      '**/.{idea,git,cache,output,temp}/**',
      '**/{karma,rollup,webpack,vite,vitest,jest,ava,babel,nyc,cypress,tsup,build}.config.*',
      '**/.migration-backup/**',
      '**/migrate-jest-to-vitest.js',
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/**',
        'dist/**',
        'coverage/**',
        '**/*.d.ts',
        'vitest.config.js',
        'vitest.setup.js',
      ],
    },
    deps: {
      optimizer: {
        web: {
          include: ['vue', '@vue/test-utils', 'axios', 'vue-router'],
        },
      },
    },
    // Performance optimizations
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        minThreads: 1,
        maxThreads: 4,
      },
    },
    // Increase timeout for Vue component tests
    testTimeout: 10000,
  },
  resolve: {
    alias: {
      '@': '/src',
      // Ensure Vue 2 compatibility
      vue: 'vue/dist/vue.js',
    },
  },
});
