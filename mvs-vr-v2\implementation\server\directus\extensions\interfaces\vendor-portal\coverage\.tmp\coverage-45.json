{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 329, "endOffset": 450, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/tests/GuidedSetupService.mock.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7490, "count": 1}], "isBlockCoverage": true}, {"functionName": "GuidedSetupService", "ranges": [{"startOffset": 402, "endOffset": 660, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardingStatus", "ranges": [{"startOffset": 664, "endOffset": 943, "count": 0}], "isBlockCoverage": false}, {"functionName": "createOnboardingStatus", "ranges": [{"startOffset": 947, "endOffset": 1289, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOnboardingStatus", "ranges": [{"startOffset": 1293, "endOffset": 1512, "count": 0}], "isBlockCoverage": false}]}]}