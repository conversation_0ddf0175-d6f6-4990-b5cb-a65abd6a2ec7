import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import ShowroomLayoutEditor from '../src/components/VisualEditors/ShowroomLayoutEditor.vue';

// Mock the API
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
};

describe('ShowroomLayoutEditor Simple Test', () => {
  let wrapper;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock API responses
    mockApi.get.mockImplementation(url => {
      if (url.includes('products')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'product1', name: 'Product 1', category: 'cat1' },
              { id: 'product2', name: 'Product 2', category: 'cat2' },
            ],
          },
        });
      } else if (url.includes('categories')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'cat1', name: 'Category 1' },
              { id: 'cat2', name: 'Category 2' },
            ],
          },
        });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    // Mount component
    wrapper = mount(ShowroomLayoutEditor, {
      propsData: {
        vendorId: 'vendor1',
      },
      mocks: {
        $api: mockApi,
      },
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.showroom-layout-editor').exists()).toBe(true);
  });

  it('displays the editor title', () => {
    expect(wrapper.find('.editor-title').text()).toBe('Showroom Layout Editor');
  });

  it('has the correct initial layout data', () => {
    expect(wrapper.vm.layout.name).toBe('New Showroom');
    expect(wrapper.vm.layout.template).toBe('template_1');
    expect(wrapper.vm.layout.size).toBe('medium');
  });

  it('loads data on mount', async () => {
    // Wait for promises to resolve
    await wrapper.vm.$nextTick();

    // Check API calls
    expect(mockApi.get).toHaveBeenCalledWith(
      `/items/products?filter[vendor_id][_eq]=vendor1`,
    );
    expect(mockApi.get).toHaveBeenCalledWith('/items/categories');
  });
});
