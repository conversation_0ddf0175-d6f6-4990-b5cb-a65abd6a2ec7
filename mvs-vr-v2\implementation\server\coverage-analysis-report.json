{"overall": {}, "components": {}, "integrationTests": {"visual-editors-integration.test.ts": {"exists": true, "path": "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\tests\\integration\\visual-editors-integration.test.ts"}, "frontend-backend-communication.test.ts": {"exists": true, "path": "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\tests\\integration\\frontend-backend-communication.test.ts"}, "directus-supabase-integration.test.ts": {"exists": true, "path": "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\tests\\integration\\directus-supabase-integration.test.ts"}, "vendor-portal-auth-flow.test.ts": {"exists": true, "path": "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\tests\\integration\\vendor-portal-auth-flow.test.ts"}}, "recommendations": [{"type": "component", "priority": "medium", "message": "VisualEditors.vue has low test coverage (0% lines)"}, {"type": "component", "priority": "medium", "message": "ShowroomLayoutEditor.vue has low test coverage (0% lines)"}, {"type": "component", "priority": "medium", "message": "ProductConfigurator.vue has low test coverage (0% lines)"}, {"type": "component", "priority": "medium", "message": "MaterialTextureEditor.vue has low test coverage (0% lines)"}, {"type": "component", "priority": "medium", "message": "LightingEditor.vue has low test coverage (0% lines)"}, {"type": "component", "priority": "medium", "message": "AnimationEditor.vue has low test coverage (0% lines)"}], "visualEditors": {"VisualEditors.vue": {"lines": "0", "functions": "0", "branches": "0", "status": "No coverage data"}, "ShowroomLayoutEditor.vue": {"lines": "0", "functions": "0", "branches": "0", "status": "No coverage data"}, "ProductConfigurator.vue": {"lines": "0", "functions": "0", "branches": "0", "status": "No coverage data"}, "MaterialTextureEditor.vue": {"lines": "0", "functions": "0", "branches": "0", "status": "No coverage data"}, "LightingEditor.vue": {"lines": "0", "functions": "0", "branches": "0", "status": "No coverage data"}, "AnimationEditor.vue": {"lines": "0", "functions": "0", "branches": "0", "status": "No coverage data"}}}