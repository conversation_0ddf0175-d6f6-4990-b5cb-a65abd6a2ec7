/**
 * Redis Mock for Testing
 * Provides a simple in-memory Redis implementation for tests
 */

import { vi } from 'vitest';

export class RedisMock {
  private store: Map<string, string> = new Map();
  private expirations: Map<string, number> = new Map();

  // Basic Redis operations
  async get(key: string): Promise<string | null> {
    this.checkExpiration(key);
    return this.store.get(key) || null;
  }

  async set(key: string, value: string, options?: { EX?: number }): Promise<'OK'> {
    this.store.set(key, value);
    
    if (options?.EX) {
      const expirationTime = Date.now() + (options.EX * 1000);
      this.expirations.set(key, expirationTime);
    }
    
    return 'OK';
  }

  async del(key: string | string[]): Promise<number> {
    const keys = Array.isArray(key) ? key : [key];
    let deletedCount = 0;
    
    for (const k of keys) {
      if (this.store.has(k)) {
        this.store.delete(k);
        this.expirations.delete(k);
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  async exists(key: string): Promise<number> {
    this.checkExpiration(key);
    return this.store.has(key) ? 1 : 0;
  }

  async keys(pattern: string): Promise<string[]> {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const matchingKeys: string[] = [];
    
    for (const key of this.store.keys()) {
      this.checkExpiration(key);
      if (regex.test(key) && this.store.has(key)) {
        matchingKeys.push(key);
      }
    }
    
    return matchingKeys;
  }

  async expire(key: string, seconds: number): Promise<number> {
    if (!this.store.has(key)) {
      return 0;
    }
    
    const expirationTime = Date.now() + (seconds * 1000);
    this.expirations.set(key, expirationTime);
    return 1;
  }

  async ttl(key: string): Promise<number> {
    const expiration = this.expirations.get(key);
    if (!expiration) {
      return -1; // No expiration set
    }
    
    const remaining = Math.ceil((expiration - Date.now()) / 1000);
    return remaining > 0 ? remaining : -2; // Key expired
  }

  // Hash operations
  async hget(key: string, field: string): Promise<string | null> {
    const hash = this.store.get(key);
    if (!hash) return null;
    
    try {
      const parsed = JSON.parse(hash);
      return parsed[field] || null;
    } catch {
      return null;
    }
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    let hash: Record<string, string> = {};
    
    const existing = this.store.get(key);
    if (existing) {
      try {
        hash = JSON.parse(existing);
      } catch {
        // Invalid JSON, start fresh
      }
    }
    
    const isNewField = !(field in hash);
    hash[field] = value;
    this.store.set(key, JSON.stringify(hash));
    
    return isNewField ? 1 : 0;
  }

  async hdel(key: string, field: string): Promise<number> {
    const hash = this.store.get(key);
    if (!hash) return 0;
    
    try {
      const parsed = JSON.parse(hash);
      if (field in parsed) {
        delete parsed[field];
        this.store.set(key, JSON.stringify(parsed));
        return 1;
      }
    } catch {
      // Invalid JSON
    }
    
    return 0;
  }

  // List operations
  async lpush(key: string, ...values: string[]): Promise<number> {
    const existing = this.store.get(key);
    let list: string[] = [];
    
    if (existing) {
      try {
        list = JSON.parse(existing);
      } catch {
        list = [];
      }
    }
    
    list.unshift(...values);
    this.store.set(key, JSON.stringify(list));
    return list.length;
  }

  async rpop(key: string): Promise<string | null> {
    const existing = this.store.get(key);
    if (!existing) return null;
    
    try {
      const list = JSON.parse(existing);
      if (list.length === 0) return null;
      
      const value = list.pop();
      this.store.set(key, JSON.stringify(list));
      return value;
    } catch {
      return null;
    }
  }

  // Utility methods
  private checkExpiration(key: string): void {
    const expiration = this.expirations.get(key);
    if (expiration && Date.now() > expiration) {
      this.store.delete(key);
      this.expirations.delete(key);
    }
  }

  // Test utilities
  clear(): void {
    this.store.clear();
    this.expirations.clear();
  }

  size(): number {
    return this.store.size;
  }

  getAllKeys(): string[] {
    return Array.from(this.store.keys());
  }

  // Mock connection methods
  async connect(): Promise<void> {
    // Mock connection
  }

  async disconnect(): Promise<void> {
    // Mock disconnection
  }

  async ping(): Promise<'PONG'> {
    return 'PONG';
  }
}

/**
 * Create a Redis mock with Vitest spies
 */
export function createRedisMock(): RedisMock {
  const mock = new RedisMock();
  
  // Add spies to all methods for testing
  vi.spyOn(mock, 'get');
  vi.spyOn(mock, 'set');
  vi.spyOn(mock, 'del');
  vi.spyOn(mock, 'exists');
  vi.spyOn(mock, 'keys');
  vi.spyOn(mock, 'expire');
  vi.spyOn(mock, 'ttl');
  vi.spyOn(mock, 'hget');
  vi.spyOn(mock, 'hset');
  vi.spyOn(mock, 'hdel');
  vi.spyOn(mock, 'lpush');
  vi.spyOn(mock, 'rpop');
  vi.spyOn(mock, 'connect');
  vi.spyOn(mock, 'disconnect');
  vi.spyOn(mock, 'ping');
  
  return mock;
}

/**
 * Create a simple Redis mock for basic operations
 */
export function createSimpleRedisMock() {
  return {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    keys: vi.fn().mockResolvedValue([]),
    expire: vi.fn().mockResolvedValue(1),
    ttl: vi.fn().mockResolvedValue(-1),
    hget: vi.fn().mockResolvedValue(null),
    hset: vi.fn().mockResolvedValue(1),
    hdel: vi.fn().mockResolvedValue(1),
    lpush: vi.fn().mockResolvedValue(1),
    rpop: vi.fn().mockResolvedValue(null),
    connect: vi.fn().mockResolvedValue(undefined),
    disconnect: vi.fn().mockResolvedValue(undefined),
    ping: vi.fn().mockResolvedValue('PONG'),
  };
}
