/**
 * Configuration Service
 * 
 * This service manages system configuration for the MVS-VR platform.
 * It provides centralized configuration management with validation and caching.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);

// Configuration cache
let configCache = null;
let lastConfigLoad = null;
const CONFIG_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Configuration file paths
const CONFIG_PATHS = {
  system: path.join(__dirname, '../config/system.json'),
  api: path.join(__dirname, '../config/api.json'),
  security: path.join(__dirname, '../config/security.json'),
  database: path.join(__dirname, '../config/database.json'),
  storage: path.join(__dirname, '../config/storage.json')
};

// Default configuration
const DEFAULT_CONFIG = {
  system: {
    name: 'MVS-VR Platform',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    debug: process.env.NODE_ENV !== 'production',
    logLevel: process.env.LOG_LEVEL || 'info'
  },
  api: {
    port: parseInt(process.env.PORT || '3000', 10),
    host: process.env.HOST || '0.0.0.0',
    cors: {
      enabled: true,
      origins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000']
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100 // limit each IP to 100 requests per windowMs
    }
  },
  security: {
    jwt: {
      secret: process.env.JWT_SECRET || 'default-secret-change-in-production',
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    },
    encryption: {
      algorithm: 'aes-256-gcm',
      keyLength: 32
    },
    headers: {
      contentSecurityPolicy: true,
      xFrameOptions: 'DENY',
      xContentTypeOptions: 'nosniff'
    }
  },
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    database: process.env.DB_NAME || 'mvs_vr',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    ssl: process.env.DB_SSL === 'true',
    pool: {
      min: 2,
      max: 10,
      idle: 10000
    }
  },
  storage: {
    provider: process.env.STORAGE_PROVIDER || 'local',
    local: {
      uploadPath: process.env.UPLOAD_PATH || './uploads',
      maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '100000000', 10) // 100MB
    },
    s3: {
      bucket: process.env.S3_BUCKET || '',
      region: process.env.S3_REGION || 'us-east-1',
      accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || ''
    }
  }
};

/**
 * Load configuration from files
 * @returns {Promise<Object>} Configuration object
 */
async function loadConfiguration() {
  const config = { ...DEFAULT_CONFIG };
  
  // Load configuration files if they exist
  for (const [key, filePath] of Object.entries(CONFIG_PATHS)) {
    try {
      if (await existsAsync(filePath)) {
        const fileContent = await readFileAsync(filePath, 'utf8');
        const fileConfig = JSON.parse(fileContent);
        config[key] = { ...config[key], ...fileConfig };
      }
    } catch (error) {
      console.warn(`Warning: Could not load configuration file ${filePath}:`, error.message);
    }
  }
  
  return config;
}

/**
 * Get system configuration
 * @param {boolean} forceReload - Force reload from files
 * @returns {Promise<Object>} System configuration
 */
async function getSystemConfig(forceReload = false) {
  const now = Date.now();
  
  // Return cached config if valid and not forcing reload
  if (!forceReload && configCache && lastConfigLoad && (now - lastConfigLoad) < CONFIG_CACHE_TTL) {
    return configCache;
  }
  
  // Load configuration
  configCache = await loadConfiguration();
  lastConfigLoad = now;
  
  return configCache;
}

/**
 * Get specific configuration section
 * @param {string} section - Configuration section name
 * @returns {Promise<Object>} Configuration section
 */
async function getConfig(section) {
  const config = await getSystemConfig();
  return config[section] || {};
}

/**
 * Update configuration section
 * @param {string} section - Configuration section name
 * @param {Object} updates - Configuration updates
 * @returns {Promise<void>}
 */
async function updateConfig(section, updates) {
  const config = await getSystemConfig();
  
  // Update configuration
  config[section] = { ...config[section], ...updates };
  
  // Save to file
  const filePath = CONFIG_PATHS[section];
  if (filePath) {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!await existsAsync(dir)) {
      await fs.promises.mkdir(dir, { recursive: true });
    }
    
    await writeFileAsync(filePath, JSON.stringify(config[section], null, 2));
  }
  
  // Update cache
  configCache = config;
  lastConfigLoad = Date.now();
}

/**
 * Validate configuration
 * @param {Object} config - Configuration to validate
 * @returns {Object} Validation result
 */
function validateConfig(config) {
  const errors = [];
  const warnings = [];
  
  // Validate system configuration
  if (!config.system?.name) {
    errors.push('System name is required');
  }
  
  // Validate API configuration
  if (!config.api?.port || config.api.port < 1 || config.api.port > 65535) {
    errors.push('Valid API port is required (1-65535)');
  }
  
  // Validate security configuration
  if (!config.security?.jwt?.secret || config.security.jwt.secret === 'default-secret-change-in-production') {
    if (config.system?.environment === 'production') {
      errors.push('JWT secret must be changed in production');
    } else {
      warnings.push('JWT secret should be changed from default');
    }
  }
  
  // Validate database configuration
  if (!config.database?.host) {
    errors.push('Database host is required');
  }
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get configuration health status
 * @returns {Promise<Object>} Health status
 */
async function getHealthStatus() {
  try {
    const config = await getSystemConfig();
    const validation = validateConfig(config);
    
    return {
      status: validation.valid ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      validation,
      cache: {
        loaded: !!configCache,
        lastLoad: lastConfigLoad ? new Date(lastConfigLoad).toISOString() : null,
        age: lastConfigLoad ? Date.now() - lastConfigLoad : null
      }
    };
  } catch (error) {
    return {
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

/**
 * Clear configuration cache
 */
function clearCache() {
  configCache = null;
  lastConfigLoad = null;
}

module.exports = {
  getSystemConfig,
  getConfig,
  updateConfig,
  validateConfig,
  getHealthStatus,
  clearCache,
  DEFAULT_CONFIG
};
