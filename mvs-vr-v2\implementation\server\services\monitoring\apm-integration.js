/**
 * Application Performance Monitoring (APM) Integration Service
 * Provides comprehensive APM with OpenTelemetry, custom metrics, and performance optimization
 */

import { trace, metrics, context, SpanStatusCode, SpanKind } from '@opentelemetry/api';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics';
import { PrometheusExporter } from '@opentelemetry/exporter-prometheus';
import { JaegerExporter } from '@opentelemetry/exporter-jaeger';
import { Resource } from '@opentelemetry/resources';
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions';
import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';

export class APMIntegration extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      serviceName: options.serviceName || 'mvs-vr-server',
      serviceVersion: options.serviceVersion || '2.0.0',
      environment: options.environment || process.env.NODE_ENV || 'development',
      enableTracing: options.enableTracing !== false,
      enableMetrics: options.enableMetrics !== false,
      enableProfiling: options.enableProfiling || false,
      jaegerEndpoint: options.jaegerEndpoint || 'http://localhost:14268/api/traces',
      prometheusPort: options.prometheusPort || 9464,
      samplingRate: options.samplingRate || 1.0,
      enableCustomMetrics: options.enableCustomMetrics !== false,
      enablePerformanceOptimization: options.enablePerformanceOptimization !== false,
      ...options
    };

    // APM components
    this.sdk = null;
    this.tracer = null;
    this.meter = null;
    this.prometheusExporter = null;
    
    // Performance tracking
    this.performanceMetrics = new Map();
    this.activeSpans = new Map();
    this.performanceBaselines = new Map();
    
    // Custom metrics
    this.customMetrics = {
      httpRequests: null,
      httpDuration: null,
      websocketConnections: null,
      websocketMessages: null,
      databaseQueries: null,
      cacheHits: null,
      errorRate: null,
      memoryUsage: null,
      cpuUsage: null,
      customCounters: new Map(),
      customHistograms: new Map(),
      customGauges: new Map()
    };

    // Performance optimization
    this.optimizationRules = new Map();
    this.performanceAlerts = new Map();
    this.slowOperations = new Map();

    this.initialize();
  }

  async initialize() {
    try {
      console.log('🔍 Initializing APM Integration...');
      
      // Setup OpenTelemetry SDK
      await this.setupOpenTelemetry();
      
      // Initialize custom metrics
      if (this.options.enableCustomMetrics) {
        this.initializeCustomMetrics();
      }
      
      // Setup performance optimization
      if (this.options.enablePerformanceOptimization) {
        this.setupPerformanceOptimization();
      }
      
      // Start performance monitoring
      this.startPerformanceMonitoring();
      
      console.log('✅ APM Integration initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize APM Integration:', error);
      this.emit('error', error);
    }
  }

  async setupOpenTelemetry() {
    // Create resource
    const resource = new Resource({
      [SemanticResourceAttributes.SERVICE_NAME]: this.options.serviceName,
      [SemanticResourceAttributes.SERVICE_VERSION]: this.options.serviceVersion,
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: this.options.environment,
    });

    // Setup exporters
    const exporters = [];
    
    if (this.options.enableTracing) {
      exporters.push(new JaegerExporter({
        endpoint: this.options.jaegerEndpoint,
      }));
    }

    // Setup Prometheus exporter for metrics
    if (this.options.enableMetrics) {
      this.prometheusExporter = new PrometheusExporter({
        port: this.options.prometheusPort,
      });
    }

    // Initialize SDK
    this.sdk = new NodeSDK({
      resource,
      traceExporter: exporters.length > 0 ? exporters[0] : undefined,
      metricReader: this.prometheusExporter ? new PeriodicExportingMetricReader({
        exporter: this.prometheusExporter,
        exportIntervalMillis: 5000,
      }) : undefined,
      instrumentations: [
        getNodeAutoInstrumentations({
          '@opentelemetry/instrumentation-fs': { enabled: false },
          '@opentelemetry/instrumentation-http': {
            enabled: true,
            requestHook: (span, request) => {
              this.enhanceHttpSpan(span, request);
            },
          },
          '@opentelemetry/instrumentation-express': { enabled: true },
          '@opentelemetry/instrumentation-redis': { enabled: true },
        }),
      ],
    });

    // Start SDK
    await this.sdk.start();

    // Get tracer and meter
    this.tracer = trace.getTracer(this.options.serviceName, this.options.serviceVersion);
    this.meter = metrics.getMeter(this.options.serviceName, this.options.serviceVersion);
  }

  initializeCustomMetrics() {
    // HTTP metrics
    this.customMetrics.httpRequests = this.meter.createCounter('http_requests_total', {
      description: 'Total number of HTTP requests',
    });

    this.customMetrics.httpDuration = this.meter.createHistogram('http_request_duration_seconds', {
      description: 'HTTP request duration in seconds',
      boundaries: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
    });

    // WebSocket metrics
    this.customMetrics.websocketConnections = this.meter.createUpDownCounter('websocket_connections_active', {
      description: 'Number of active WebSocket connections',
    });

    this.customMetrics.websocketMessages = this.meter.createCounter('websocket_messages_total', {
      description: 'Total number of WebSocket messages',
    });

    // Database metrics
    this.customMetrics.databaseQueries = this.meter.createHistogram('database_query_duration_seconds', {
      description: 'Database query duration in seconds',
      boundaries: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
    });

    // Cache metrics
    this.customMetrics.cacheHits = this.meter.createCounter('cache_operations_total', {
      description: 'Total number of cache operations',
    });

    // Error metrics
    this.customMetrics.errorRate = this.meter.createCounter('errors_total', {
      description: 'Total number of errors',
    });

    // System metrics
    this.customMetrics.memoryUsage = this.meter.createObservableGauge('memory_usage_bytes', {
      description: 'Memory usage in bytes',
    });

    this.customMetrics.cpuUsage = this.meter.createObservableGauge('cpu_usage_percent', {
      description: 'CPU usage percentage',
    });

    // Setup observable metrics callbacks
    this.customMetrics.memoryUsage.addCallback((result) => {
      const memUsage = process.memoryUsage();
      result.observe(memUsage.heapUsed, { type: 'heap_used' });
      result.observe(memUsage.heapTotal, { type: 'heap_total' });
      result.observe(memUsage.rss, { type: 'rss' });
      result.observe(memUsage.external, { type: 'external' });
    });

    this.customMetrics.cpuUsage.addCallback((result) => {
      const cpuUsage = process.cpuUsage();
      result.observe(cpuUsage.user / 1000000, { type: 'user' }); // Convert to seconds
      result.observe(cpuUsage.system / 1000000, { type: 'system' });
    });
  }

  setupPerformanceOptimization() {
    // Define optimization rules
    this.optimizationRules.set('slow_http_request', {
      threshold: 1000, // 1 second
      action: 'alert',
      description: 'HTTP request taking longer than 1 second'
    });

    this.optimizationRules.set('high_memory_usage', {
      threshold: 500 * 1024 * 1024, // 500MB
      action: 'optimize',
      description: 'Memory usage exceeding 500MB'
    });

    this.optimizationRules.set('high_error_rate', {
      threshold: 0.05, // 5%
      action: 'alert',
      description: 'Error rate exceeding 5%'
    });

    this.optimizationRules.set('slow_database_query', {
      threshold: 500, // 500ms
      action: 'optimize',
      description: 'Database query taking longer than 500ms'
    });
  }

  startPerformanceMonitoring() {
    // Monitor performance metrics every 30 seconds
    setInterval(() => {
      this.collectPerformanceMetrics();
      this.analyzePerformance();
      this.optimizePerformance();
    }, 30000);

    // Monitor slow operations every 5 seconds
    setInterval(() => {
      this.monitorSlowOperations();
    }, 5000);
  }

  /**
   * Create a custom span for tracing
   * @param {string} name - Span name
   * @param {Object} options - Span options
   * @param {Function} fn - Function to execute within span
   */
  async createSpan(name, options = {}, fn) {
    if (!this.tracer) {
      return fn ? await fn() : null;
    }

    const span = this.tracer.startSpan(name, {
      kind: options.kind || SpanKind.INTERNAL,
      attributes: options.attributes || {},
    });

    const spanId = span.spanContext().spanId;
    this.activeSpans.set(spanId, {
      name,
      startTime: performance.now(),
      attributes: options.attributes || {}
    });

    try {
      const result = fn ? await context.with(trace.setSpan(context.active(), span), fn) : null;
      span.setStatus({ code: SpanStatusCode.OK });
      return result;
    } catch (error) {
      span.setStatus({
        code: SpanStatusCode.ERROR,
        message: error.message,
      });
      span.recordException(error);
      throw error;
    } finally {
      const endTime = performance.now();
      const duration = endTime - this.activeSpans.get(spanId)?.startTime || 0;
      
      span.setAttributes({
        'performance.duration_ms': duration,
        'performance.end_time': endTime,
      });
      
      span.end();
      this.activeSpans.delete(spanId);
      
      // Check for performance issues
      this.checkPerformanceThresholds(name, duration, options.attributes);
    }
  }

  /**
   * Track HTTP request metrics
   * @param {string} method - HTTP method
   * @param {string} route - Route path
   * @param {number} statusCode - Response status code
   * @param {number} duration - Request duration in ms
   */
  trackHttpRequest(method, route, statusCode, duration) {
    const labels = { method, route, status: statusCode.toString() };
    
    this.customMetrics.httpRequests?.add(1, labels);
    this.customMetrics.httpDuration?.record(duration / 1000, labels);
    
    // Track errors
    if (statusCode >= 400) {
      this.customMetrics.errorRate?.add(1, { type: 'http', status: statusCode.toString() });
    }
    
    // Check performance thresholds
    this.checkPerformanceThresholds('http_request', duration, labels);
  }

  /**
   * Track WebSocket metrics
   * @param {string} event - Event type (connect, disconnect, message)
   * @param {Object} metadata - Additional metadata
   */
  trackWebSocketEvent(event, metadata = {}) {
    const labels = { event, ...metadata };
    
    switch (event) {
      case 'connect':
        this.customMetrics.websocketConnections?.add(1);
        break;
      case 'disconnect':
        this.customMetrics.websocketConnections?.add(-1);
        break;
      case 'message':
        this.customMetrics.websocketMessages?.add(1, labels);
        break;
    }
  }

  /**
   * Track database query performance
   * @param {string} operation - Database operation
   * @param {string} table - Table name
   * @param {number} duration - Query duration in ms
   */
  trackDatabaseQuery(operation, table, duration) {
    const labels = { operation, table };
    
    this.customMetrics.databaseQueries?.record(duration / 1000, labels);
    
    // Check for slow queries
    this.checkPerformanceThresholds('database_query', duration, labels);
  }

  /**
   * Track cache operations
   * @param {string} operation - Cache operation (hit, miss, set, delete)
   * @param {string} key - Cache key
   */
  trackCacheOperation(operation, key) {
    const labels = { operation, cache_type: this.getCacheType(key) };
    this.customMetrics.cacheHits?.add(1, labels);
  }

  /**
   * Create custom counter metric
   * @param {string} name - Metric name
   * @param {string} description - Metric description
   */
  createCustomCounter(name, description) {
    if (!this.customMetrics.customCounters.has(name)) {
      const counter = this.meter.createCounter(name, { description });
      this.customMetrics.customCounters.set(name, counter);
    }
    return this.customMetrics.customCounters.get(name);
  }

  /**
   * Create custom histogram metric
   * @param {string} name - Metric name
   * @param {string} description - Metric description
   * @param {Array} boundaries - Histogram boundaries
   */
  createCustomHistogram(name, description, boundaries) {
    if (!this.customMetrics.customHistograms.has(name)) {
      const histogram = this.meter.createHistogram(name, { description, boundaries });
      this.customMetrics.customHistograms.set(name, histogram);
    }
    return this.customMetrics.customHistograms.get(name);
  }

  /**
   * Create custom gauge metric
   * @param {string} name - Metric name
   * @param {string} description - Metric description
   */
  createCustomGauge(name, description) {
    if (!this.customMetrics.customGauges.has(name)) {
      const gauge = this.meter.createObservableGauge(name, { description });
      this.customMetrics.customGauges.set(name, gauge);
    }
    return this.customMetrics.customGauges.get(name);
  }

  enhanceHttpSpan(span, request) {
    // Add custom attributes to HTTP spans
    span.setAttributes({
      'http.user_agent': request.headers['user-agent'] || 'unknown',
      'http.content_length': request.headers['content-length'] || '0',
      'http.request_id': request.headers['x-request-id'] || 'unknown',
    });
  }

  checkPerformanceThresholds(operationType, duration, attributes = {}) {
    const rule = this.optimizationRules.get(`slow_${operationType}`);
    if (rule && duration > rule.threshold) {
      this.handlePerformanceIssue(operationType, duration, rule, attributes);
    }
  }

  handlePerformanceIssue(operationType, duration, rule, attributes) {
    const issue = {
      type: operationType,
      duration,
      threshold: rule.threshold,
      description: rule.description,
      attributes,
      timestamp: Date.now(),
    };

    this.performanceAlerts.set(`${operationType}_${Date.now()}`, issue);
    
    this.emit('performanceIssue', issue);
    
    if (rule.action === 'optimize') {
      this.triggerOptimization(operationType, issue);
    }
  }

  triggerOptimization(operationType, issue) {
    // Implement optimization strategies based on operation type
    switch (operationType) {
      case 'http_request':
        this.optimizeHttpPerformance(issue);
        break;
      case 'database_query':
        this.optimizeDatabasePerformance(issue);
        break;
      case 'memory_usage':
        this.optimizeMemoryUsage(issue);
        break;
    }
  }

  optimizeHttpPerformance(issue) {
    // Implement HTTP performance optimizations
    console.log(`🚀 Optimizing HTTP performance for ${issue.attributes?.route || 'unknown route'}`);
    // Could implement caching, compression, etc.
  }

  optimizeDatabasePerformance(issue) {
    // Implement database performance optimizations
    console.log(`🚀 Optimizing database performance for ${issue.attributes?.table || 'unknown table'}`);
    // Could implement query optimization, indexing suggestions, etc.
  }

  optimizeMemoryUsage(issue) {
    // Implement memory optimization
    console.log('🚀 Triggering garbage collection for memory optimization');
    if (global.gc) {
      global.gc();
    }
  }

  collectPerformanceMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.performanceMetrics.set('memory', {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      rss: memUsage.rss,
      external: memUsage.external,
      timestamp: Date.now(),
    });
    
    this.performanceMetrics.set('cpu', {
      user: cpuUsage.user,
      system: cpuUsage.system,
      timestamp: Date.now(),
    });
  }

  analyzePerformance() {
    // Analyze collected metrics for trends and anomalies
    const memMetrics = this.performanceMetrics.get('memory');
    if (memMetrics && memMetrics.heapUsed > 500 * 1024 * 1024) { // 500MB
      this.handlePerformanceIssue('memory_usage', memMetrics.heapUsed, 
        this.optimizationRules.get('high_memory_usage'), { type: 'heap' });
    }
  }

  optimizePerformance() {
    // Implement automatic performance optimizations
    this.cleanupOldMetrics();
    this.cleanupOldAlerts();
  }

  monitorSlowOperations() {
    // Monitor for operations that are taking too long
    for (const [spanId, spanInfo] of this.activeSpans) {
      const duration = performance.now() - spanInfo.startTime;
      if (duration > 5000) { // 5 seconds
        console.warn(`⚠️ Long-running operation detected: ${spanInfo.name} (${duration.toFixed(2)}ms)`);
      }
    }
  }

  cleanupOldMetrics() {
    const cutoff = Date.now() - 3600000; // 1 hour
    for (const [key, metric] of this.performanceMetrics) {
      if (metric.timestamp < cutoff) {
        this.performanceMetrics.delete(key);
      }
    }
  }

  cleanupOldAlerts() {
    const cutoff = Date.now() - 1800000; // 30 minutes
    for (const [key, alert] of this.performanceAlerts) {
      if (alert.timestamp < cutoff) {
        this.performanceAlerts.delete(key);
      }
    }
  }

  getCacheType(key) {
    if (key.startsWith('user:')) return 'user';
    if (key.startsWith('session:')) return 'session';
    if (key.startsWith('asset:')) return 'asset';
    return 'general';
  }

  getMetrics() {
    return {
      activeSpans: this.activeSpans.size,
      performanceAlerts: this.performanceAlerts.size,
      performanceMetrics: this.performanceMetrics.size,
      customMetrics: {
        counters: this.customMetrics.customCounters.size,
        histograms: this.customMetrics.customHistograms.size,
        gauges: this.customMetrics.customGauges.size,
      },
      optimizationRules: this.optimizationRules.size,
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down APM Integration...');
    
    if (this.sdk) {
      await this.sdk.shutdown();
    }
    
    this.activeSpans.clear();
    this.performanceMetrics.clear();
    this.performanceAlerts.clear();
    
    console.log('✅ APM Integration shutdown complete');
  }
}

export default APMIntegration;
