{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/TeamMemberManagement.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49267, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 909, "endOffset": 1373, "count": 5}], "isBlockCoverage": true}, {"functionName": "loadTeamMembers", "ranges": [{"startOffset": 1392, "endOffset": 1777, "count": 0}], "isBlockCoverage": false}, {"functionName": "addMember", "ranges": [{"startOffset": 1788, "endOffset": 2312, "count": 1}, {"startOffset": 1881, "endOffset": 2148, "count": 0}, {"startOffset": 2157, "endOffset": 2306, "count": 0}], "isBlockCoverage": true}, {"functionName": "updateMember", "ranges": [{"startOffset": 2323, "endOffset": 3018, "count": 1}, {"startOffset": 2421, "endOffset": 2446, "count": 0}, {"startOffset": 2633, "endOffset": 2851, "count": 0}, {"startOffset": 2860, "endOffset": 2962, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2686, "endOffset": 2711, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeMember", "ranges": [{"startOffset": 3029, "endOffset": 3513, "count": 1}, {"startOffset": 3355, "endOffset": 3457, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3238, "endOffset": 3261, "count": 1}], "isBlockCoverage": true}, {"functionName": "editMember", "ranges": [{"startOffset": 3524, "endOffset": 3592, "count": 1}], "isBlockCoverage": true}, {"functionName": "cancelEdit", "ranges": [{"startOffset": 3603, "endOffset": 3656, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetNewMember", "ranges": [{"startOffset": 3667, "endOffset": 3786, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateMember", "ranges": [{"startOffset": 3797, "endOffset": 4119, "count": 2}, {"startOffset": 3879, "endOffset": 3958, "count": 0}, {"startOffset": 4011, "endOffset": 4118, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateEmail", "ranges": [{"startOffset": 4130, "endOffset": 4234, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6212, "endOffset": 9005, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6271, "endOffset": 6405, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6461, "endOffset": 6630, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6698, "endOffset": 6834, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6897, "endOffset": 7720, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7802, "endOffset": 8346, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8436, "endOffset": 9001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8963, "endOffset": 8978, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/test-utils/component-test-utils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "createWrapper", "ranges": [{"startOffset": 902, "endOffset": 1797, "count": 5}, {"startOffset": 995, "endOffset": 1031, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1899, "endOffset": 1928, "count": 5}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2060, "endOffset": 2134, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2231, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "wait", "ranges": [{"startOffset": 2440, "endOffset": 2519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2612, "endOffset": 2632, "count": 0}], "isBlockCoverage": false}, {"functionName": "findAll", "ranges": [{"startOffset": 2982, "endOffset": 3573, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3669, "endOffset": 3692, "count": 0}], "isBlockCoverage": false}, {"functionName": "trigger", "ranges": [{"startOffset": 4111, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4800, "endOffset": 4823, "count": 0}], "isBlockCoverage": false}, {"functionName": "setValue", "ranges": [{"startOffset": 5049, "endOffset": 5320, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5417, "endOffset": 5441, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasClass", "ranges": [{"startOffset": 5652, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6246, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}]}]}