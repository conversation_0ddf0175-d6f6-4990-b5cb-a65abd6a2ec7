{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/tests/interface-wizard-integration.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35075, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35075, "count": 1}, {"startOffset": 2229, "endOffset": 35074, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 382, "endOffset": 508, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 430, "endOffset": 482, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 558, "endOffset": 660, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 606, "endOffset": 657, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 707, "endOffset": 803, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 752, "endOffset": 800, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 849, "endOffset": 943, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 893, "endOffset": 940, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 991, "endOffset": 1089, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1037, "endOffset": 1086, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1138, "endOffset": 1238, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1185, "endOffset": 1235, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1290, "endOffset": 1396, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1340, "endOffset": 1393, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1444, "endOffset": 1542, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1490, "endOffset": 1539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1590, "endOffset": 1688, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1636, "endOffset": 1685, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1748, "endOffset": 1865, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1792, "endOffset": 1839, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1919, "endOffset": 2029, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1971, "endOffset": 2026, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2534, "endOffset": 8025, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/interface.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35581, "count": 1}, {"startOffset": 12209, "endOffset": 12253, "count": 0}, {"startOffset": 12254, "endOffset": 35580, "count": 0}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 4104, "endOffset": 4114, "count": 0}], "isBlockCoverage": false}, {"functionName": "data", "ranges": [{"startOffset": 4129, "endOffset": 4371, "count": 0}], "isBlockCoverage": false}, {"functionName": "availableTabs", "ranges": [{"startOffset": 4392, "endOffset": 5907, "count": 0}], "isBlockCoverage": false}, {"functionName": "vendorId", "ranges": [{"startOffset": 5914, "endOffset": 5997, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 6006, "endOffset": 6042, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 6062, "endOffset": 7558, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadProducts", "ranges": [{"startOffset": 7565, "endOffset": 7890, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadCategories", "ranges": [{"startOffset": 7897, "endOffset": 8121, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadClients", "ranges": [{"startOffset": 8128, "endOffset": 9208, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadShowrooms", "ranges": [{"startOffset": 9215, "endOffset": 9545, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadSubscription", "ranges": [{"startOffset": 9552, "endOffset": 10041, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadBranding", "ranges": [{"startOffset": 10048, "endOffset": 10306, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnalytics", "ranges": [{"startOffset": 10313, "endOffset": 10509, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnalytics", "ranges": [{"startOffset": 10516, "endOffset": 11431, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentVendorId", "ranges": [{"startOffset": 11438, "endOffset": 11575, "count": 0}], "isBlockCoverage": false}, {"functionName": "activeTab", "ranges": [{"startOffset": 11597, "endOffset": 11639, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 11923, "endOffset": 12029, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12446, "endOffset": 12536, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12625, "endOffset": 12709, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12758, "endOffset": 12802, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/AnalyticsChart.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24148, "count": 1}], "isBlockCoverage": true}, {"functionName": "validator", "ranges": [{"startOffset": 1934, "endOffset": 1995, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 2050, "endOffset": 2060, "count": 0}], "isBlockCoverage": false}, {"functionName": "setup", "ranges": [{"startOffset": 2199, "endOffset": 8415, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 8693, "endOffset": 8799, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9227, "endOffset": 9316, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9421, "endOffset": 9504, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9553, "endOffset": 9597, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1167", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/AnalyticsChart.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11505, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1823, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1974, "endOffset": 1996, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2103, "endOffset": 2134, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1168", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/AnalyticsChart.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 8}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1170", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 8}, {"startOffset": 543, "endOffset": 553, "count": 7}, {"startOffset": 553, "endOffset": 597, "count": 1}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 611, "endOffset": 616, "count": 1}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 812, "endOffset": 904, "count": 1}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 8}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 8}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 8}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 16}, {"startOffset": 2728, "endOffset": 2796, "count": 8}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 8}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1171", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10653, "count": 1}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 2427, "endOffset": 2437, "count": 0}], "isBlockCoverage": false}, {"functionName": "setup", "ranges": [{"startOffset": 2454, "endOffset": 3346, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 3623, "endOffset": 3729, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4163, "endOffset": 4252, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4362, "endOffset": 4445, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4494, "endOffset": 4538, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1172", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/LivePreview.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13653, "count": 1}], "isBlockCoverage": true}, {"functionName": "validator", "ranges": [{"startOffset": 2125, "endOffset": 2180, "count": 0}], "isBlockCoverage": false}, {"functionName": "validator", "ranges": [{"startOffset": 2272, "endOffset": 2336, "count": 0}], "isBlockCoverage": false}, {"functionName": "validator", "ranges": [{"startOffset": 2434, "endOffset": 2486, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 2675, "endOffset": 2685, "count": 0}], "isBlockCoverage": false}, {"functionName": "setup", "ranges": [{"startOffset": 2831, "endOffset": 4567, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 4844, "endOffset": 4950, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5375, "endOffset": 5464, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5566, "endOffset": 5649, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5698, "endOffset": 5742, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1173", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/contexts/PreviewContext.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33224, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33224, "count": 1}], "isBlockCoverage": true}, {"functionName": "providePreviewContext", "ranges": [{"startOffset": 773, "endOffset": 6357, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6467, "endOffset": 6504, "count": 0}], "isBlockCoverage": false}, {"functionName": "usePreviewContext", "ranges": [{"startOffset": 6706, "endOffset": 6966, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7072, "endOffset": 7105, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1174", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/shared/utils/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setLogLevel", "ranges": [{"startOffset": 596, "endOffset": 762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLogLevel", "ranges": [{"startOffset": 844, "endOffset": 896, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1021, "endOffset": 1188, "count": 0}], "isBlockCoverage": false}, {"functionName": "info", "ranges": [{"startOffset": 1313, "endOffset": 1476, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1603, "endOffset": 1766, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1892, "endOffset": 2059, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1175", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewFrame.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21138, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21138, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 1961, "endOffset": 5074, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 5351, "endOffset": 5457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5883, "endOffset": 5972, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6075, "endOffset": 6158, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6207, "endOffset": 6251, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1176", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewFrame.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31126, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 5108, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5259, "endOffset": 5281, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5388, "endOffset": 5419, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1177", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewFrame.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 554, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1178", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewControls.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23409, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23409, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 1791, "endOffset": 6799, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 7076, "endOffset": 7182, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7611, "endOffset": 7700, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7806, "endOffset": 7889, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7938, "endOffset": 7982, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1179", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewControls.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19415, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19415, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 3169, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3320, "endOffset": 3342, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3449, "endOffset": 3480, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1180", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewControls.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1181", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/LivePreview.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8741, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1087, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 1112, "endOffset": 1288, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 1290, "endOffset": 1472, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1598, "endOffset": 1620, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1727, "endOffset": 1758, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1182", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/LivePreview.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 554, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1183", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/DevicePreview.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42390, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42390, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 2327, "endOffset": 8912, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 9189, "endOffset": 9295, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9722, "endOffset": 9811, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9915, "endOffset": 9998, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10047, "endOffset": 10091, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1184", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/DevicePreview.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 65062, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 65062, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 9621, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 9646, "endOffset": 9852, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 9854, "endOffset": 10066, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 10068, "endOffset": 10275, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 10277, "endOffset": 10489, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10615, "endOffset": 10637, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 10744, "endOffset": 10775, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1185", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/DevicePreview.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1186", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/ABTestingFramework.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52035, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52035, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 2338, "endOffset": 9651, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 9928, "endOffset": 10034, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10466, "endOffset": 10555, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10664, "endOffset": 10747, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10796, "endOffset": 10840, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1187", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/ABTestingFramework.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 109531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 109531, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 16921, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 16946, "endOffset": 17166, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 17292, "endOffset": 17314, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 17421, "endOffset": 17452, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1188", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/ABTestingFramework.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1189", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PerformanceTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43845, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43845, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 2055, "endOffset": 8791, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 9068, "endOffset": 9174, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9611, "endOffset": 9700, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9814, "endOffset": 9897, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9946, "endOffset": 9990, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1190", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PerformanceTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70500, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 70500, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 10729, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 10754, "endOffset": 11179, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11305, "endOffset": 11327, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 11434, "endOffset": 11465, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1191", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PerformanceTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 570, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1192", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10639, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10639, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1729, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1880, "endOffset": 1902, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2009, "endOffset": 2040, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1193", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1194", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/interface.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21441, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 3443, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3594, "endOffset": 3616, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3723, "endOffset": 3754, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1195", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/interface.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 550, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 550, "count": 1}], "isBlockCoverage": true}]}]}