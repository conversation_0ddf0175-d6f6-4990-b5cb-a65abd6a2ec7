import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Create a minimal ProductConfigurator component for testing
const MinimalProductConfigurator = {
  name: 'ProductConfigurator',
  props: {
    vendorId: {
      type: String,
      required: true,
    },
    productId: {
      type: String,
      default: null,
    },
  },
  template: `
    <div class="product-configurator-wrapper">
      <div class="product-configurator">
        <div class="editor-header">
          <h3>Product Configurator</h3>
        </div>
        <div class="editor-content">
          <p>Minimal test component</p>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      products: [],
      selectedProduct: null,
    };
  },
};

// Mock API
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
};

describe('ProductConfigurator Minimal', () => {
  let wrapper;

  beforeEach(() => {
    vi.clearAllMocks();

    mockApi.get.mockResolvedValue({
      data: { data: [] },
    });

    wrapper = mount(MinimalProductConfigurator, {
      propsData: {
        vendorId: 'vendor1',
      },
      mocks: {
        $api: mockApi,
      },
    });
  });

  it('should mount successfully', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.product-configurator-wrapper').exists()).toBe(true);
  });

  it('should display the header', () => {
    expect(wrapper.find('h3').text()).toBe('Product Configurator');
  });

  it('should have the correct vendor ID prop', () => {
    expect(wrapper.vm.vendorId).toBe('vendor1');
  });
});
