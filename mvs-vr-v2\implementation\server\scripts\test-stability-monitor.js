#!/usr/bin/env node

/**
 * Test Stability Monitor
 * 
 * This script monitors the stability of our core passing tests and alerts
 * if any of them start failing. It's designed to run in CI/CD pipelines
 * and during development to ensure we maintain our stable test foundation.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Define our stable test suites that must always pass
const STABLE_TESTS = [
  'tests/unit/api-key-middleware.test.ts',
  'tests/unit/simple-rate-limit.test.ts', 
  'tests/unit/simple-vitest.test.ts',
  'tests/unit/visual-editors/visual-editors-api.test.ts'
];

// Expected test counts for each suite
const EXPECTED_COUNTS = {
  'tests/unit/api-key-middleware.test.ts': 13,
  'tests/unit/simple-rate-limit.test.ts': 3,
  'tests/unit/simple-vitest.test.ts': 2,
  'tests/unit/visual-editors/visual-editors-api.test.ts': 13
};

/**
 * Run stability check for our core tests
 */
async function runStabilityCheck() {
  console.log('🔍 Running Test Stability Check...\n');
  
  let allPassed = true;
  const results = [];
  
  for (const testFile of STABLE_TESTS) {
    console.log(`Testing: ${testFile}`);
    
    try {
      // Run the specific test file
      const output = execSync(`npm test -- ${testFile} --run --reporter=json`, {
        encoding: 'utf8',
        stdio: 'pipe'
      });
      
      // Parse the JSON output to get test results
      const lines = output.split('\n');
      let jsonResult = null;
      
      for (const line of lines) {
        try {
          const parsed = JSON.parse(line);
          if (parsed.testResults) {
            jsonResult = parsed;
            break;
          }
        } catch (e) {
          // Continue looking for valid JSON
        }
      }
      
      if (jsonResult) {
        const testResult = jsonResult.testResults[0];
        const passedCount = testResult.assertionResults.filter(r => r.status === 'passed').length;
        const expectedCount = EXPECTED_COUNTS[testFile];
        
        if (passedCount === expectedCount) {
          console.log(`✅ PASSED: ${passedCount}/${expectedCount} tests\n`);
          results.push({ file: testFile, status: 'PASSED', count: passedCount });
        } else {
          console.log(`❌ FAILED: ${passedCount}/${expectedCount} tests\n`);
          allPassed = false;
          results.push({ file: testFile, status: 'FAILED', count: passedCount, expected: expectedCount });
        }
      } else {
        // Fallback: check if command succeeded
        console.log(`✅ PASSED: Command succeeded\n`);
        results.push({ file: testFile, status: 'PASSED', count: 'unknown' });
      }
      
    } catch (error) {
      console.log(`❌ FAILED: Test execution failed`);
      console.log(`Error: ${error.message}\n`);
      allPassed = false;
      results.push({ file: testFile, status: 'FAILED', error: error.message });
    }
  }
  
  // Generate summary report
  console.log('📊 STABILITY CHECK SUMMARY');
  console.log('=' .repeat(50));
  
  results.forEach(result => {
    const status = result.status === 'PASSED' ? '✅' : '❌';
    console.log(`${status} ${result.file}: ${result.count} tests`);
  });
  
  console.log('=' .repeat(50));
  
  if (allPassed) {
    console.log('🎉 ALL STABLE TESTS PASSING - Foundation is stable!');
    return 0;
  } else {
    console.log('⚠️  STABILITY ISSUE DETECTED - Some core tests are failing!');
    console.log('Please fix failing tests before proceeding with development.');
    return 1;
  }
}

/**
 * Generate stability report for documentation
 */
function generateStabilityReport(results) {
  const timestamp = new Date().toISOString();
  const reportPath = path.join(__dirname, '../docs/test-stability-report.md');
  
  const report = `# Test Stability Report

**Generated**: ${timestamp}

## Core Test Status

${results.map(result => {
  const status = result.status === 'PASSED' ? '✅ PASSING' : '❌ FAILING';
  return `- **${result.file}**: ${status} (${result.count} tests)`;
}).join('\n')}

## Recommendations

${results.some(r => r.status === 'FAILED') ? 
  '⚠️ **Action Required**: Some core tests are failing. Please investigate and fix before continuing development.' :
  '✅ **All Clear**: Core test foundation is stable. Safe to continue development.'
}

---
*This report is automatically generated by test-stability-monitor.js*
`;

  fs.writeFileSync(reportPath, report);
  console.log(`📄 Stability report saved to: ${reportPath}`);
}

// Run the stability check if this script is executed directly
if (require.main === module) {
  runStabilityCheck()
    .then(exitCode => process.exit(exitCode))
    .catch(error => {
      console.error('❌ Stability check failed:', error);
      process.exit(1);
    });
}

module.exports = { runStabilityCheck, STABLE_TESTS, EXPECTED_COUNTS };
