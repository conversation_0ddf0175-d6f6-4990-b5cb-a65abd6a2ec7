/**
 * Database Optimization Tests
 *
 * This file contains tests for the database optimization utilities.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock logger
const mockLogger = {
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock Supabase client
const mockSupabase = {
  from: vi.fn().mockImplementation(() => mockSupabase),
  select: vi.fn().mockImplementation(() => mockSupabase),
  insert: vi.fn().mockImplementation(() => mockSupabase),
  update: vi.fn().mockImplementation(() => mockSupabase),
  delete: vi.fn().mockImplementation(() => mockSupabase),
  eq: vi.fn().mockImplementation(() => mockSupabase),
  neq: vi.fn().mockImplementation(() => mockSupabase),
  gt: vi.fn().mockImplementation(() => mockSupabase),
  gte: vi.fn().mockImplementation(() => mockSupabase),
  lt: vi.fn().mockImplementation(() => mockSupabase),
  lte: vi.fn().mockImplementation(() => mockSupabase),
  like: vi.fn().mockImplementation(() => mockSupabase),
  ilike: vi.fn().mockImplementation(() => mockSupabase),
  is: vi.fn().mockImplementation(() => mockSupabase),
  in: vi.fn().mockImplementation(() => mockSupabase),
  order: vi.fn().mockImplementation(() => mockSupabase),
  limit: vi.fn().mockImplementation(() => mockSupabase),
  range: vi.fn().mockImplementation(() => mockSupabase),
};

// Mock Redis client
const mockRedis = {
  get: vi.fn(),
  set: vi.fn(),
  keys: vi.fn(),
  del: vi.fn(),
};

// Mock dependencies
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(() => mockSupabase),
}));

vi.mock('ioredis', () => {
  const RedisMock = vi.fn().mockImplementation(() => mockRedis);
  return { default: RedisMock };
});

vi.mock('../../api/middleware/auth-middleware.js', () => ({
  logger: mockLogger,
}));

// Import the module under test
const {
  optimizedQuery,
  invalidateTableCache,
} = require('../../shared/utils/database-optimization');

describe('Database Optimization', () => {
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
  });

  describe('optimizedQuery', () => {
    describe('select', () => {
      it('should build and execute a basic select query', async () => {
        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.select.mockReturnValue(mockQueryResult);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select();

        // Assertions
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(result).toEqual({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });
      });

      it('should apply filters to the query', async () => {
        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.eq.mockReturnValue(mockQueryResult);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select('*', {
          filters: { id: 1, status: 'active' },
        });

        // Assertions
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(mockSupabase.eq).toHaveBeenCalledWith('id', 1);
        expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active');
        expect(result).toEqual({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });
      });

      it('should apply complex filters to the query', async () => {
        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [{ id: 2, name: 'Test 2' }],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.eq.mockReturnValue(mockQueryResult);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select('*', {
          filters: {
            id: { gt: 1 },
            status: { eq: 'active' },
          },
        });

        // Assertions
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(mockSupabase.gt).toHaveBeenCalledWith('id', 1);
        expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active');
        expect(result).toEqual({
          data: [{ id: 2, name: 'Test 2' }],
          error: null,
        });
      });

      it('should apply ordering to the query', async () => {
        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [
            { id: 2, name: 'Test 2' },
            { id: 1, name: 'Test 1' },
          ],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.order.mockReturnValue(mockQueryResult);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select('*', {
          order: { column: 'id', ascending: false },
        });

        // Assertions
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(mockSupabase.order).toHaveBeenCalledWith('id', { ascending: false });
        expect(result).toEqual({
          data: [
            { id: 2, name: 'Test 2' },
            { id: 1, name: 'Test 1' },
          ],
          error: null,
        });
      });

      it('should apply pagination to the query', async () => {
        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [{ id: 2, name: 'Test 2' }],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.range.mockReturnValue(mockQueryResult);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select('*', {
          limit: 1,
          offset: 1,
        });

        // Assertions
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(mockSupabase.range).toHaveBeenCalledWith(1, 1);
        expect(result).toEqual({
          data: [{ id: 2, name: 'Test 2' }],
          error: null,
        });
      });

      it('should use cache if available', async () => {
        // Mock Redis response
        mockRedis.get.mockResolvedValue(
          JSON.stringify({
            data: [{ id: 1, name: 'Cached Test' }],
            error: null,
          }),
        );

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select('*', {
          cache: true,
        });

        // Assertions
        expect(mockRedis.get).toHaveBeenCalled();
        expect(mockSupabase.from).not.toHaveBeenCalled();
        expect(result).toEqual({
          data: [{ id: 1, name: 'Cached Test' }],
          error: null,
        });
      });

      it('should cache results if caching is enabled', async () => {
        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.select.mockReturnValue(mockQueryResult);

        // Mock Redis response
        mockRedis.get.mockResolvedValue(null);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select('*', {
          cache: true,
          ttl: 300,
        });

        // Assertions
        expect(mockRedis.get).toHaveBeenCalled();
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(mockRedis.set).toHaveBeenCalled();
        expect(result).toEqual({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });
      });

      it('should log slow queries', async () => {
        // Mock Date.now to simulate a slow query
        const originalDateNow = Date.now;
        const mockDateNow = vi
          .fn()
          .mockReturnValueOnce(1000) // Start time
          .mockReturnValueOnce(1600); // End time (600ms elapsed)

        Date.now = mockDateNow;

        // Mock the final query object to be awaitable
        const mockQueryResult = Promise.resolve({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });

        // Make the final method in the chain return the promise
        mockSupabase.select.mockReturnValue(mockQueryResult);

        // Call the function
        const query = optimizedQuery('users');
        const result = await query.select();

        // Assertions
        expect(mockSupabase.from).toHaveBeenCalledWith('users');
        expect(mockSupabase.select).toHaveBeenCalledWith('*');
        expect(mockLogger.warn).toHaveBeenCalled();
        expect(result).toEqual({
          data: [{ id: 1, name: 'Test' }],
          error: null,
        });

        // Restore Date.now
        Date.now = originalDateNow;
      });
    });

    // Additional tests for insert, update, and delete would follow a similar pattern
  });

  describe('invalidateTableCache', () => {
    it('should delete all cache keys for a table', async () => {
      // Mock Redis response
      mockRedis.keys.mockResolvedValue([
        'db:query:{"table":"users","columns":"*"}',
        'db:query:{"table":"users","columns":"id,name"}',
      ]);

      // Call the function
      await invalidateTableCache('users');

      // Assertions
      expect(mockRedis.keys).toHaveBeenCalled();
      expect(mockRedis.del).toHaveBeenCalled();
      expect(mockLogger.debug).toHaveBeenCalled();
    });

    it('should handle empty results', async () => {
      // Mock Redis response
      mockRedis.keys.mockResolvedValue([]);

      // Call the function
      await invalidateTableCache('users');

      // Assertions
      expect(mockRedis.keys).toHaveBeenCalled();
      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      // Mock Redis error
      mockRedis.keys.mockRejectedValue(new Error('Redis error'));

      // Call the function
      await invalidateTableCache('users');

      // Assertions
      expect(mockRedis.keys).toHaveBeenCalled();
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });
});
