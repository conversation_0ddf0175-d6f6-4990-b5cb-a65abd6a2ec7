{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2065", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/security-enhancement.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34831, "count": 1}, {"startOffset": 1064, "endOffset": 34830, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 325, "endOffset": 533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 384, "endOffset": 525, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 554, "endOffset": 683, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 734, "endOffset": 766, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1220, "endOffset": 10725, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2066", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/api/middleware/security-enhancement.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35863, "count": 1}, {"startOffset": 573, "endOffset": 600, "count": 0}], "isBlockCoverage": true}, {"functionName": "generateCsrfToken", "ranges": [{"startOffset": 822, "endOffset": 918, "count": 0}], "isBlockCoverage": false}, {"functionName": "csrfProtection", "ranges": [{"startOffset": 1140, "endOffset": 1282, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateCsrfToken", "ranges": [{"startOffset": 1549, "endOffset": 1897, "count": 0}], "isBlockCoverage": false}, {"functionName": "storeCsrfToken", "ranges": [{"startOffset": 2164, "endOffset": 2513, "count": 0}], "isBlockCoverage": false}, {"functionName": "csrfTokenGenerator", "ranges": [{"startOffset": 2745, "endOffset": 2899, "count": 0}], "isBlockCoverage": false}, {"functionName": "contentSecurityPolicy", "ranges": [{"startOffset": 3030, "endOffset": 4248, "count": 0}], "isBlockCoverage": false}, {"functionName": "securityHeaders", "ranges": [{"startOffset": 4331, "endOffset": 4947, "count": 0}], "isBlockCoverage": false}, {"functionName": "securityRateLimit", "ranges": [{"startOffset": 5111, "endOffset": 6351, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2112", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/api/middleware/auth-middleware.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19643, "count": 1}, {"startOffset": 996, "endOffset": 1001, "count": 0}, {"startOffset": 1061, "endOffset": 1066, "count": 0}, {"startOffset": 1113, "endOffset": 1139, "count": 0}, {"startOffset": 1192, "endOffset": 1212, "count": 0}, {"startOffset": 1253, "endOffset": 1280, "count": 0}, {"startOffset": 1319, "endOffset": 1335, "count": 0}, {"startOffset": 1620, "endOffset": 1628, "count": 0}, {"startOffset": 1629, "endOffset": 1638, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeKeystore", "ranges": [{"startOffset": 2147, "endOffset": 2847, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2900, "endOffset": 2969, "count": 0}], "isBlockCoverage": false}, {"functionName": "createRateLimiter", "ranges": [{"startOffset": 3122, "endOffset": 4554, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackFailedAttempt", "ranges": [{"startOffset": 4635, "endOffset": 5502, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIpBlocked", "ranges": [{"startOffset": 5551, "endOffset": 5733, "count": 0}], "isBlockCoverage": false}, {"functionName": "encryptToken", "ranges": [{"startOffset": 6343, "endOffset": 6799, "count": 0}], "isBlockCoverage": false}, {"functionName": "decryptToken", "ranges": [{"startOffset": 6960, "endOffset": 7283, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTokenRevoked", "ranges": [{"startOffset": 7448, "endOffset": 7778, "count": 0}], "isBlockCoverage": false}, {"functionName": "revokeToken", "ranges": [{"startOffset": 7960, "endOffset": 8406, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateDirectusToken", "ranges": [{"startOffset": 8576, "endOffset": 9244, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateSupabaseToken", "ranges": [{"startOffset": 9402, "endOffset": 10009, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshSupabaseToken", "ranges": [{"startOffset": 10177, "endOffset": 11051, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSecureCookie", "ranges": [{"startOffset": 11289, "endOffset": 11613, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTokenFromRequest", "ranges": [{"startOffset": 11774, "endOffset": 12523, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackAuthEvent", "ranges": [{"startOffset": 12662, "endOffset": 13397, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateCsrfToken", "ranges": [{"startOffset": 13497, "endOffset": 13555, "count": 0}], "isBlockCoverage": false}, {"functionName": "authenticate", "ranges": [{"startOffset": 13901, "endOffset": 19119, "count": 0}], "isBlockCoverage": false}]}]}