/**
 * Sync Engine Service
 * Handles real-time data synchronization with conflict resolution and offline support
 */

import { EventEmitter } from 'events';
import { createHash } from 'crypto';
import Redis from 'ioredis';

export class SyncEngine extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      redis: options.redis || null,
      enableConflictResolution: options.enableConflictResolution || true,
      conflictResolutionStrategy: options.conflictResolutionStrategy || 'last-write-wins', // last-write-wins, merge, custom
      enableVersioning: options.enableVersioning || true,
      maxVersionHistory: options.maxVersionHistory || 100,
      syncInterval: options.syncInterval || 1000,
      enableOfflineSupport: options.enableOfflineSupport || true,
      offlineQueueSize: options.offlineQueueSize || 10000,
      enableOptimisticUpdates: options.enableOptimisticUpdates || true,
      enableBatching: options.enableBatching || true,
      batchSize: options.batchSize || 50,
      batchTimeout: options.batchTimeout || 500,
      ...options
    };

    // Data management
    this.entities = new Map(); // entityId -> entity data
    this.versions = new Map(); // entityId -> version info
    this.versionHistory = new Map(); // entityId -> array of versions
    this.subscriptions = new Map(); // entityId -> Set of subscriber info
    
    // Conflict resolution
    this.pendingConflicts = new Map(); // entityId -> conflict info
    this.conflictResolvers = new Map(); // entity type -> resolver function
    
    // Offline support
    this.offlineQueues = new Map(); // clientId -> array of operations
    this.clientStates = new Map(); // clientId -> client state info
    
    // Batching
    this.batchQueues = new Map(); // entityId -> array of operations
    this.batchTimers = new Map(); // entityId -> timer
    
    // Metrics
    this.metrics = {
      entitiesSynced: 0,
      conflictsResolved: 0,
      optimisticUpdates: 0,
      offlineOperations: 0,
      syncErrors: 0,
      averageSyncTime: 0,
      lastReset: Date.now()
    };

    this.redis = null;
    this.initialize();
  }

  async initialize() {
    try {
      if (this.options.redis) {
        this.redis = new Redis(this.options.redis);
        await this.redis.ping();
        console.log('✅ Redis connection established for Sync Engine');
      }

      this.startSyncLoop();
      this.startMetricsCollection();

      console.log('🔄 Sync Engine initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize Sync Engine:', error);
      this.emit('error', error);
    }
  }

  /**
   * Subscribe to entity changes
   * @param {string} entityId - Entity identifier
   * @param {string} clientId - Client identifier
   * @param {Object} options - Subscription options
   */
  subscribe(entityId, clientId, options = {}) {
    if (!this.subscriptions.has(entityId)) {
      this.subscriptions.set(entityId, new Set());
    }

    const subscription = {
      clientId,
      subscribedAt: Date.now(),
      options: {
        includeHistory: options.includeHistory || false,
        conflictResolution: options.conflictResolution || this.options.conflictResolutionStrategy,
        optimisticUpdates: options.optimisticUpdates !== false,
        ...options
      }
    };

    this.subscriptions.get(entityId).add(subscription);

    // Send current state if entity exists
    if (this.entities.has(entityId)) {
      const entity = this.entities.get(entityId);
      const version = this.versions.get(entityId);
      
      this.notifySubscriber(subscription, {
        type: 'entity-state',
        entityId,
        data: entity,
        version: version.current,
        timestamp: Date.now()
      });

      // Send version history if requested
      if (options.includeHistory && this.versionHistory.has(entityId)) {
        const history = this.versionHistory.get(entityId);
        this.notifySubscriber(subscription, {
          type: 'entity-history',
          entityId,
          history: history.slice(-options.historyLimit || 10),
          timestamp: Date.now()
        });
      }
    }

    console.log(`📡 Client ${clientId} subscribed to entity ${entityId}`);
    this.emit('subscription', { entityId, clientId, subscription });

    return subscription;
  }

  /**
   * Unsubscribe from entity changes
   * @param {string} entityId - Entity identifier
   * @param {string} clientId - Client identifier
   */
  unsubscribe(entityId, clientId) {
    if (!this.subscriptions.has(entityId)) {
      return false;
    }

    const subscribers = this.subscriptions.get(entityId);
    const subscription = Array.from(subscribers).find(sub => sub.clientId === clientId);
    
    if (subscription) {
      subscribers.delete(subscription);
      
      if (subscribers.size === 0) {
        this.subscriptions.delete(entityId);
      }
      
      console.log(`📡 Client ${clientId} unsubscribed from entity ${entityId}`);
      return true;
    }
    
    return false;
  }

  /**
   * Update an entity
   * @param {string} entityId - Entity identifier
   * @param {Object} data - Entity data
   * @param {Object} context - Update context
   */
  async updateEntity(entityId, data, context = {}) {
    const startTime = Date.now();
    
    try {
      const operation = {
        type: 'update',
        entityId,
        data,
        clientId: context.clientId,
        timestamp: Date.now(),
        version: context.version || null,
        operationId: this.generateOperationId(),
        metadata: context.metadata || {}
      };

      // Handle offline client
      if (context.offline) {
        return this.queueOfflineOperation(operation);
      }

      // Check for conflicts if versioning is enabled
      if (this.options.enableVersioning && this.entities.has(entityId)) {
        const currentVersion = this.versions.get(entityId);
        
        if (operation.version && operation.version < currentVersion.current) {
          return this.handleConflict(operation, currentVersion);
        }
      }

      // Apply update
      const result = await this.applyUpdate(operation);
      
      // Notify subscribers
      await this.notifySubscribers(entityId, {
        type: 'entity-updated',
        entityId,
        data: result.data,
        version: result.version,
        operation,
        timestamp: Date.now()
      });

      this.metrics.entitiesSynced++;
      
      const syncTime = Date.now() - startTime;
      this.updateSyncTimeMetrics(syncTime);

      console.log(`🔄 Entity ${entityId} updated (v${result.version})`);
      this.emit('entityUpdated', { entityId, operation, result, syncTime });

      return result;
    } catch (error) {
      console.error(`Entity update error for ${entityId}:`, error);
      this.metrics.syncErrors++;
      this.emit('updateError', { entityId, operation: { entityId, data, context }, error });
      throw error;
    }
  }

  /**
   * Apply an update operation
   * @param {Object} operation - Update operation
   */
  async applyUpdate(operation) {
    const { entityId, data } = operation;
    
    // Get current entity state
    const currentEntity = this.entities.get(entityId) || {};
    const currentVersion = this.versions.get(entityId) || { current: 0, lastModified: 0 };
    
    // Merge data based on strategy
    let mergedData;
    if (this.options.conflictResolutionStrategy === 'merge') {
      mergedData = this.mergeData(currentEntity, data);
    } else {
      mergedData = { ...currentEntity, ...data };
    }

    // Update version
    const newVersion = {
      current: currentVersion.current + 1,
      lastModified: operation.timestamp,
      modifiedBy: operation.clientId,
      operationId: operation.operationId
    };

    // Store entity and version
    this.entities.set(entityId, mergedData);
    this.versions.set(entityId, newVersion);

    // Store version history
    if (this.options.enableVersioning) {
      this.addToVersionHistory(entityId, {
        version: newVersion.current,
        data: mergedData,
        operation,
        timestamp: operation.timestamp
      });
    }

    // Persist to Redis if available
    if (this.redis) {
      await this.persistToRedis(entityId, mergedData, newVersion);
    }

    return {
      data: mergedData,
      version: newVersion.current,
      operationId: operation.operationId
    };
  }

  /**
   * Handle conflict resolution
   * @param {Object} operation - Conflicting operation
   * @param {Object} currentVersion - Current version info
   */
  async handleConflict(operation, currentVersion) {
    console.log(`⚠️ Conflict detected for entity ${operation.entityId}`);
    
    const conflict = {
      entityId: operation.entityId,
      operation,
      currentVersion,
      detectedAt: Date.now(),
      status: 'pending'
    };

    this.pendingConflicts.set(operation.entityId, conflict);

    // Apply conflict resolution strategy
    let resolution;
    switch (this.options.conflictResolutionStrategy) {
      case 'last-write-wins':
        resolution = await this.resolveLastWriteWins(conflict);
        break;
      case 'merge':
        resolution = await this.resolveMerge(conflict);
        break;
      case 'custom':
        resolution = await this.resolveCustom(conflict);
        break;
      default:
        throw new Error(`Unknown conflict resolution strategy: ${this.options.conflictResolutionStrategy}`);
    }

    this.pendingConflicts.delete(operation.entityId);
    this.metrics.conflictsResolved++;

    console.log(`✅ Conflict resolved for entity ${operation.entityId} using ${this.options.conflictResolutionStrategy}`);
    this.emit('conflictResolved', { conflict, resolution });

    return resolution;
  }

  async resolveLastWriteWins(conflict) {
    // Simply apply the operation (last write wins)
    return this.applyUpdate(conflict.operation);
  }

  async resolveMerge(conflict) {
    const { entityId, operation } = conflict;
    const currentEntity = this.entities.get(entityId);
    
    // Perform three-way merge
    const mergedData = this.mergeData(currentEntity, operation.data);
    
    const mergedOperation = {
      ...operation,
      data: mergedData,
      type: 'merge-resolution'
    };

    return this.applyUpdate(mergedOperation);
  }

  async resolveCustom(conflict) {
    const { entityId } = conflict;
    const entityType = this.getEntityType(entityId);
    const resolver = this.conflictResolvers.get(entityType);
    
    if (!resolver) {
      throw new Error(`No custom conflict resolver found for entity type: ${entityType}`);
    }

    const resolution = await resolver(conflict);
    return this.applyUpdate(resolution.operation);
  }

  /**
   * Merge two data objects
   * @param {Object} current - Current data
   * @param {Object} incoming - Incoming data
   */
  mergeData(current, incoming) {
    // Simple merge strategy - can be enhanced for complex data structures
    const merged = { ...current };
    
    for (const [key, value] of Object.entries(incoming)) {
      if (value !== null && value !== undefined) {
        if (typeof value === 'object' && !Array.isArray(value) && typeof current[key] === 'object') {
          merged[key] = this.mergeData(current[key], value);
        } else {
          merged[key] = value;
        }
      }
    }
    
    return merged;
  }

  /**
   * Queue operation for offline client
   * @param {Object} operation - Operation to queue
   */
  queueOfflineOperation(operation) {
    const { clientId } = operation;
    
    if (!this.offlineQueues.has(clientId)) {
      this.offlineQueues.set(clientId, []);
    }

    const queue = this.offlineQueues.get(clientId);
    
    if (queue.length >= this.options.offlineQueueSize) {
      queue.shift(); // Remove oldest operation
    }

    queue.push(operation);
    this.metrics.offlineOperations++;

    console.log(`📱 Queued offline operation for client ${clientId}: ${operation.operationId}`);
    this.emit('offlineOperationQueued', { clientId, operation });

    return {
      queued: true,
      operationId: operation.operationId,
      queuePosition: queue.length
    };
  }

  /**
   * Process offline operations when client comes back online
   * @param {string} clientId - Client identifier
   */
  async processOfflineOperations(clientId) {
    const queue = this.offlineQueues.get(clientId);
    if (!queue || queue.length === 0) {
      return [];
    }

    console.log(`🔄 Processing ${queue.length} offline operations for client ${clientId}`);
    
    const results = [];
    const operations = [...queue]; // Copy queue
    queue.length = 0; // Clear queue

    for (const operation of operations) {
      try {
        const result = await this.updateEntity(operation.entityId, operation.data, {
          ...operation,
          offline: false
        });
        results.push({ success: true, operation, result });
      } catch (error) {
        console.error(`Failed to process offline operation ${operation.operationId}:`, error);
        results.push({ success: false, operation, error: error.message });
      }
    }

    this.emit('offlineOperationsProcessed', { clientId, results });
    return results;
  }

  /**
   * Notify subscribers of entity changes
   * @param {string} entityId - Entity identifier
   * @param {Object} notification - Notification data
   */
  async notifySubscribers(entityId, notification) {
    const subscribers = this.subscriptions.get(entityId);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const notificationPromises = Array.from(subscribers).map(subscription =>
      this.notifySubscriber(subscription, notification)
    );

    await Promise.allSettled(notificationPromises);
  }

  async notifySubscriber(subscription, notification) {
    try {
      // Apply optimistic updates if enabled
      if (subscription.options.optimisticUpdates && notification.type === 'entity-updated') {
        this.metrics.optimisticUpdates++;
      }

      this.emit('subscriberNotification', {
        subscription,
        notification,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error(`Failed to notify subscriber ${subscription.clientId}:`, error);
    }
  }

  addToVersionHistory(entityId, versionData) {
    if (!this.versionHistory.has(entityId)) {
      this.versionHistory.set(entityId, []);
    }

    const history = this.versionHistory.get(entityId);
    history.push(versionData);

    // Maintain history size limit
    if (history.length > this.options.maxVersionHistory) {
      history.shift();
    }
  }

  async persistToRedis(entityId, data, version) {
    if (!this.redis) return;

    const key = `entity:${entityId}`;
    const value = JSON.stringify({
      data,
      version,
      lastSync: Date.now()
    });

    await this.redis.set(key, value);
  }

  /**
   * Get entity state
   * @param {string} entityId - Entity identifier
   */
  getEntity(entityId) {
    const data = this.entities.get(entityId);
    const version = this.versions.get(entityId);
    
    if (!data) return null;

    return {
      entityId,
      data,
      version: version?.current || 0,
      lastModified: version?.lastModified || 0,
      modifiedBy: version?.modifiedBy || null
    };
  }

  /**
   * Get entity version history
   * @param {string} entityId - Entity identifier
   * @param {Object} options - Query options
   */
  getEntityHistory(entityId, options = {}) {
    const history = this.versionHistory.get(entityId) || [];
    
    let filteredHistory = history;

    if (options.since) {
      filteredHistory = filteredHistory.filter(item => item.timestamp >= options.since);
    }

    if (options.limit) {
      filteredHistory = filteredHistory.slice(-options.limit);
    }

    return filteredHistory;
  }

  getEntityType(entityId) {
    // Extract entity type from ID (assuming format: type:id)
    const parts = entityId.split(':');
    return parts.length > 1 ? parts[0] : 'unknown';
  }

  generateOperationId() {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  startSyncLoop() {
    setInterval(() => {
      this.performSync();
    }, this.options.syncInterval);
  }

  async performSync() {
    // Sync with Redis if available
    if (this.redis) {
      // Implementation for syncing with Redis cluster
      // This would handle distributed synchronization
    }

    // Process batched operations
    await this.processBatchedOperations();
  }

  async processBatchedOperations() {
    for (const [entityId, operations] of this.batchQueues) {
      if (operations.length > 0) {
        await this.processBatch(entityId, operations);
        operations.length = 0; // Clear batch
      }
    }
  }

  async processBatch(entityId, operations) {
    console.log(`📦 Processing batch for entity ${entityId}: ${operations.length} operations`);
    
    // Apply operations in sequence
    for (const operation of operations) {
      try {
        await this.applyUpdate(operation);
      } catch (error) {
        console.error(`Batch operation failed for ${entityId}:`, error);
      }
    }
  }

  startMetricsCollection() {
    setInterval(() => {
      this.updateMetrics();
    }, 5000);
  }

  updateMetrics() {
    const now = Date.now();
    const timeDiff = now - this.metrics.lastReset;
    
    // Calculate rates
    this.metrics.syncRate = this.metrics.entitiesSynced / (timeDiff / 1000);
    this.metrics.conflictRate = this.metrics.conflictsResolved / (timeDiff / 1000);
    this.metrics.errorRate = this.metrics.syncErrors / (timeDiff / 1000);
    
    this.metrics.lastReset = now;
    
    // Reset counters
    this.metrics.entitiesSynced = 0;
    this.metrics.conflictsResolved = 0;
    this.metrics.optimisticUpdates = 0;
    this.metrics.offlineOperations = 0;
    this.metrics.syncErrors = 0;

    this.emit('metrics', this.metrics);
  }

  updateSyncTimeMetrics(syncTime) {
    this.metrics.averageSyncTime = 
      (this.metrics.averageSyncTime * 0.9) + (syncTime * 0.1);
  }

  getMetrics() {
    return {
      ...this.metrics,
      entities: this.entities.size,
      subscriptions: Array.from(this.subscriptions.values())
        .reduce((sum, subs) => sum + subs.size, 0),
      pendingConflicts: this.pendingConflicts.size,
      offlineQueues: this.offlineQueues.size,
      versionHistorySize: Array.from(this.versionHistory.values())
        .reduce((sum, history) => sum + history.length, 0)
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down Sync Engine...');
    
    // Process remaining batched operations
    await this.processBatchedOperations();
    
    // Clear all timers
    for (const timer of this.batchTimers.values()) {
      clearTimeout(timer);
    }

    // Close Redis connection
    if (this.redis) {
      await this.redis.quit();
    }

    console.log('✅ Sync Engine shutdown complete');
  }
}

export default SyncEngine;
