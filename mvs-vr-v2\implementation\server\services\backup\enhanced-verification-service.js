/**
 * Enhanced Backup Verification Service
 *
 * This service provides comprehensive verification of cross-region backup replication
 * including integrity checks, metadata validation, and automated reporting.
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { promisify } from 'util';
import { <PERSON><PERSON><PERSON> } from 'node:buffer';
import process from 'process';
import {
  S3Client,
  GetObjectCommand,
  HeadObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { getLogger } from '../../utils/logger.js';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const logger = getLogger('enhanced-verification');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const unlinkAsync = promisify(fs.unlink);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

/**
 * Enhanced Verification Service Configuration
 */
const config = {
  primaryRegion: process.env.PRIMARY_REGION || 'us-east-1',
  secondaryRegion: process.env.SECONDARY_REGION || 'us-west-2',
  buckets: {
    database: {
      primary: process.env.PRIMARY_DB_BUCKET || 'mvs-vr-db-backups',
      secondary: process.env.SECONDARY_DB_BUCKET || 'mvs-vr-db-backups-dr',
    },
    files: {
      primary: process.env.PRIMARY_FILES_BUCKET || 'mvs-vr-file-backups',
      secondary: process.env.SECONDARY_FILES_BUCKET || 'mvs-vr-file-backups-dr',
    },
    config: {
      primary: process.env.PRIMARY_CONFIG_BUCKET || 'mvs-vr-config-backups',
      secondary: process.env.SECONDARY_CONFIG_BUCKET || 'mvs-vr-config-backups-dr',
    },
  },
  verification: {
    maxSampleSize: parseInt(process.env.MAX_VERIFICATION_SAMPLES) || 50,
    checksumAlgorithm: 'sha256',
    integrityThreshold: 95, // Minimum integrity score percentage
    maxVerificationTime: 30 * 60 * 1000, // 30 minutes
    reportRetentionDays: 30,
    criticalFilePatterns: [/\.sql$/, /\.dump$/, /critical/i, /system/i, /config/i],
  },
  tempDir: path.join(__dirname, '../../temp/verification'),
  reportsDir: path.join(__dirname, '../../reports/verification'),
};

/**
 * Enhanced Backup Verification Service
 */
class EnhancedVerificationService {
  constructor(options = {}) {
    this.config = { ...config, ...options };
    this.primaryClient = new S3Client({ region: this.config.primaryRegion });
    this.secondaryClient = new S3Client({ region: this.config.secondaryRegion });
  }

  /**
   * Initialize the verification service
   */
  async initialize() {
    // Create necessary directories
    await this.ensureDirectory(this.config.tempDir);
    await this.ensureDirectory(this.config.reportsDir);

    logger.info('Enhanced Verification Service initialized');
  }

  /**
   * Ensure directory exists
   */
  async ensureDirectory(dirPath) {
    if (!(await existsAsync(dirPath))) {
      await mkdirAsync(dirPath, { recursive: true });
    }
  }

  /**
   * Run comprehensive verification for all bucket types
   */
  async runComprehensiveVerification() {
    const startTime = Date.now();
    const verificationId = `verification-${Date.now()}`;

    logger.info(`Starting comprehensive verification: ${verificationId}`);

    const results = {
      verificationId,
      startTime: new Date(startTime).toISOString(),
      endTime: null,
      duration: 0,
      overallStatus: 'unknown',
      buckets: {},
      summary: {
        totalObjects: 0,
        verifiedObjects: 0,
        failedObjects: 0,
        integrityScore: 0,
        criticalIssues: [],
        recommendations: [],
      },
    };

    try {
      // Verify each bucket type
      for (const [bucketType, bucketConfig] of Object.entries(this.config.buckets)) {
        logger.info(`Verifying bucket type: ${bucketType}`);

        try {
          results.buckets[bucketType] = await this.verifyBucketType(bucketType, bucketConfig);
        } catch (error) {
          logger.error(`Error verifying ${bucketType}:`, error);
          results.buckets[bucketType] = {
            status: 'error',
            error: error.message,
            timestamp: new Date().toISOString(),
          };
        }
      }

      // Calculate overall results
      this.calculateOverallResults(results);

      // Generate recommendations
      this.generateRecommendations(results);

      const endTime = Date.now();
      results.endTime = new Date(endTime).toISOString();
      results.duration = (endTime - startTime) / 1000;

      // Save verification report
      await this.saveVerificationReport(results);

      logger.info(
        `Comprehensive verification completed in ${results.duration}s with ${results.overallStatus} status`,
      );

      return results;
    } catch (error) {
      logger.error('Comprehensive verification failed:', error);
      throw error;
    }
  }

  /**
   * Verify a specific bucket type
   */
  async verifyBucketType(bucketType, bucketConfig) {
    const results = {
      bucketType,
      status: 'unknown',
      replication: null,
      integrity: null,
      metadata: null,
      timestamp: new Date().toISOString(),
    };

    // Step 1: Verify replication status
    results.replication = await this.verifyReplication(bucketType, bucketConfig);

    // Step 2: Verify integrity for sample objects
    results.integrity = await this.verifyIntegrity(
      bucketType,
      bucketConfig,
      results.replication.objects,
    );

    // Step 3: Verify metadata consistency
    results.metadata = await this.verifyMetadata(
      bucketType,
      bucketConfig,
      results.replication.objects,
    );

    // Determine overall status
    results.status = this.determineBucketStatus(results);

    return results;
  }

  /**
   * Verify replication status between primary and secondary buckets
   */
  async verifyReplication(bucketType, bucketConfig) {
    logger.info(`Verifying replication for ${bucketType}...`);

    // List objects in both buckets
    const primaryObjects = await this.listBucketObjects(this.primaryClient, bucketConfig.primary);
    const secondaryObjects = await this.listBucketObjects(
      this.secondaryClient,
      bucketConfig.secondary,
    );

    // Create maps for efficient lookup
    const primaryMap = new Map(primaryObjects.map(obj => [obj.Key, obj]));
    const secondaryMap = new Map(secondaryObjects.map(obj => [obj.Key, obj]));

    // Find missing and mismatched objects
    const missingInSecondary = [];
    const mismatched = [];
    const verified = [];

    for (const [key, primaryObj] of primaryMap) {
      const secondaryObj = secondaryMap.get(key);

      if (!secondaryObj) {
        missingInSecondary.push(key);
      } else if (primaryObj.ETag !== secondaryObj.ETag || primaryObj.Size !== secondaryObj.Size) {
        mismatched.push({
          key,
          primary: { etag: primaryObj.ETag, size: primaryObj.Size },
          secondary: { etag: secondaryObj.ETag, size: secondaryObj.Size },
        });
      } else {
        verified.push(key);
      }
    }

    // Find extra objects in secondary
    const extraInSecondary = Array.from(secondaryMap.keys()).filter(key => !primaryMap.has(key));

    const replicationScore = (verified.length / primaryObjects.length) * 100;

    return {
      primaryCount: primaryObjects.length,
      secondaryCount: secondaryObjects.length,
      verifiedCount: verified.length,
      missingCount: missingInSecondary.length,
      mismatchedCount: mismatched.length,
      extraCount: extraInSecondary.length,
      replicationScore,
      status: replicationScore >= 95 ? 'healthy' : replicationScore >= 90 ? 'warning' : 'critical',
      objects: primaryObjects.slice(0, this.config.verification.maxSampleSize), // Sample for integrity testing
      details: {
        missing: missingInSecondary,
        mismatched,
        extra: extraInSecondary,
      },
    };
  }

  /**
   * Verify integrity using checksums for sample objects
   */
  async verifyIntegrity(bucketType, bucketConfig, sampleObjects) {
    logger.info(`Verifying integrity for ${bucketType} (${sampleObjects.length} samples)...`);

    const results = {
      totalSamples: sampleObjects.length,
      verifiedSamples: 0,
      failedSamples: 0,
      criticalFailures: 0,
      details: [],
    };

    // Prioritize critical files
    const criticalObjects = sampleObjects.filter(obj =>
      this.config.verification.criticalFilePatterns.some(pattern => pattern.test(obj.Key)),
    );

    const regularObjects = sampleObjects.filter(
      obj => !this.config.verification.criticalFilePatterns.some(pattern => pattern.test(obj.Key)),
    );

    // Verify critical files first, then sample of regular files
    const objectsToVerify = [
      ...criticalObjects,
      ...regularObjects.slice(
        0,
        Math.max(0, this.config.verification.maxSampleSize - criticalObjects.length),
      ),
    ];

    for (const obj of objectsToVerify) {
      try {
        const isCritical = criticalObjects.includes(obj);
        const verificationResult = await this.verifyObjectIntegrity(
          bucketConfig.primary,
          bucketConfig.secondary,
          obj.Key,
          isCritical,
        );

        results.details.push({
          key: obj.Key,
          critical: isCritical,
          ...verificationResult,
        });

        if (verificationResult.valid) {
          results.verifiedSamples++;
        } else {
          results.failedSamples++;
          if (isCritical) {
            results.criticalFailures++;
          }
        }
      } catch (error) {
        logger.error(`Error verifying integrity for ${obj.Key}:`, error);
        results.failedSamples++;
        results.details.push({
          key: obj.Key,
          valid: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    }

    const integrityScore = (results.verifiedSamples / results.totalSamples) * 100;

    return {
      ...results,
      integrityScore,
      status:
        integrityScore >= this.config.verification.integrityThreshold
          ? 'healthy'
          : integrityScore >= 90
            ? 'warning'
            : 'critical',
    };
  }

  /**
   * Verify object integrity between primary and secondary regions
   */
  async verifyObjectIntegrity(
    primaryBucket,
    secondaryBucket,
    objectKey,
    downloadForChecksum = false,
  ) {
    try {
      // Get metadata from both regions
      const primaryMetadata = await this.getObjectMetadata(
        this.primaryClient,
        primaryBucket,
        objectKey,
      );
      const secondaryMetadata = await this.getObjectMetadata(
        this.secondaryClient,
        secondaryBucket,
        objectKey,
      );

      // Basic metadata comparison
      const metadataValid =
        primaryMetadata.ContentLength === secondaryMetadata.ContentLength &&
        primaryMetadata.ETag === secondaryMetadata.ETag;

      let checksumValid = true;
      let checksumDetails = null;

      // For critical files or when explicitly requested, perform checksum verification
      if (downloadForChecksum) {
        checksumDetails = await this.performChecksumVerification(
          primaryBucket,
          secondaryBucket,
          objectKey,
        );
        checksumValid = checksumDetails.valid;
      }

      return {
        valid: metadataValid && checksumValid,
        metadataValid,
        checksumValid,
        primarySize: primaryMetadata.ContentLength,
        secondarySize: secondaryMetadata.ContentLength,
        primaryETag: primaryMetadata.ETag,
        secondaryETag: secondaryMetadata.ETag,
        checksumDetails,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Perform checksum verification by downloading and comparing files
   */
  async performChecksumVerification(primaryBucket, secondaryBucket, objectKey) {
    const primaryPath = path.join(this.config.tempDir, 'primary', objectKey);
    const secondaryPath = path.join(this.config.tempDir, 'secondary', objectKey);

    try {
      // Ensure directories exist
      await this.ensureDirectory(path.dirname(primaryPath));
      await this.ensureDirectory(path.dirname(secondaryPath));

      // Download files
      await this.downloadObject(this.primaryClient, primaryBucket, objectKey, primaryPath);
      await this.downloadObject(this.secondaryClient, secondaryBucket, objectKey, secondaryPath);

      // Calculate checksums
      const primaryChecksum = await this.calculateFileChecksum(primaryPath);
      const secondaryChecksum = await this.calculateFileChecksum(secondaryPath);

      // Clean up
      await unlinkAsync(primaryPath);
      await unlinkAsync(secondaryPath);

      return {
        valid: primaryChecksum === secondaryChecksum,
        primaryChecksum,
        secondaryChecksum,
        algorithm: this.config.verification.checksumAlgorithm,
      };
    } catch (error) {
      // Clean up on error
      try {
        if (await existsAsync(primaryPath)) await unlinkAsync(primaryPath);
        if (await existsAsync(secondaryPath)) await unlinkAsync(secondaryPath);
      } catch (cleanupError) {
        logger.warn('Error during cleanup:', cleanupError);
      }

      throw error;
    }
  }

  /**
   * Calculate file checksum
   */
  async calculateFileChecksum(filePath) {
    const fileBuffer = await readFileAsync(filePath);
    const hash = crypto.createHash(this.config.verification.checksumAlgorithm);
    hash.update(fileBuffer);
    return hash.digest('hex');
  }

  /**
   * Download object from S3
   */
  async downloadObject(client, bucket, key, outputPath) {
    const command = new GetObjectCommand({ Bucket: bucket, Key: key });
    const response = await client.send(command);

    const chunks = [];
    for await (const chunk of response.Body) {
      chunks.push(chunk);
    }

    const buffer = Buffer.concat(chunks);
    await writeFileAsync(outputPath, buffer);
  }

  /**
   * Get object metadata
   */
  async getObjectMetadata(client, bucket, key) {
    const command = new HeadObjectCommand({ Bucket: bucket, Key: key });
    return await client.send(command);
  }

  /**
   * List objects in bucket
   */
  async listBucketObjects(client, bucket) {
    const objects = [];
    let continuationToken = null;

    do {
      const command = new ListObjectsV2Command({
        Bucket: bucket,
        ContinuationToken: continuationToken,
      });

      const response = await client.send(command);

      if (response.Contents) {
        objects.push(...response.Contents);
      }

      continuationToken = response.NextContinuationToken;
    } while (continuationToken);

    return objects;
  }

  /**
   * Verify metadata consistency
   */
  async verifyMetadata(bucketType, bucketConfig, sampleObjects) {
    logger.info(`Verifying metadata consistency for ${bucketType}...`);

    // This is a placeholder for metadata verification
    // In a real implementation, this would check metadata consistency
    return {
      status: 'healthy',
      details: 'Metadata verification not yet implemented',
    };
  }

  /**
   * Determine bucket status based on verification results
   */
  determineBucketStatus(results) {
    const replicationStatus = results.replication?.status || 'unknown';
    const integrityStatus = results.integrity?.status || 'unknown';

    if (replicationStatus === 'critical' || integrityStatus === 'critical') {
      return 'critical';
    } else if (replicationStatus === 'warning' || integrityStatus === 'warning') {
      return 'warning';
    } else if (replicationStatus === 'healthy' && integrityStatus === 'healthy') {
      return 'healthy';
    } else {
      return 'unknown';
    }
  }

  /**
   * Calculate overall verification results
   */
  calculateOverallResults(results) {
    let totalObjects = 0;
    let verifiedObjects = 0;
    let failedObjects = 0;
    let criticalIssues = [];

    for (const [bucketType, bucketResult] of Object.entries(results.buckets)) {
      if (bucketResult.replication) {
        totalObjects += bucketResult.replication.primaryCount;
        verifiedObjects += bucketResult.replication.verifiedCount;
        failedObjects +=
          bucketResult.replication.missingCount + bucketResult.replication.mismatchedCount;
      }

      if (bucketResult.status === 'critical') {
        criticalIssues.push(`Critical issues detected in ${bucketType} bucket`);
      }

      if (bucketResult.integrity?.criticalFailures > 0) {
        criticalIssues.push(
          `${bucketResult.integrity.criticalFailures} critical file integrity failures in ${bucketType}`,
        );
      }
    }

    results.summary.totalObjects = totalObjects;
    results.summary.verifiedObjects = verifiedObjects;
    results.summary.failedObjects = failedObjects;
    results.summary.integrityScore = totalObjects > 0 ? (verifiedObjects / totalObjects) * 100 : 0;
    results.summary.criticalIssues = criticalIssues;

    // Determine overall status
    if (criticalIssues.length > 0) {
      results.overallStatus = 'critical';
    } else if (results.summary.integrityScore >= 95) {
      results.overallStatus = 'healthy';
    } else if (results.summary.integrityScore >= 90) {
      results.overallStatus = 'warning';
    } else {
      results.overallStatus = 'critical';
    }
  }

  /**
   * Generate recommendations based on verification results
   */
  generateRecommendations(results) {
    const recommendations = [];

    if (results.summary.integrityScore < 95) {
      recommendations.push(
        `Integrity score (${results.summary.integrityScore.toFixed(1)}%) is below threshold. Investigate and re-replicate failed objects.`,
      );
    }

    for (const [bucketType, bucketResult] of Object.entries(results.buckets)) {
      if (bucketResult.replication?.missingCount > 0) {
        recommendations.push(
          `Re-run replication for ${bucketResult.replication.missingCount} missing objects in ${bucketType} bucket`,
        );
      }

      if (bucketResult.integrity?.criticalFailures > 0) {
        recommendations.push(
          `Immediate attention required: ${bucketResult.integrity.criticalFailures} critical files failed integrity check in ${bucketType} bucket`,
        );
      }
    }

    if (recommendations.length === 0) {
      recommendations.push('All verification checks passed. Continue regular monitoring.');
    }

    results.summary.recommendations = recommendations;
  }

  /**
   * Save verification report
   */
  async saveVerificationReport(results) {
    const reportPath = path.join(
      this.config.reportsDir,
      `verification-report-${results.verificationId}.json`,
    );

    await writeFileAsync(reportPath, JSON.stringify(results, null, 2));

    // Also save as latest report
    const latestReportPath = path.join(this.config.reportsDir, 'latest-verification-report.json');
    await writeFileAsync(latestReportPath, JSON.stringify(results, null, 2));

    logger.info(`Verification report saved to ${reportPath}`);
  }
}

export { EnhancedVerificationService, config };
