{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/PreviewTestingTools.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24812, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24812, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 351, "endOffset": 521, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 410, "endOffset": 455, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 571, "endOffset": 708, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 632, "endOffset": 679, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 763, "endOffset": 891, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 829, "endOffset": 882, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 951, "endOffset": 1089, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1022, "endOffset": 1080, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1776, "endOffset": 5990, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1813, "endOffset": 2010, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2044, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2207, "endOffset": 2593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2648, "endOffset": 2982, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3034, "endOffset": 3326, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3401, "endOffset": 4143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4220, "endOffset": 4756, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4838, "endOffset": 5378, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5447, "endOffset": 5986, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10653, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10653, "count": 1}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 2427, "endOffset": 2437, "count": 0}], "isBlockCoverage": false}, {"functionName": "setup", "ranges": [{"startOffset": 2454, "endOffset": 3346, "count": 8}, {"startOffset": 2997, "endOffset": 3021, "count": 0}, {"startOffset": 3097, "endOffset": 3102, "count": 0}], "isBlockCoverage": true}, {"functionName": "handlePreviewChange", "ranges": [{"startOffset": 3171, "endOffset": 3224, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 3623, "endOffset": 3729, "count": 8}, {"startOffset": 3692, "endOffset": 3727, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4163, "endOffset": 4252, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4362, "endOffset": 4445, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4494, "endOffset": 4538, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10639, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10639, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1729, "count": 11}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 1041, "endOffset": 1302, "count": 8}, {"startOffset": 1303, "endOffset": 1722, "count": 3}, {"startOffset": 1340, "endOffset": 1469, "count": 1}, {"startOffset": 1470, "endOffset": 1722, "count": 2}, {"startOffset": 1503, "endOffset": 1583, "count": 1}, {"startOffset": 1584, "endOffset": 1722, "count": 1}, {"startOffset": 1712, "endOffset": 1722, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 552, "endOffset": 948, "count": 44}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 741, "endOffset": 804, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1880, "endOffset": 1902, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2009, "endOffset": 2040, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/PreviewTestingTools.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 8}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}