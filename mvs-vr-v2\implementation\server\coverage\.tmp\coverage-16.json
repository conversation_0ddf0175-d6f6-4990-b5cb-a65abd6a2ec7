{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1437", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 101166, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 101166, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1036, "endOffset": 1168, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectedLight", "ranges": [{"startOffset": 1189, "endOffset": 1295, "count": 0}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1306, "endOffset": 1449, "count": 0}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 1458, "endOffset": 1494, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 1514, "endOffset": 2442, "count": 0}], "isBlockCoverage": true}, {"functionName": "getDefaultLighting", "ranges": [{"startOffset": 2453, "endOffset": 2964, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLightIcon", "ranges": [{"startOffset": 2975, "endOffset": 3255, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLightTypeName", "ranges": [{"startOffset": 3266, "endOffset": 3551, "count": 0}], "isBlockCoverage": true}, {"functionName": "createLight", "ranges": [{"startOffset": 3562, "endOffset": 4414, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectLight", "ranges": [{"startOffset": 4425, "endOffset": 4491, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteLight", "ranges": [{"startOffset": 4502, "endOffset": 5004, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLighting", "ranges": [{"startOffset": 5015, "endOffset": 6089, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetLighting", "ranges": [{"startOffset": 6100, "endOffset": 6632, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 10054, "endOffset": 40902, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1448", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1449", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/%00plugin-vue:export-helper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_exports__.default", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 4}, {"startOffset": 308, "endOffset": 336, "count": 12}], "isBlockCoverage": true}]}, {"scriptId": "1458", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 94781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 94781, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1065, "endOffset": 2859, "count": 0}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2880, "endOffset": 3025, "count": 0}], "isBlockCoverage": true}, {"functionName": "previewStyle", "ranges": [{"startOffset": 3036, "endOffset": 3554, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 3563, "endOffset": 3599, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 3619, "endOffset": 4201, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterMaterials", "ranges": [{"startOffset": 4208, "endOffset": 4907, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectCategory", "ranges": [{"startOffset": 4914, "endOffset": 5022, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectMaterialById", "ranges": [{"startOffset": 5029, "endOffset": 5206, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectMaterial", "ranges": [{"startOffset": 5213, "endOffset": 5501, "count": 0}], "isBlockCoverage": true}, {"functionName": "createNewMaterial", "ranges": [{"startOffset": 5508, "endOffset": 6072, "count": 0}], "isBlockCoverage": false}, {"functionName": "openTextureUpload", "ranges": [{"startOffset": 6079, "endOffset": 6202, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTextureSelected", "ranges": [{"startOffset": 6209, "endOffset": 6395, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTextureDrop", "ranges": [{"startOffset": 6402, "endOffset": 6601, "count": 0}], "isBlockCoverage": false}, {"functionName": "uploadTexture", "ranges": [{"startOffset": 6608, "endOffset": 7200, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeTexture", "ranges": [{"startOffset": 7207, "endOffset": 7357, "count": 0}], "isBlockCoverage": true}, {"functionName": "changePreviewShape", "ranges": [{"startOffset": 7364, "endOffset": 7430, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveMaterial", "ranges": [{"startOffset": 7437, "endOffset": 8547, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetMaterial", "ranges": [{"startOffset": 8554, "endOffset": 8695, "count": 0}], "isBlockCoverage": true}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 11354, "endOffset": 35182, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1459", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 574, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1469", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 168790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 168790, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1042, "endOffset": 1978, "count": 0}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1999, "endOffset": 2158, "count": 0}], "isBlockCoverage": true}, {"functionName": "totalOptionsPrice", "ranges": [{"startOffset": 2165, "endOffset": 2983, "count": 0}], "isBlockCoverage": true}, {"functionName": "totalPrice", "ranges": [{"startOffset": 2990, "endOffset": 3129, "count": 0}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 3138, "endOffset": 3174, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 3194, "endOffset": 3929, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterProducts", "ranges": [{"startOffset": 3936, "endOffset": 4322, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 4329, "endOffset": 4492, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectProductById", "ranges": [{"startOffset": 4499, "endOffset": 4668, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectProduct", "ranges": [{"startOffset": 4675, "endOffset": 5762, "count": 0}], "isBlockCoverage": true}, {"functionName": "addOptionGroup", "ranges": [{"startOffset": 5769, "endOffset": 5986, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeOptionGroup", "ranges": [{"startOffset": 5993, "endOffset": 6583, "count": 0}], "isBlockCoverage": true}, {"functionName": "addOption", "ranges": [{"startOffset": 6590, "endOffset": 6849, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeOption", "ranges": [{"startOffset": 6856, "endOffset": 7261, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectOption", "ranges": [{"startOffset": 7268, "endOffset": 9495, "count": 0}], "isBlockCoverage": true}, {"functionName": "isOptionSelected", "ranges": [{"startOffset": 9502, "endOffset": 9831, "count": 0}], "isBlockCoverage": true}, {"functionName": "isOptionDisabled", "ranges": [{"startOffset": 9920, "endOffset": 10030, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveConfiguration", "ranges": [{"startOffset": 10037, "endOffset": 10943, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetConfiguration", "ranges": [{"startOffset": 10950, "endOffset": 11111, "count": 0}], "isBlockCoverage": true}, {"functionName": "addDependency", "ranges": [{"startOffset": 11178, "endOffset": 11787, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeDependency", "ranges": [{"startOffset": 11794, "endOffset": 12037, "count": 0}], "isBlockCoverage": false}, {"functionName": "addIncompatibility", "ranges": [{"startOffset": 12044, "endOffset": 12698, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeIncompatibility", "ranges": [{"startOffset": 12705, "endOffset": 12968, "count": 0}], "isBlockCoverage": false}, {"functionName": "canSelectOption", "ranges": [{"startOffset": 13061, "endOffset": 14083, "count": 0}], "isBlockCoverage": true}, {"functionName": "areDependenciesSatisfied", "ranges": [{"startOffset": 14163, "endOffset": 14659, "count": 0}], "isBlockCoverage": false}, {"functionName": "addConfigRule", "ranges": [{"startOffset": 14698, "endOffset": 14998, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeConfigRule", "ranges": [{"startOffset": 15040, "endOffset": 15120, "count": 0}], "isBlockCoverage": false}, {"functionName": "showAddDependencyModal", "ranges": [{"startOffset": 15184, "endOffset": 15445, "count": 0}], "isBlockCoverage": false}, {"functionName": "hideAddDependencyModal", "ranges": [{"startOffset": 15452, "endOffset": 15524, "count": 0}], "isBlockCoverage": false}, {"functionName": "confirmAddDependency", "ranges": [{"startOffset": 15531, "endOffset": 15972, "count": 0}], "isBlockCoverage": false}, {"functionName": "showAddIncompatibilityModal", "ranges": [{"startOffset": 15979, "endOffset": 16250, "count": 0}], "isBlockCoverage": false}, {"functionName": "hideAddIncompatibilityModal", "ranges": [{"startOffset": 16257, "endOffset": 16339, "count": 0}], "isBlockCoverage": false}, {"functionName": "confirmAddIncompatibility", "ranges": [{"startOffset": 16346, "endOffset": 16802, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDependencyLabel", "ranges": [{"startOffset": 16875, "endOffset": 17231, "count": 0}], "isBlockCoverage": false}, {"functionName": "getIncompatibilityLabel", "ranges": [{"startOffset": 17238, "endOffset": 17624, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 22669, "endOffset": 63321, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1470", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 570, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1881", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70243, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 70243, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1043, "endOffset": 1726, "count": 0}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1747, "endOffset": 1872, "count": 0}], "isBlockCoverage": true}, {"functionName": "gridStyle", "ranges": [{"startOffset": 1883, "endOffset": 2002, "count": 0}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 2011, "endOffset": 2047, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 2067, "endOffset": 2978, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterProducts", "ranges": [{"startOffset": 2985, "endOffset": 3373, "count": 0}], "isBlockCoverage": true}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 3380, "endOffset": 3543, "count": 0}], "isBlockCoverage": true}, {"functionName": "to<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3550, "endOffset": 3608, "count": 0}], "isBlockCoverage": true}, {"functionName": "toggleSnap", "ranges": [{"startOffset": 3615, "endOffset": 3677, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomIn", "ranges": [{"startOffset": 3684, "endOffset": 3771, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomOut", "ranges": [{"startOffset": 3778, "endOffset": 3868, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetZoom", "ranges": [{"startOffset": 3875, "endOffset": 3920, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleViewMode", "ranges": [{"startOffset": 3927, "endOffset": 4011, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragStart", "ranges": [{"startOffset": 4018, "endOffset": 4172, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragOver", "ranges": [{"startOffset": 4179, "endOffset": 4234, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDrop", "ranges": [{"startOffset": 4241, "endOffset": 5110, "count": 0}], "isBlockCoverage": false}, {"functionName": "startDrag", "ranges": [{"startOffset": 5117, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "getItemStyle", "ranges": [{"startOffset": 6277, "endOffset": 6532, "count": 0}], "isBlockCoverage": true}, {"functionName": "removeItem", "ranges": [{"startOffset": 6539, "endOffset": 6713, "count": 0}], "isBlockCoverage": false}, {"functionName": "rotateItem", "ranges": [{"startOffset": 6720, "endOffset": 6794, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLayout", "ranges": [{"startOffset": 6801, "endOffset": 7520, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetLayout", "ranges": [{"startOffset": 7527, "endOffset": 7660, "count": 0}], "isBlockCoverage": true}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 9284, "endOffset": 24845, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1882", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 570, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "2429", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/VisualEditors.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35673, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35673, "count": 1}, {"startOffset": 685, "endOffset": 35672, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2629, "endOffset": 12139, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2430", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 69999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 69999, "count": 1}, {"startOffset": 1128, "endOffset": 69998, "count": 0}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2231, "endOffset": 2952, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatLastSaved", "ranges": [{"startOffset": 2973, "endOffset": 3701, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 3710, "endOffset": 3753, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadInitialData", "ranges": [{"startOffset": 3773, "endOffset": 4764, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadShowrooms", "ranges": [{"startOffset": 4771, "endOffset": 5144, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadProducts", "ranges": [{"startOffset": 5151, "endOffset": 5513, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadMaterials", "ranges": [{"startOffset": 5520, "endOffset": 5886, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimations", "ranges": [{"startOffset": 5893, "endOffset": 6263, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTab", "ranges": [{"startOffset": 6270, "endOffset": 6937, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLayoutUpdate", "ranges": [{"startOffset": 6944, "endOffset": 7381, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleProductUpdate", "ranges": [{"startOffset": 7388, "endOffset": 7823, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleMaterialUpdate", "ranges": [{"startOffset": 7830, "endOffset": 8271, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLightingUpdate", "ranges": [{"startOffset": 8278, "endOffset": 8398, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleAnimationUpdate", "ranges": [{"startOffset": 8405, "endOffset": 8852, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateLastSaved", "ranges": [{"startOffset": 8859, "endOffset": 8919, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleAutoSave", "ranges": [{"startOffset": 8926, "endOffset": 9002, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 10431, "endOffset": 26896, "count": 0}], "isBlockCoverage": false}]}]}