{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 27}, {"startOffset": 1132, "endOffset": 1139, "count": 24}], "isBlockCoverage": true}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 13}], "isBlockCoverage": true}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 2}], "isBlockCoverage": true}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 21}], "isBlockCoverage": true}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/tests/WizardContainer.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 74225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 74225, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateStepData", "ranges": [{"startOffset": 819, "endOffset": 891, "count": 0}], "isBlockCoverage": false}, {"functionName": "validate", "ranges": [{"startOffset": 897, "endOffset": 970, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1009, "endOffset": 13799, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1580, "endOffset": 2108, "count": 21}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2124, "endOffset": 2184, "count": 21}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2218, "endOffset": 2270, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2325, "endOffset": 2487, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2526, "endOffset": 3002, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3049, "endOffset": 3134, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3201, "endOffset": 3602, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3676, "endOffset": 3960, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4031, "endOffset": 4405, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4485, "endOffset": 4977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5041, "endOffset": 5453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5504, "endOffset": 6759, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6820, "endOffset": 7336, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7409, "endOffset": 7738, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7808, "endOffset": 8131, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8184, "endOffset": 8439, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8496, "endOffset": 8847, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8906, "endOffset": 9333, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9389, "endOffset": 10032, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10089, "endOffset": 10532, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10600, "endOffset": 11244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11293, "endOffset": 12584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12653, "endOffset": 13795, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/WizardContainer.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43133, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43133, "count": 1}], "isBlockCoverage": true}, {"functionName": "validator", "ranges": [{"startOffset": 2135, "endOffset": 2298, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2175, "endOffset": 2279, "count": 72}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 2943, "endOffset": 2953, "count": 22}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2968, "endOffset": 3180, "count": 24}], "isBlockCoverage": true}, {"functionName": "currentStep", "ranges": [{"startOffset": 3201, "endOffset": 3270, "count": 32}], "isBlockCoverage": true}, {"functionName": "currentStepData", "ranges": [{"startOffset": 3277, "endOffset": 3360, "count": 34}, {"startOffset": 3348, "endOffset": 3353, "count": 32}], "isBlockCoverage": true}, {"functionName": "isFirstStep", "ranges": [{"startOffset": 3367, "endOffset": 3430, "count": 32}], "isBlockCoverage": true}, {"functionName": "isLastStep", "ranges": [{"startOffset": 3437, "endOffset": 3519, "count": 32}], "isBlockCoverage": true}, {"functionName": "isCurrentStepValid", "ranges": [{"startOffset": 3526, "endOffset": 3612, "count": 42}], "isBlockCoverage": true}, {"functionName": "currentStepDependenciesValid", "ranges": [{"startOffset": 3619, "endOffset": 3906, "count": 12}, {"startOffset": 3687, "endOffset": 3699, "count": 0}, {"startOffset": 3854, "endOffset": 3859, "count": 10}], "isBlockCoverage": true}, {"functionName": "canProceedToNextStep", "ranges": [{"startOffset": 3913, "endOffset": 4018, "count": 38}, {"startOffset": 3975, "endOffset": 4011, "count": 10}], "isBlockCoverage": true}, {"functionName": "currentStepDependencyErrors", "ranges": [{"startOffset": 4025, "endOffset": 4104, "count": 35}, {"startOffset": 4092, "endOffset": 4097, "count": 0}], "isBlockCoverage": true}, {"functionName": "progressPercentage", "ranges": [{"startOffset": 4111, "endOffset": 4333, "count": 35}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 4342, "endOffset": 4690, "count": 24}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4490, "endOffset": 4536, "count": 2}, {"startOffset": 4531, "endOffset": 4536, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4544, "endOffset": 4619, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 4695, "endOffset": 4832, "count": 24}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4837, "endOffset": 4971, "count": 23}], "isBlockCoverage": true}, {"functionName": "isStepCompleted", "ranges": [{"startOffset": 4991, "endOffset": 5063, "count": 2161}], "isBlockCoverage": true}, {"functionName": "isStepAccessible", "ranges": [{"startOffset": 5070, "endOffset": 5453, "count": 1171}, {"startOffset": 5262, "endOffset": 5274, "count": 390}, {"startOffset": 5274, "endOffset": 5314, "count": 781}, {"startOffset": 5314, "endOffset": 5326, "count": 71}, {"startOffset": 5326, "endOffset": 5366, "count": 710}, {"startOffset": 5366, "endOffset": 5427, "count": 795}, {"startOffset": 5406, "endOffset": 5419, "count": 585}, {"startOffset": 5427, "endOffset": 5452, "count": 125}], "isBlockCoverage": true}, {"functionName": "handleStepClick", "ranges": [{"startOffset": 5460, "endOffset": 5598, "count": 0}], "isBlockCoverage": false}, {"functionName": "goToStep", "ranges": [{"startOffset": 5605, "endOffset": 5763, "count": 8}], "isBlockCoverage": true}, {"functionName": "goToNextStep", "ranges": [{"startOffset": 5770, "endOffset": 5909, "count": 3}], "isBlockCoverage": true}, {"functionName": "goToPreviousStep", "ranges": [{"startOffset": 5916, "endOffset": 6031, "count": 2}], "isBlockCoverage": true}, {"functionName": "updateStepData", "ranges": [{"startOffset": 6038, "endOffset": 6380, "count": 1}], "isBlockCoverage": true}, {"functionName": "updateStepDataWithoutPropagation", "ranges": [{"startOffset": 6387, "endOffset": 6607, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateCurrentStep", "ranges": [{"startOffset": 6614, "endOffset": 6746, "count": 9}], "isBlockCoverage": true}, {"functionName": "completeWizard", "ranges": [{"startOffset": 6753, "endOffset": 6962, "count": 2}], "isBlockCoverage": true}, {"functionName": "saveProgress", "ranges": [{"startOffset": 6969, "endOffset": 7355, "count": 12}], "isBlockCoverage": true}, {"functionName": "loadSavedProgress", "ranges": [{"startOffset": 7362, "endOffset": 7980, "count": 24}, {"startOffset": 7493, "endOffset": 7879, "count": 1}, {"startOffset": 7636, "endOffset": 7641, "count": 0}, {"startOffset": 7701, "endOffset": 7706, "count": 0}, {"startOffset": 7772, "endOffset": 7776, "count": 0}, {"startOffset": 7888, "endOffset": 7974, "count": 0}], "isBlockCoverage": true}, {"functionName": "clearSavedProgress", "ranges": [{"startOffset": 7987, "endOffset": 8063, "count": 2}], "isBlockCoverage": true}, {"functionName": "toggleHelp", "ranges": [{"startOffset": 8070, "endOffset": 8140, "count": 2}], "isBlockCoverage": true}, {"functionName": "trackStepView", "ranges": [{"startOffset": 8147, "endOffset": 8390, "count": 9}], "isBlockCoverage": true}, {"functionName": "trackWizardStart", "ranges": [{"startOffset": 8397, "endOffset": 8567, "count": 24}], "isBlockCoverage": true}, {"functionName": "trackWizardCompletion", "ranges": [{"startOffset": 8574, "endOffset": 8752, "count": 2}], "isBlockCoverage": true}, {"functionName": "handleKeyboardNavigation", "ranges": [{"startOffset": 8759, "endOffset": 11327, "count": 3}, {"startOffset": 9037, "endOffset": 9062, "count": 0}, {"startOffset": 9135, "endOffset": 9153, "count": 1}, {"startOffset": 9162, "endOffset": 9374, "count": 1}, {"startOffset": 9384, "endOffset": 9401, "count": 1}, {"startOffset": 9410, "endOffset": 9600, "count": 1}, {"startOffset": 9610, "endOffset": 9816, "count": 0}, {"startOffset": 9826, "endOffset": 10303, "count": 0}, {"startOffset": 10313, "endOffset": 10454, "count": 0}, {"startOffset": 10464, "endOffset": 10829, "count": 0}, {"startOffset": 10839, "endOffset": 10848, "count": 0}, {"startOffset": 10849, "endOffset": 10858, "count": 0}, {"startOffset": 10859, "endOffset": 10868, "count": 1}, {"startOffset": 10869, "endOffset": 10878, "count": 1}, {"startOffset": 10879, "endOffset": 10888, "count": 1}, {"startOffset": 10897, "endOffset": 10906, "count": 1}, {"startOffset": 10907, "endOffset": 10916, "count": 1}, {"startOffset": 10917, "endOffset": 10926, "count": 1}, {"startOffset": 10927, "endOffset": 11313, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 11608, "endOffset": 11714, "count": 24}, {"startOffset": 11677, "endOffset": 11712, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12161, "endOffset": 12250, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12374, "endOffset": 12457, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12506, "endOffset": 12550, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/utils/StepDependencyManager.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 39707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 39707, "count": 1}], "isBlockCoverage": true}, {"functionName": "StepDependencyManager", "ranges": [{"startOffset": 685, "endOffset": 867, "count": 24}, {"startOffset": 779, "endOffset": 784, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateDependencies", "ranges": [{"startOffset": 1075, "endOffset": 3920, "count": 12}, {"startOffset": 1200, "endOffset": 1286, "count": 10}, {"startOffset": 1286, "endOffset": 1904, "count": 2}, {"startOffset": 1707, "endOffset": 1743, "count": 1}, {"startOffset": 1744, "endOffset": 1778, "count": 1}, {"startOffset": 1780, "endOffset": 1890, "count": 1}, {"startOffset": 1812, "endOffset": 1878, "count": 0}, {"startOffset": 1904, "endOffset": 1985, "count": 2}, {"startOffset": 1985, "endOffset": 3842, "count": 0}, {"startOffset": 3842, "endOffset": 3919, "count": 2}], "isBlockCoverage": true}, {"functionName": "propagateData", "ranges": [{"startOffset": 4116, "endOffset": 5710, "count": 1}, {"startOffset": 4305, "endOffset": 5706, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4602, "endOffset": 4650, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDisabledFields", "ranges": [{"startOffset": 5905, "endOffset": 7088, "count": 78}, {"startOffset": 6026, "endOffset": 6060, "count": 4}, {"startOffset": 6086, "endOffset": 7087, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/WizardContainer.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34076, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34076, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 5658, "count": 78}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 2917, "endOffset": 3346, "count": 1}, {"startOffset": 3347, "endOffset": 3357, "count": 77}, {"startOffset": 3697, "endOffset": 3701, "count": 0}, {"startOffset": 3974, "endOffset": 4106, "count": 12}, {"startOffset": 4107, "endOffset": 4117, "count": 66}, {"startOffset": 4135, "endOffset": 4357, "count": 74}, {"startOffset": 4358, "endOffset": 4584, "count": 4}, {"startOffset": 4743, "endOffset": 4753, "count": 0}, {"startOffset": 5136, "endOffset": 5149, "count": 1}, {"startOffset": 5150, "endOffset": 5163, "count": 77}, {"startOffset": 5196, "endOffset": 5627, "count": 1}, {"startOffset": 5373, "endOffset": 5614, "count": 0}, {"startOffset": 5628, "endOffset": 5638, "count": 77}, {"startOffset": 5641, "endOffset": 5651, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 917, "endOffset": 2811, "count": 234}, {"startOffset": 1508, "endOffset": 1516, "count": 78}, {"startOffset": 1517, "endOffset": 1523, "count": 156}, {"startOffset": 2151, "endOffset": 2282, "count": 37}, {"startOffset": 2283, "endOffset": 2420, "count": 197}, {"startOffset": 2535, "endOffset": 2565, "count": 117}, {"startOffset": 2566, "endOffset": 2599, "count": 80}, {"startOffset": 2600, "endOffset": 2677, "count": 25}, {"startOffset": 2678, "endOffset": 2688, "count": 209}, {"startOffset": 2719, "endOffset": 2793, "count": 117}, {"startOffset": 2794, "endOffset": 2804, "count": 117}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 1562, "endOffset": 1636, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.keydown", "ranges": [{"startOffset": 1658, "endOffset": 1848, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.keydown", "ranges": [{"startOffset": 1850, "endOffset": 2050, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3182, "endOffset": 3339, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5809, "endOffset": 5831, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5938, "endOffset": 5969, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/WizardContainer.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1167", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 24}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 23}], "isBlockCoverage": true}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2754, "endOffset": 2777, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 1}, {"startOffset": 2801, "endOffset": 2809, "count": 0}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}