/**
 * WebSocket Integration Tests
 * Comprehensive tests for real-time WebSocket functionality
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import WebSocket from 'ws';
import { WebSocketManager } from '../../services/realtime/websocket-manager.js';
import { EventDispatcher } from '../../services/realtime/event-dispatcher.js';
import { ConnectionPool } from '../../services/realtime/connection-pool.js';
import { MessageRouter } from '../../services/realtime/message-router.js';
import { SyncEngine } from '../../services/realtime/sync-engine.js';

describe('WebSocket Integration Tests', () => {
  let wsManager;
  let eventDispatcher;
  let connectionPool;
  let messageRouter;
  let syncEngine;
  let testPort;
  let testClients = [];

  beforeAll(async () => {
    testPort = 8081; // Use different port for testing
    
    // Initialize services
    wsManager = new WebSocketManager({ 
      port: testPort,
      maxConnections: 100,
      heartbeatInterval: 5000
    });
    
    eventDispatcher = new EventDispatcher({
      enablePersistence: false, // Disable for testing
      enableBatching: true,
      batchTimeout: 100
    });
    
    connectionPool = new ConnectionPool({
      maxPoolSize: 50,
      maxConnectionsPerPool: 10
    });
    
    messageRouter = new MessageRouter({
      processingInterval: 10
    });
    
    syncEngine = new SyncEngine({
      enableVersioning: true,
      conflictResolutionStrategy: 'last-write-wins'
    });

    // Wait for services to be ready
    await Promise.all([
      new Promise(resolve => wsManager.once('ready', resolve)),
      new Promise(resolve => eventDispatcher.once('ready', resolve)),
      new Promise(resolve => connectionPool.once('ready', resolve)),
      new Promise(resolve => messageRouter.once('ready', resolve)),
      new Promise(resolve => syncEngine.once('ready', resolve))
    ]);

    // Setup service integrations
    setupTestIntegrations();
  });

  afterAll(async () => {
    // Close all test clients
    await Promise.all(testClients.map(client => closeClient(client)));
    testClients = [];

    // Shutdown services
    await Promise.all([
      wsManager.shutdown(),
      eventDispatcher.shutdown(),
      connectionPool.shutdown(),
      messageRouter.shutdown(),
      syncEngine.shutdown()
    ]);
  });

  beforeEach(() => {
    // Clear any existing test clients
    testClients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        client.close();
      }
    });
    testClients = [];
  });

  afterEach(async () => {
    // Clean up test clients after each test
    await Promise.all(testClients.map(client => closeClient(client)));
    testClients = [];
  });

  function setupTestIntegrations() {
    // Connect WebSocket Manager to Event Dispatcher
    wsManager.on('message', ({ connection, message }) => {
      eventDispatcher.publish('websocket.message', {
        connectionId: connection.id,
        message,
        clientInfo: connection.clientInfo
      });
    });

    // Connect Event Dispatcher to Message Router
    eventDispatcher.subscribe('websocket.message', async (event) => {
      await messageRouter.routeMessage(event.data.message, {
        source: 'websocket',
        connectionId: event.data.connectionId
      });
    });

    // Add test routes
    messageRouter.addRoute('type:test', {
      target: 'test-handler',
      priority: 5
    });

    messageRouter.addRoute('type:sync', {
      target: 'sync-engine',
      priority: 8
    });
  }

  async function createTestClient(options = {}) {
    return new Promise((resolve, reject) => {
      const client = new WebSocket(`ws://localhost:${testPort}`, options.protocols);
      
      client.on('open', () => {
        testClients.push(client);
        resolve(client);
      });
      
      client.on('error', reject);
      
      // Set timeout for connection
      setTimeout(() => {
        if (client.readyState !== WebSocket.OPEN) {
          reject(new Error('Connection timeout'));
        }
      }, 5000);
    });
  }

  async function closeClient(client) {
    return new Promise((resolve) => {
      if (client.readyState === WebSocket.CLOSED) {
        resolve();
        return;
      }
      
      client.on('close', resolve);
      client.close();
      
      // Force close after timeout
      setTimeout(() => {
        if (client.readyState !== WebSocket.CLOSED) {
          client.terminate();
        }
        resolve();
      }, 1000);
    });
  }

  function sendMessage(client, message) {
    return new Promise((resolve, reject) => {
      if (client.readyState !== WebSocket.OPEN) {
        reject(new Error('Client not connected'));
        return;
      }
      
      client.send(JSON.stringify(message));
      resolve();
    });
  }

  function waitForMessage(client, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('Message timeout'));
      }, timeout);
      
      client.once('message', (data) => {
        clearTimeout(timer);
        try {
          const message = JSON.parse(data.toString());
          resolve(message);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  describe('WebSocket Manager', () => {
    it('should accept WebSocket connections', async () => {
      const client = await createTestClient();
      expect(client.readyState).toBe(WebSocket.OPEN);
      
      // Should receive welcome message
      const welcomeMessage = await waitForMessage(client);
      expect(welcomeMessage.type).toBe('connection');
      expect(welcomeMessage.connectionId).toBeDefined();
    });

    it('should handle multiple concurrent connections', async () => {
      const clients = await Promise.all([
        createTestClient(),
        createTestClient(),
        createTestClient()
      ]);

      expect(clients).toHaveLength(3);
      clients.forEach(client => {
        expect(client.readyState).toBe(WebSocket.OPEN);
      });

      // Check metrics
      const metrics = wsManager.getMetrics();
      expect(metrics.activeConnections).toBeGreaterThanOrEqual(3);
    });

    it('should handle authentication', async () => {
      const client = await createTestClient();
      await waitForMessage(client); // Welcome message

      // Send authentication
      await sendMessage(client, {
        type: 'authenticate',
        payload: {
          token: 'valid-test-token',
          userId: 'test-user-123'
        }
      });

      const authResponse = await waitForMessage(client);
      expect(authResponse.type).toBe('authenticated');
      expect(authResponse.userId).toBe('test-user-123');
    });

    it('should handle subscription and broadcasting', async () => {
      const client1 = await createTestClient();
      const client2 = await createTestClient();
      
      await waitForMessage(client1); // Welcome message
      await waitForMessage(client2); // Welcome message

      // Subscribe to channel
      await sendMessage(client1, {
        type: 'subscribe',
        payload: { channel: 'test-channel' }
      });

      const subResponse = await waitForMessage(client1);
      expect(subResponse.type).toBe('subscribed');
      expect(subResponse.channel).toBe('test-channel');

      // Broadcast message
      await wsManager.broadcast('test-channel', { message: 'Hello World!' });

      const broadcastMessage = await waitForMessage(client1);
      expect(broadcastMessage.type).toBe('broadcast');
      expect(broadcastMessage.channel).toBe('test-channel');
      expect(broadcastMessage.data.message).toBe('Hello World!');
    });

    it('should handle heartbeat and connection timeout', async () => {
      const client = await createTestClient();
      await waitForMessage(client); // Welcome message

      // Send ping
      await sendMessage(client, { type: 'ping' });

      const pongResponse = await waitForMessage(client);
      expect(pongResponse.type).toBe('pong');
      expect(pongResponse.timestamp).toBeDefined();
    });
  });

  describe('Event Dispatcher', () => {
    it('should publish and deliver events', async () => {
      let receivedEvent = null;
      
      const subscriptionId = eventDispatcher.subscribe('test-channel', (event) => {
        receivedEvent = event;
      });

      await eventDispatcher.publish('test-channel', { test: 'data' });

      // Wait for event delivery
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(receivedEvent).toBeDefined();
      expect(receivedEvent.data.test).toBe('data');
      expect(receivedEvent.channel).toBe('test-channel');

      eventDispatcher.unsubscribe('test-channel', subscriptionId);
    });

    it('should handle event filtering', async () => {
      let receivedEvents = [];
      
      const subscriptionId = eventDispatcher.subscribe('filter-test', (event) => {
        receivedEvents.push(event);
      }, {
        filter: (event) => event.data.important === true
      });

      // Publish events with and without filter criteria
      await eventDispatcher.publish('filter-test', { important: true, message: 'Important' });
      await eventDispatcher.publish('filter-test', { important: false, message: 'Not important' });
      await eventDispatcher.publish('filter-test', { important: true, message: 'Also important' });

      // Wait for event delivery
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(receivedEvents).toHaveLength(2);
      expect(receivedEvents[0].data.message).toBe('Important');
      expect(receivedEvents[1].data.message).toBe('Also important');

      eventDispatcher.unsubscribe('filter-test', subscriptionId);
    });

    it('should handle batch delivery', async () => {
      let batchedEvents = [];
      
      const subscriptionId = eventDispatcher.subscribe('batch-test', (events) => {
        if (Array.isArray(events)) {
          batchedEvents.push(...events);
        } else {
          batchedEvents.push(events);
        }
      }, {
        supportsBatch: true
      });

      // Publish multiple events quickly
      for (let i = 0; i < 5; i++) {
        await eventDispatcher.publish('batch-test', { index: i });
      }

      // Wait for batch processing
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(batchedEvents.length).toBeGreaterThan(0);

      eventDispatcher.unsubscribe('batch-test', subscriptionId);
    });
  });

  describe('Connection Pool', () => {
    it('should manage connection pools', async () => {
      const client = await createTestClient();
      await waitForMessage(client); // Welcome message

      // Get connection info from WebSocket manager
      const connections = Array.from(wsManager.connections.keys());
      expect(connections.length).toBeGreaterThan(0);

      const connectionId = connections[0];
      const connection = wsManager.getConnectionInfo(connectionId);

      // Add connection to pool
      const poolId = connectionPool.addConnection(connectionId, connection);
      expect(poolId).toBeDefined();

      // Check pool metrics
      const metrics = connectionPool.getMetrics();
      expect(metrics.totalConnections).toBeGreaterThan(0);
      expect(metrics.activeConnections).toBeGreaterThan(0);

      // Remove connection
      const removed = connectionPool.removeConnection(connectionId);
      expect(removed).toBe(true);
    });

    it('should handle load balancing', async () => {
      // Create multiple pools
      const pool1 = connectionPool.createPool('test-pool-1', { maxConnections: 2 });
      const pool2 = connectionPool.createPool('test-pool-2', { maxConnections: 2 });

      expect(pool1.id).toBe('test-pool-1');
      expect(pool2.id).toBe('test-pool-2');

      // Create connections and test load balancing
      const clients = await Promise.all([
        createTestClient(),
        createTestClient(),
        createTestClient()
      ]);

      // Wait for welcome messages
      await Promise.all(clients.map(client => waitForMessage(client)));

      const connections = Array.from(wsManager.connections.keys());
      
      // Add connections to pools
      const poolAssignments = connections.map(connectionId => {
        const connection = wsManager.getConnectionInfo(connectionId);
        return connectionPool.addConnection(connectionId, connection);
      });

      // Should distribute across pools
      const uniquePools = new Set(poolAssignments);
      expect(uniquePools.size).toBeGreaterThan(1);
    });
  });

  describe('Message Router', () => {
    it('should route messages based on patterns', async () => {
      let routedMessages = [];
      
      messageRouter.on('messageDelivery', (delivery) => {
        routedMessages.push(delivery);
      });

      const client = await createTestClient();
      await waitForMessage(client); // Welcome message

      // Send test message
      await sendMessage(client, {
        type: 'test',
        data: { content: 'Test message' }
      });

      // Wait for routing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(routedMessages.length).toBeGreaterThan(0);
      const routed = routedMessages.find(msg => msg.message.type === 'test');
      expect(routed).toBeDefined();
      expect(routed.target).toBe('test-handler');
    });

    it('should apply filters and transformers', async () => {
      // Add filter
      messageRouter.addFilter('test-filter', (message) => {
        return message.data && message.data.allowed === true;
      });

      // Add transformer
      messageRouter.addTransformer('test-transformer', (message) => {
        return {
          ...message,
          transformed: true,
          timestamp: Date.now()
        };
      });

      // Add route with filter and transformer
      messageRouter.addRoute('type:filtered-test', {
        target: 'filtered-handler',
        priority: 7,
        filters: ['test-filter'],
        transformers: ['test-transformer']
      });

      let deliveredMessages = [];
      messageRouter.on('messageDelivery', (delivery) => {
        if (delivery.target === 'filtered-handler') {
          deliveredMessages.push(delivery);
        }
      });

      const client = await createTestClient();
      await waitForMessage(client); // Welcome message

      // Send message that should be filtered out
      await sendMessage(client, {
        type: 'filtered-test',
        data: { allowed: false, content: 'Should be filtered' }
      });

      // Send message that should pass
      await sendMessage(client, {
        type: 'filtered-test',
        data: { allowed: true, content: 'Should pass' }
      });

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(deliveredMessages).toHaveLength(1);
      expect(deliveredMessages[0].message.transformed).toBe(true);
      expect(deliveredMessages[0].message.data.content).toBe('Should pass');
    });
  });

  describe('Sync Engine', () => {
    it('should synchronize entity updates', async () => {
      const entityId = 'test-entity-1';
      const testData = { name: 'Test Entity', value: 42 };

      // Update entity
      const result = await syncEngine.updateEntity(entityId, testData, {
        clientId: 'test-client'
      });

      expect(result.data).toEqual(expect.objectContaining(testData));
      expect(result.version).toBe(1);
      expect(result.operationId).toBeDefined();

      // Get entity
      const entity = syncEngine.getEntity(entityId);
      expect(entity).toBeDefined();
      expect(entity.data).toEqual(expect.objectContaining(testData));
      expect(entity.version).toBe(1);
    });

    it('should handle conflict resolution', async () => {
      const entityId = 'conflict-test-entity';
      
      // Create initial entity
      await syncEngine.updateEntity(entityId, { value: 1 }, {
        clientId: 'client-1'
      });

      // Simulate concurrent updates with version conflict
      const update1 = syncEngine.updateEntity(entityId, { value: 2 }, {
        clientId: 'client-1',
        version: 1
      });

      const update2 = syncEngine.updateEntity(entityId, { value: 3 }, {
        clientId: 'client-2',
        version: 1 // Same version - should cause conflict
      });

      const results = await Promise.allSettled([update1, update2]);
      
      // Both should succeed due to last-write-wins strategy
      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('fulfilled');

      const finalEntity = syncEngine.getEntity(entityId);
      expect(finalEntity.version).toBeGreaterThan(1);
    });

    it('should handle subscriptions and notifications', async () => {
      const entityId = 'subscription-test-entity';
      let notifications = [];

      // Subscribe to entity
      syncEngine.on('subscriberNotification', (notification) => {
        if (notification.subscription.clientId === 'test-subscriber') {
          notifications.push(notification);
        }
      });

      const subscription = syncEngine.subscribe(entityId, 'test-subscriber');
      expect(subscription).toBeDefined();

      // Update entity
      await syncEngine.updateEntity(entityId, { status: 'active' }, {
        clientId: 'updater'
      });

      // Wait for notification
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(notifications.length).toBeGreaterThan(0);
      const updateNotification = notifications.find(n => 
        n.notification.type === 'entity-updated'
      );
      expect(updateNotification).toBeDefined();
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle end-to-end real-time sync', async () => {
      const client1 = await createTestClient();
      const client2 = await createTestClient();
      
      await waitForMessage(client1); // Welcome message
      await waitForMessage(client2); // Welcome message

      // Authenticate clients
      await sendMessage(client1, {
        type: 'authenticate',
        payload: { token: 'valid-token', userId: 'user1' }
      });
      
      await sendMessage(client2, {
        type: 'authenticate',
        payload: { token: 'valid-token', userId: 'user2' }
      });

      await waitForMessage(client1); // Auth response
      await waitForMessage(client2); // Auth response

      // Subscribe to sync channel
      await sendMessage(client1, {
        type: 'subscribe',
        payload: { channel: 'sync-updates' }
      });

      await waitForMessage(client1); // Subscription confirmation

      // Send sync update from client2
      await sendMessage(client2, {
        type: 'sync',
        entityId: 'shared-entity',
        data: { content: 'Updated from client2' },
        version: 1
      });

      // Client1 should receive the sync update
      const syncUpdate = await waitForMessage(client1, 2000);
      expect(syncUpdate.type).toBe('broadcast');
      expect(syncUpdate.channel).toBe('sync-updates');
    });

    it('should handle high-frequency updates', async () => {
      const client = await createTestClient();
      await waitForMessage(client); // Welcome message

      const startTime = Date.now();
      const messageCount = 100;
      let receivedCount = 0;

      client.on('message', () => {
        receivedCount++;
      });

      // Send many messages quickly
      for (let i = 0; i < messageCount; i++) {
        await sendMessage(client, {
          type: 'test',
          index: i,
          timestamp: Date.now()
        });
      }

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 1000));

      const endTime = Date.now();
      const duration = endTime - startTime;
      const messagesPerSecond = messageCount / (duration / 1000);

      console.log(`Processed ${messageCount} messages in ${duration}ms (${messagesPerSecond.toFixed(2)} msg/s)`);
      
      // Should handle at least 50 messages per second
      expect(messagesPerSecond).toBeGreaterThan(50);
    });
  });
});
