{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/ProductConfiguratorMinimal.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10575, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10575, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1085, "endOffset": 1166, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1426, "endOffset": 2353, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1485, "endOffset": 1798, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1858, "endOffset": 2033, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2093, "endOffset": 2194, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2267, "endOffset": 2349, "count": 1}], "isBlockCoverage": true}]}]}