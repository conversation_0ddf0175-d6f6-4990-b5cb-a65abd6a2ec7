/**
 * Testable Database Optimization Tests
 * 
 * This demonstrates how the refactored database optimization module
 * with dependency injection is much easier to test.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DIContainer } from '../../shared/utils/dependency-injection.js';

// Import the testable module
const createDatabaseOptimization = require('../../shared/utils/database-optimization-testable.js');

describe('Database Optimization (Testable Version)', () => {
  let container: DIContainer;
  let mockSupabase: any;
  let mockRedis: any;
  let mockLogger: any;
  let dbOptimization: any;

  beforeEach(() => {
    // Create test container
    container = new DIContainer();

    // Create simple, focused mocks
    mockSupabase = {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      gt: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
    };

    mockRedis = {
      get: vi.fn(),
      set: vi.fn(),
      keys: vi.fn(),
      del: vi.fn(),
    };

    mockLogger = {
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    };

    // Register mocks in test container
    container.register('supabaseClient', mockSupabase);
    container.register('redisClient', mockRedis);
    container.register('logger', mockLogger);

    // Create module instance with test dependencies
    dbOptimization = createDatabaseOptimization(container);
  });

  describe('optimizedQuery', () => {
    it('should build and execute a basic select query', async () => {
      // Setup mock response
      const mockResult = { data: [{ id: 1, name: 'Test' }], error: null };
      
      // Make the final method in the chain return a promise
      mockSupabase.select.mockResolvedValue(mockResult);

      // Execute query
      const query = dbOptimization.optimizedQuery('users');
      const result = await query.select();

      // Verify calls
      expect(mockSupabase.from).toHaveBeenCalledWith('users');
      expect(mockSupabase.select).toHaveBeenCalledWith('*');
      expect(result).toEqual(mockResult);
    });

    it('should apply filters correctly', async () => {
      const mockResult = { data: [{ id: 1, name: 'Test' }], error: null };
      mockSupabase.eq.mockResolvedValue(mockResult);

      const query = dbOptimization.optimizedQuery('users');
      const result = await query.select('*', {
        filters: { id: 1, status: 'active' }
      });

      expect(mockSupabase.eq).toHaveBeenCalledWith('id', 1);
      expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active');
      expect(result).toEqual(mockResult);
    });

    it('should handle caching when enabled', async () => {
      const mockResult = { data: [{ id: 1, name: 'Test' }], error: null };
      mockSupabase.select.mockResolvedValue(mockResult);
      mockRedis.get.mockResolvedValue(null); // Cache miss
      mockRedis.set.mockResolvedValue('OK');

      const query = dbOptimization.optimizedQuery('users');
      await query.select('*', { cache: true, ttl: 300 });

      expect(mockRedis.get).toHaveBeenCalled();
      expect(mockRedis.set).toHaveBeenCalled();
    });

    it('should return cached result when available', async () => {
      const cachedResult = { data: [{ id: 1, name: 'Cached' }], error: null };
      mockRedis.get.mockResolvedValue(JSON.stringify(cachedResult));

      const query = dbOptimization.optimizedQuery('users');
      const result = await query.select('*', { cache: true });

      expect(mockRedis.get).toHaveBeenCalled();
      expect(mockSupabase.select).not.toHaveBeenCalled(); // Should not hit database
      expect(result).toEqual(cachedResult);
    });

    it('should log slow queries', async () => {
      const mockResult = { data: [{ id: 1, name: 'Test' }], error: null };
      
      // Mock Date.now to simulate slow query
      const originalDateNow = Date.now;
      Date.now = vi.fn()
        .mockReturnValueOnce(1000) // Start time
        .mockReturnValueOnce(1600); // End time (600ms elapsed)

      mockSupabase.select.mockResolvedValue(mockResult);

      const query = dbOptimization.optimizedQuery('users');
      await query.select();

      expect(mockLogger.warn).toHaveBeenCalledWith(
        'Slow query detected',
        expect.objectContaining({
          table: 'users',
          duration: 600
        })
      );

      // Restore Date.now
      Date.now = originalDateNow;
    });
  });

  describe('invalidateTableCache', () => {
    it('should delete cache keys for a table', async () => {
      const mockKeys = ['db_query:users:key1', 'db_query:users:key2'];
      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.del.mockResolvedValue(2);

      await dbOptimization.invalidateTableCache('users');

      expect(mockRedis.keys).toHaveBeenCalledWith('db_query:users:*');
      expect(mockRedis.del).toHaveBeenCalledWith(...mockKeys);
      expect(mockLogger.debug).toHaveBeenCalledWith(
        'Invalidated 2 cache entries for table: users'
      );
    });

    it('should handle empty cache gracefully', async () => {
      mockRedis.keys.mockResolvedValue([]);

      await dbOptimization.invalidateTableCache('users');

      expect(mockRedis.keys).toHaveBeenCalledWith('db_query:users:*');
      expect(mockRedis.del).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      const error = new Error('Redis error');
      mockRedis.keys.mockRejectedValue(error);

      await dbOptimization.invalidateTableCache('users');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Error invalidating table cache:',
        error
      );
    });
  });

  describe('generateCacheKey', () => {
    it('should generate consistent cache keys', () => {
      const key1 = dbOptimization.generateCacheKey('users', {
        filters: { id: 1 },
        limit: 10
      });
      
      const key2 = dbOptimization.generateCacheKey('users', {
        filters: { id: 1 },
        limit: 10
      });

      expect(key1).toBe(key2);
      expect(key1).toContain('users');
    });
  });
});
