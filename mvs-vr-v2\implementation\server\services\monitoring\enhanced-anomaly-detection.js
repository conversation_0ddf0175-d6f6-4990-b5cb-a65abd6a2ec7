/**
 * Enhanced Anomaly Detection Service
 *
 * This service provides advanced anomaly detection capabilities with multiple algorithms,
 * machine learning models, and comprehensive visualization support.
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { EventEmitter } from 'events';
import { fileURLToPath } from 'url';

const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple logger for services
const logger = {
  info: msg => console.log(`[INFO] ${msg}`),
  error: msg => console.error(`[ERROR] ${msg}`),
  debug: msg => console.log(`[DEBUG] ${msg}`),
};

// Anomaly detection algorithms
const ALGORITHMS = {
  Z_SCORE: 'z-score',
  MAD: 'mad', // Median Absolute Deviation
  IQR: 'iqr', // Interquartile Range
  ISOLATION_FOREST: 'isolation-forest',
  LSTM: 'lstm', // Long Short-Term Memory
  SEASONAL_DECOMPOSE: 'seasonal-decompose',
  CHANGE_POINT: 'change-point',
};

// Anomaly severity levels
const SEVERITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
};

// Anomaly types
const ANOMALY_TYPES = {
  POINT: 'point', // Single point anomaly
  CONTEXTUAL: 'contextual', // Anomaly in specific context
  COLLECTIVE: 'collective', // Group of points forming anomaly
  TREND: 'trend', // Trend change anomaly
  SEASONAL: 'seasonal', // Seasonal pattern anomaly
};

class EnhancedAnomalyDetectionService extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      algorithms: [ALGORITHMS.Z_SCORE, ALGORITHMS.MAD, ALGORITHMS.IQR],
      defaultThreshold: 3.0,
      minSamples: 10,
      windowSize: 100,
      seasonalityPeriod: 24,
      confidenceLevel: 0.95,
      outputPath: options.outputPath || path.join(__dirname, '../../data/anomalies'),
      visualizationEnabled: options.visualizationEnabled !== false,
      realTimeProcessing: options.realTimeProcessing !== false,
      ...options,
    };

    // Anomaly storage
    this.detectedAnomalies = new Map();
    this.anomalyHistory = [];
    this.modelCache = new Map();

    // Statistics
    this.stats = {
      totalDetections: 0,
      detectionsByAlgorithm: {},
      detectionsBySeverity: {},
      detectionsByType: {},
      lastDetection: null,
      startTime: Date.now(),
    };

    this.initialize();
  }

  /**
   * Initialize the anomaly detection service
   */
  async initialize() {
    try {
      // Ensure output directory exists
      if (this.options.outputPath && !(await existsAsync(this.options.outputPath))) {
        await mkdirAsync(this.options.outputPath, { recursive: true });
      }

      // Initialize algorithm statistics
      for (const algorithm of this.options.algorithms) {
        this.stats.detectionsByAlgorithm[algorithm] = 0;
      }

      for (const severity of Object.values(SEVERITY_LEVELS)) {
        this.stats.detectionsBySeverity[severity] = 0;
      }

      for (const type of Object.values(ANOMALY_TYPES)) {
        this.stats.detectionsByType[type] = 0;
      }

      logger.info('Enhanced anomaly detection service initialized');
    } catch (error) {
      logger.error('Failed to initialize enhanced anomaly detection service:', error);
      throw error;
    }
  }

  /**
   * Detect anomalies in time series data
   * @param {Array<Object>} timeSeries - Time series data
   * @param {Object} options - Detection options
   * @returns {Promise<Array<Object>>} Detected anomalies
   */
  async detectAnomalies(timeSeries, options = {}) {
    const {
      algorithms = this.options.algorithms,
      metric = 'unknown',
      threshold = this.options.defaultThreshold,
      enableVisualization = this.options.visualizationEnabled,
    } = options;

    if (!timeSeries || timeSeries.length < this.options.minSamples) {
      logger.debug(
        `Insufficient data for anomaly detection. Required: ${this.options.minSamples}, Got: ${timeSeries?.length || 0}`,
      );
      return [];
    }

    const allAnomalies = [];

    // Run each algorithm
    for (const algorithm of algorithms) {
      try {
        const anomalies = this.runAlgorithm(algorithm, timeSeries, { metric, threshold });

        // Enrich anomalies with metadata
        const enrichedAnomalies = anomalies.map(anomaly => ({
          ...anomaly,
          algorithm,
          metric,
          detectedAt: new Date().toISOString(),
          id: this.generateAnomalyId(anomaly, algorithm, metric),
        }));

        allAnomalies.push(...enrichedAnomalies);

        // Update statistics
        this.stats.detectionsByAlgorithm[algorithm] += enrichedAnomalies.length;
      } catch (error) {
        logger.error(`Error running ${algorithm} algorithm:`, error);
      }
    }

    // Deduplicate and rank anomalies
    const uniqueAnomalies = this.deduplicateAnomalies(allAnomalies);
    const rankedAnomalies = this.rankAnomalies(uniqueAnomalies);

    // Store anomalies
    this.storeAnomalies(rankedAnomalies, metric);

    // Generate visualization if enabled
    if (enableVisualization && rankedAnomalies.length > 0) {
      await this.generateVisualization(timeSeries, rankedAnomalies, metric);
    }

    // Update global statistics
    this.updateStatistics(rankedAnomalies);

    // Emit events for real-time processing
    if (this.options.realTimeProcessing) {
      this.emitAnomalyEvents(rankedAnomalies);
    }

    logger.info(
      `Detected ${rankedAnomalies.length} anomalies for metric ${metric} using ${algorithms.length} algorithms`,
    );

    return rankedAnomalies;
  }

  /**
   * Run specific anomaly detection algorithm
   * @param {string} algorithm - Algorithm name
   * @param {Array<Object>} timeSeries - Time series data
   * @param {Object} options - Algorithm options
   * @returns {Array<Object>} Detected anomalies
   */
  runAlgorithm(algorithm, timeSeries, options = {}) {
    const { threshold = this.options.defaultThreshold } = options;

    switch (algorithm) {
      case ALGORITHMS.Z_SCORE:
        return this.detectZScoreAnomalies(timeSeries, threshold);

      case ALGORITHMS.MAD:
        return this.detectMADAnomalies(timeSeries, threshold);

      case ALGORITHMS.IQR:
        return this.detectIQRAnomalies(timeSeries);

      case ALGORITHMS.ISOLATION_FOREST:
        return this.detectIsolationForestAnomalies(timeSeries);

      case ALGORITHMS.SEASONAL_DECOMPOSE:
        return this.detectSeasonalAnomalies(timeSeries);

      case ALGORITHMS.CHANGE_POINT:
        return this.detectChangePointAnomalies(timeSeries);

      default:
        throw new Error(`Unknown algorithm: ${algorithm}`);
    }
  }

  /**
   * Z-Score anomaly detection
   * @param {Array<Object>} timeSeries - Time series data
   * @param {number} threshold - Z-score threshold
   * @returns {Array<Object>} Detected anomalies
   */
  detectZScoreAnomalies(timeSeries, threshold = 3.0) {
    const values = timeSeries.map(point => point.value);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    if (stdDev === 0) return [];

    const anomalies = [];

    timeSeries.forEach((point, index) => {
      const zScore = Math.abs((point.value - mean) / stdDev);

      if (zScore > threshold) {
        anomalies.push({
          timestamp: point.timestamp,
          value: point.value,
          score: zScore,
          severity: this.calculateSeverity(zScore, threshold),
          type: ANOMALY_TYPES.POINT,
          confidence: Math.min(zScore / threshold, 1.0),
          context: {
            mean,
            stdDev,
            threshold,
            index,
          },
        });
      }
    });

    return anomalies;
  }

  /**
   * Median Absolute Deviation (MAD) anomaly detection
   * @param {Array<Object>} timeSeries - Time series data
   * @param {number} threshold - MAD threshold
   * @returns {Array<Object>} Detected anomalies
   */
  detectMADAnomalies(timeSeries, threshold = 3.0) {
    const values = timeSeries.map(point => point.value);
    const median = this.calculateMedian(values);
    const deviations = values.map(val => Math.abs(val - median));
    const mad = this.calculateMedian(deviations);

    if (mad === 0) return [];

    const anomalies = [];

    timeSeries.forEach((point, index) => {
      const madScore = Math.abs(point.value - median) / mad;

      if (madScore > threshold) {
        anomalies.push({
          timestamp: point.timestamp,
          value: point.value,
          score: madScore,
          severity: this.calculateSeverity(madScore, threshold),
          type: ANOMALY_TYPES.POINT,
          confidence: Math.min(madScore / threshold, 1.0),
          context: {
            median,
            mad,
            threshold,
            index,
          },
        });
      }
    });

    return anomalies;
  }

  /**
   * Interquartile Range (IQR) anomaly detection
   * @param {Array<Object>} timeSeries - Time series data
   * @returns {Array<Object>} Detected anomalies
   */
  detectIQRAnomalies(timeSeries) {
    const values = timeSeries.map(point => point.value).sort((a, b) => a - b);
    const q1Index = Math.floor(values.length * 0.25);
    const q3Index = Math.floor(values.length * 0.75);
    const q1 = values[q1Index];
    const q3 = values[q3Index];
    const iqr = q3 - q1;
    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;

    const anomalies = [];

    timeSeries.forEach((point, index) => {
      if (point.value < lowerBound || point.value > upperBound) {
        const distance = Math.min(
          Math.abs(point.value - lowerBound),
          Math.abs(point.value - upperBound),
        );

        anomalies.push({
          timestamp: point.timestamp,
          value: point.value,
          score: distance / iqr,
          severity:
            point.value < lowerBound || point.value > upperBound
              ? SEVERITY_LEVELS.MEDIUM
              : SEVERITY_LEVELS.LOW,
          type: ANOMALY_TYPES.POINT,
          confidence: Math.min(distance / (iqr * 2), 1.0),
          context: {
            q1,
            q3,
            iqr,
            lowerBound,
            upperBound,
            index,
          },
        });
      }
    });

    return anomalies;
  }

  /**
   * Seasonal anomaly detection
   * @param {Array<Object>} timeSeries - Time series data
   * @returns {Array<Object>} Detected anomalies
   */
  detectSeasonalAnomalies(timeSeries) {
    if (timeSeries.length < this.options.seasonalityPeriod * 2) {
      return [];
    }

    const period = this.options.seasonalityPeriod;
    const anomalies = [];

    // Simple seasonal decomposition
    for (let i = period; i < timeSeries.length; i++) {
      const currentValue = timeSeries[i].value;
      const seasonalValues = [];

      // Collect values from same seasonal position
      for (let j = i % period; j < i; j += period) {
        seasonalValues.push(timeSeries[j].value);
      }

      if (seasonalValues.length > 0) {
        const seasonalMean =
          seasonalValues.reduce((sum, val) => sum + val, 0) / seasonalValues.length;
        const seasonalStd = Math.sqrt(
          seasonalValues.reduce((sum, val) => sum + Math.pow(val - seasonalMean, 2), 0) /
            seasonalValues.length,
        );

        if (seasonalStd > 0) {
          const seasonalScore = Math.abs(currentValue - seasonalMean) / seasonalStd;

          if (seasonalScore > 2.0) {
            anomalies.push({
              timestamp: timeSeries[i].timestamp,
              value: currentValue,
              score: seasonalScore,
              severity: this.calculateSeverity(seasonalScore, 2.0),
              type: ANOMALY_TYPES.SEASONAL,
              confidence: Math.min(seasonalScore / 3.0, 1.0),
              context: {
                seasonalMean,
                seasonalStd,
                seasonalPosition: i % period,
                index: i,
              },
            });
          }
        }
      }
    }

    return anomalies;
  }

  /**
   * Change point detection
   * @param {Array<Object>} timeSeries - Time series data
   * @returns {Array<Object>} Detected anomalies
   */
  detectChangePointAnomalies(timeSeries) {
    if (timeSeries.length < 20) return [];

    const windowSize = Math.min(10, Math.floor(timeSeries.length / 4));
    const anomalies = [];

    for (let i = windowSize; i < timeSeries.length - windowSize; i++) {
      const beforeWindow = timeSeries.slice(i - windowSize, i).map(p => p.value);
      const afterWindow = timeSeries.slice(i, i + windowSize).map(p => p.value);

      const beforeMean = beforeWindow.reduce((sum, val) => sum + val, 0) / beforeWindow.length;
      const afterMean = afterWindow.reduce((sum, val) => sum + val, 0) / afterWindow.length;

      const beforeStd = Math.sqrt(
        beforeWindow.reduce((sum, val) => sum + Math.pow(val - beforeMean, 2), 0) /
          beforeWindow.length,
      );
      const afterStd = Math.sqrt(
        afterWindow.reduce((sum, val) => sum + Math.pow(val - afterMean, 2), 0) /
          afterWindow.length,
      );

      const pooledStd = Math.sqrt((beforeStd * beforeStd + afterStd * afterStd) / 2);

      if (pooledStd > 0) {
        const changeScore = Math.abs(afterMean - beforeMean) / pooledStd;

        if (changeScore > 2.0) {
          anomalies.push({
            timestamp: timeSeries[i].timestamp,
            value: timeSeries[i].value,
            score: changeScore,
            severity: this.calculateSeverity(changeScore, 2.0),
            type: ANOMALY_TYPES.TREND,
            confidence: Math.min(changeScore / 4.0, 1.0),
            context: {
              beforeMean,
              afterMean,
              changeScore,
              windowSize,
              index: i,
            },
          });
        }
      }
    }

    return anomalies;
  }

  /**
   * Placeholder for Isolation Forest algorithm
   * @param {Array<Object>} _timeSeries - Time series data (unused in placeholder)
   * @returns {Array<Object>} Detected anomalies
   */
  detectIsolationForestAnomalies(_timeSeries) {
    // Placeholder implementation - would require ML library
    logger.debug('Isolation Forest algorithm not implemented yet');
    return [];
  }

  /**
   * Calculate median of array
   * @param {Array<number>} values - Values array
   * @returns {number} Median value
   */
  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
  }

  /**
   * Calculate severity based on score and threshold
   * @param {number} score - Anomaly score
   * @param {number} threshold - Base threshold
   * @returns {string} Severity level
   */
  calculateSeverity(score, threshold) {
    if (score > threshold * 3) return SEVERITY_LEVELS.CRITICAL;
    if (score > threshold * 2) return SEVERITY_LEVELS.HIGH;
    if (score > threshold * 1.5) return SEVERITY_LEVELS.MEDIUM;
    return SEVERITY_LEVELS.LOW;
  }

  /**
   * Generate unique anomaly ID
   * @param {Object} anomaly - Anomaly object
   * @param {string} algorithm - Algorithm name
   * @param {string} metric - Metric name
   * @returns {string} Unique ID
   */
  generateAnomalyId(anomaly, algorithm, metric) {
    const timestamp = new Date(anomaly.timestamp).getTime();
    return `${metric}-${algorithm}-${timestamp}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Deduplicate anomalies from multiple algorithms
   * @param {Array<Object>} anomalies - All detected anomalies
   * @returns {Array<Object>} Deduplicated anomalies
   */
  deduplicateAnomalies(anomalies) {
    const timeWindow = 60000; // 1 minute window
    const grouped = new Map();

    anomalies.forEach(anomaly => {
      const timestamp = new Date(anomaly.timestamp).getTime();
      const windowKey = Math.floor(timestamp / timeWindow);

      if (!grouped.has(windowKey)) {
        grouped.set(windowKey, []);
      }
      grouped.get(windowKey).push(anomaly);
    });

    const deduplicated = [];

    grouped.forEach(group => {
      if (group.length === 1) {
        deduplicated.push(group[0]);
      } else {
        // Merge anomalies in same time window
        const merged = this.mergeAnomalies(group);
        deduplicated.push(merged);
      }
    });

    return deduplicated;
  }

  /**
   * Merge multiple anomalies into one
   * @param {Array<Object>} anomalies - Anomalies to merge
   * @returns {Object} Merged anomaly
   */
  mergeAnomalies(anomalies) {
    const maxScoreAnomaly = anomalies.reduce((max, current) =>
      current.score > max.score ? current : max,
    );

    return {
      ...maxScoreAnomaly,
      algorithms: anomalies.map(a => a.algorithm),
      confidence: Math.max(...anomalies.map(a => a.confidence)),
      detectionCount: anomalies.length,
    };
  }

  /**
   * Rank anomalies by importance
   * @param {Array<Object>} anomalies - Anomalies to rank
   * @returns {Array<Object>} Ranked anomalies
   */
  rankAnomalies(anomalies) {
    return anomalies.sort((a, b) => {
      // Sort by severity first, then by confidence, then by score
      const severityOrder = {
        [SEVERITY_LEVELS.CRITICAL]: 4,
        [SEVERITY_LEVELS.HIGH]: 3,
        [SEVERITY_LEVELS.MEDIUM]: 2,
        [SEVERITY_LEVELS.LOW]: 1,
      };

      if (severityOrder[a.severity] !== severityOrder[b.severity]) {
        return severityOrder[b.severity] - severityOrder[a.severity];
      }

      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence;
      }

      return b.score - a.score;
    });
  }

  /**
   * Store anomalies
   * @param {Array<Object>} anomalies - Anomalies to store
   * @param {string} metric - Metric name
   */
  storeAnomalies(anomalies, metric) {
    const key = `${metric}-${Date.now()}`;
    this.detectedAnomalies.set(key, anomalies);
    this.anomalyHistory.push(...anomalies);

    // Keep only recent history (last 1000 anomalies)
    if (this.anomalyHistory.length > 1000) {
      this.anomalyHistory = this.anomalyHistory.slice(-1000);
    }
  }

  /**
   * Update statistics
   * @param {Array<Object>} anomalies - Detected anomalies
   */
  updateStatistics(anomalies) {
    this.stats.totalDetections += anomalies.length;
    this.stats.lastDetection =
      anomalies.length > 0 ? new Date().toISOString() : this.stats.lastDetection;

    anomalies.forEach(anomaly => {
      this.stats.detectionsBySeverity[anomaly.severity]++;
      this.stats.detectionsByType[anomaly.type]++;
    });
  }

  /**
   * Emit anomaly events for real-time processing
   * @param {Array<Object>} anomalies - Detected anomalies
   */
  emitAnomalyEvents(anomalies) {
    anomalies.forEach(anomaly => {
      this.emit('anomalyDetected', anomaly);

      if (anomaly.severity === SEVERITY_LEVELS.CRITICAL) {
        this.emit('criticalAnomaly', anomaly);
      }
    });
  }

  /**
   * Generate visualization for detected anomalies
   * @param {Array<Object>} timeSeries - Original time series data
   * @param {Array<Object>} anomalies - Detected anomalies
   * @param {string} metric - Metric name
   */
  async generateVisualization(timeSeries, anomalies, metric) {
    try {
      const visualization = {
        metric,
        timestamp: new Date().toISOString(),
        timeSeries: timeSeries.map(point => ({
          timestamp: point.timestamp,
          value: point.value,
        })),
        anomalies: anomalies.map(anomaly => ({
          timestamp: anomaly.timestamp,
          value: anomaly.value,
          severity: anomaly.severity,
          type: anomaly.type,
          score: anomaly.score,
          confidence: anomaly.confidence,
        })),
        statistics: {
          totalPoints: timeSeries.length,
          anomalyCount: anomalies.length,
          anomalyRate: (anomalies.length / timeSeries.length) * 100,
        },
      };

      const fileName = `anomaly-visualization-${metric}-${Date.now()}.json`;
      const filePath = path.join(this.options.outputPath, fileName);

      await writeFileAsync(filePath, JSON.stringify(visualization, null, 2));

      logger.info(`Anomaly visualization saved: ${filePath}`);
    } catch (error) {
      logger.error('Failed to generate anomaly visualization:', error);
    }
  }

  /**
   * Get anomaly detection statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      anomalyHistorySize: this.anomalyHistory.length,
      detectedAnomaliesSize: this.detectedAnomalies.size,
    };
  }

  /**
   * Get recent anomalies
   * @param {number} limit - Maximum number of anomalies to return
   * @returns {Array<Object>} Recent anomalies
   */
  getRecentAnomalies(limit = 100) {
    return this.anomalyHistory.slice(-limit);
  }

  /**
   * Clear anomaly history
   */
  clearHistory() {
    this.anomalyHistory = [];
    this.detectedAnomalies.clear();
    logger.info('Anomaly history cleared');
  }
}

export { EnhancedAnomalyDetectionService, ALGORITHMS, SEVERITY_LEVELS, ANOMALY_TYPES };
