NODE_ENV=test
VITEST_SEGFAULT_RETRY=3
VITEST_MAX_THREADS=4
VITEST_MIN_THREADS=1
VITEST_POOL_OPTIONS={"threads":{"singleThread":true}}

# Test Environment Selection (local|staging|production)
TEST_ENV=local

# Local Test Configuration
LOCAL_SERVER_URL=http://localhost:3000
LOCAL_SUPABASE_URL=http://localhost:54321
LOCAL_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTl9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
LOCAL_SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5OX0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
LOCAL_REDIS_URL=redis://localhost:6379

# Staging Test Configuration
STAGING_SERVER_URL=https://mvs.kanousai.com
STAGING_SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
STAGING_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.staging-anon-key
STAGING_SUPABASE_SERVICE_ROLE_KEY=staging-service-role-key
STAGING_REDIS_URL=redis://staging-redis:6379

# Coverage settings
COVERAGE_PROVIDER=v8
COVERAGE_DIRECTORY=coverage

# Test timeouts
TEST_TIMEOUT=5000
TEST_SETUP_TIMEOUT=10000

# Debug settings
DEBUG_PORT=9229
DEBUG_BREAK_ON_FAILURE=false
DEBUG_PRINT_LIMIT=10000
