/**
 * Endpoint Security Suite
 * 
 * This module integrates all endpoint security middleware components
 * to provide comprehensive protection against information disclosure.
 */

const responseSanitization = require('./response-sanitization');
const { apiResponseAuditor } = require('./api-response-auditor');
const { enhancedDataMasking } = require('./enhanced-data-masking');
const { sensitiveDataAccessControl } = require('./sensitive-data-access-control');
const { comprehensiveAuditLogger } = require('./comprehensive-audit-logger');

const logger = require('../utils/logger').getLogger('endpoint-security-suite');

// Security suite configuration
const SECURITY_SUITE_CONFIG = {
  enabled: process.env.ENDPOINT_SECURITY_ENABLED !== 'false',
  mode: process.env.ENDPOINT_SECURITY_MODE || 'production', // 'development', 'staging', 'production'
  strictMode: process.env.ENDPOINT_SECURITY_STRICT === 'true',
  logLevel: process.env.ENDPOINT_SECURITY_LOG_LEVEL || 'info'
};

// Security levels
const SECURITY_LEVELS = {
  MINIMAL: 'minimal',     // Basic sanitization only
  STANDARD: 'standard',   // Sanitization + basic access control
  ENHANCED: 'enhanced',   // Standard + enhanced masking + auditing
  MAXIMUM: 'maximum'      // All security features enabled
};

/**
 * Get security configuration based on environment and level
 * @param {string} level - Security level
 * @returns {Object} Security configuration
 */
function getSecurityConfig(level = SECURITY_LEVELS.STANDARD) {
  const baseConfig = {
    responseSanitization: {
      enabled: true,
      logSanitization: SECURITY_SUITE_CONFIG.mode === 'development',
      allowBypass: SECURITY_SUITE_CONFIG.mode !== 'production'
    },
    accessControl: {
      enabled: true,
      logAccess: true,
      strictMode: SECURITY_SUITE_CONFIG.strictMode
    },
    auditLogging: {
      enabled: true,
      logLevel: 'sensitive_only'
    },
    enhancedMasking: {
      enabled: false,
      maskContent: true
    },
    responseAuditing: {
      enabled: false,
      auditLevel: 'SENSITIVE_ONLY'
    }
  };

  switch (level) {
    case SECURITY_LEVELS.MINIMAL:
      return {
        ...baseConfig,
        accessControl: { ...baseConfig.accessControl, enabled: false },
        auditLogging: { ...baseConfig.auditLogging, enabled: false }
      };

    case SECURITY_LEVELS.STANDARD:
      return baseConfig;

    case SECURITY_LEVELS.ENHANCED:
      return {
        ...baseConfig,
        enhancedMasking: { ...baseConfig.enhancedMasking, enabled: true },
        responseAuditing: { ...baseConfig.responseAuditing, enabled: true },
        auditLogging: { ...baseConfig.auditLogging, logLevel: 'all' }
      };

    case SECURITY_LEVELS.MAXIMUM:
      return {
        ...baseConfig,
        enhancedMasking: { ...baseConfig.enhancedMasking, enabled: true },
        responseAuditing: { 
          ...baseConfig.responseAuditing, 
          enabled: true, 
          auditLevel: 'ALL' 
        },
        auditLogging: { 
          ...baseConfig.auditLogging, 
          logLevel: 'all',
          includeRequestBody: true,
          includeResponseBody: true
        },
        accessControl: { 
          ...baseConfig.accessControl, 
          strictMode: true 
        },
        responseSanitization: {
          ...baseConfig.responseSanitization,
          allowBypass: false
        }
      };

    default:
      return baseConfig;
  }
}

/**
 * Create endpoint security middleware suite
 * @param {Object} options - Configuration options
 * @returns {Array} Array of middleware functions
 */
function createEndpointSecuritySuite(options = {}) {
  const {
    securityLevel = SECURITY_LEVELS.STANDARD,
    customConfig = {},
    skipPaths = ['/health', '/metrics', '/favicon.ico'],
    skipMethods = ['OPTIONS']
  } = options;

  if (!SECURITY_SUITE_CONFIG.enabled) {
    logger.warn('Endpoint security suite is disabled');
    return [];
  }

  const config = {
    ...getSecurityConfig(securityLevel),
    ...customConfig
  };

  const middlewares = [];

  logger.info(`Initializing endpoint security suite with level: ${securityLevel}`);

  // 1. Comprehensive Audit Logger (first to capture all requests)
  if (config.auditLogging.enabled) {
    middlewares.push(comprehensiveAuditLogger({
      ...config.auditLogging,
      skipPaths,
      skipMethods
    }));
    logger.info('✓ Comprehensive audit logging enabled');
  }

  // 2. Sensitive Data Access Control (before data processing)
  if (config.accessControl.enabled) {
    middlewares.push(sensitiveDataAccessControl({
      ...config.accessControl,
      skipPaths,
      skipMethods
    }));
    logger.info('✓ Sensitive data access control enabled');
  }

  // 3. Enhanced Data Masking (before sanitization)
  if (config.enhancedMasking.enabled) {
    middlewares.push(enhancedDataMasking({
      ...config.enhancedMasking,
      skipPaths,
      skipMethods
    }));
    logger.info('✓ Enhanced data masking enabled');
  }

  // 4. Response Sanitization (core protection)
  if (config.responseSanitization.enabled) {
    middlewares.push(responseSanitization({
      ...config.responseSanitization,
      skipPaths,
      skipMethods
    }));
    logger.info('✓ Response sanitization enabled');
  }

  // 5. API Response Auditor (last to audit final responses)
  if (config.responseAuditing.enabled) {
    middlewares.push(apiResponseAuditor({
      ...config.responseAuditing,
      skipPaths,
      skipMethods
    }));
    logger.info('✓ API response auditing enabled');
  }

  logger.info(`Endpoint security suite initialized with ${middlewares.length} middleware components`);

  return middlewares;
}

/**
 * Create security middleware for specific endpoint types
 * @param {string} endpointType - Type of endpoint
 * @returns {Array} Array of middleware functions
 */
function createEndpointTypeSecuritySuite(endpointType) {
  const endpointConfigs = {
    // Public API endpoints
    public: {
      securityLevel: SECURITY_LEVELS.MINIMAL,
      customConfig: {
        responseSanitization: { allowBypass: false },
        accessControl: { enabled: false }
      }
    },

    // User API endpoints
    user: {
      securityLevel: SECURITY_LEVELS.STANDARD,
      customConfig: {
        accessControl: { ownershipRequired: true }
      }
    },

    // Admin API endpoints
    admin: {
      securityLevel: SECURITY_LEVELS.ENHANCED,
      customConfig: {
        accessControl: { 
          strictMode: true,
          requiredRole: 'admin'
        },
        auditLogging: { 
          logLevel: 'all',
          includeRequestBody: true 
        }
      }
    },

    // System API endpoints
    system: {
      securityLevel: SECURITY_LEVELS.MAXIMUM,
      customConfig: {
        accessControl: { 
          strictMode: true,
          requiredRole: 'super_admin'
        },
        responseSanitization: { allowBypass: false }
      }
    },

    // Payment/Financial endpoints
    financial: {
      securityLevel: SECURITY_LEVELS.MAXIMUM,
      customConfig: {
        enhancedMasking: { 
          enabled: true,
          maskContent: true,
          preserveStructure: false
        },
        auditLogging: {
          logLevel: 'all',
          includeRequestBody: true,
          includeResponseBody: false
        }
      }
    }
  };

  const config = endpointConfigs[endpointType];
  if (!config) {
    logger.warn(`Unknown endpoint type: ${endpointType}, using standard configuration`);
    return createEndpointSecuritySuite();
  }

  return createEndpointSecuritySuite(config);
}

/**
 * Get security suite statistics
 * @returns {Object} Security suite statistics
 */
function getSecuritySuiteStats() {
  const stats = {
    enabled: SECURITY_SUITE_CONFIG.enabled,
    mode: SECURITY_SUITE_CONFIG.mode,
    strictMode: SECURITY_SUITE_CONFIG.strictMode,
    components: {}
  };

  try {
    // Get stats from individual components
    const { getAuditStats } = require('./api-response-auditor');
    const { getMaskingStats } = require('./enhanced-data-masking');
    const { getAccessControlStats } = require('./sensitive-data-access-control');
    const { getAuditStats: getComprehensiveAuditStats } = require('./comprehensive-audit-logger');

    stats.components.responseAuditing = getAuditStats();
    stats.components.enhancedMasking = getMaskingStats();
    stats.components.accessControl = getAccessControlStats();
    stats.components.comprehensiveAudit = getComprehensiveAuditStats();

  } catch (error) {
    logger.error('Error getting security suite stats:', error);
    stats.error = error.message;
  }

  return stats;
}

/**
 * Health check for security suite
 * @returns {Object} Health status
 */
function getSecuritySuiteHealth() {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    components: {},
    issues: []
  };

  try {
    // Check if all components are functioning
    const stats = getSecuritySuiteStats();
    
    if (!stats.enabled) {
      health.status = 'disabled';
      health.issues.push('Security suite is disabled');
    }

    // Check individual component health
    for (const [component, componentStats] of Object.entries(stats.components)) {
      if (componentStats.error) {
        health.components[component] = 'error';
        health.issues.push(`${component}: ${componentStats.error}`);
      } else {
        health.components[component] = 'healthy';
      }
    }

    if (health.issues.length > 0) {
      health.status = 'degraded';
    }

  } catch (error) {
    health.status = 'error';
    health.error = error.message;
  }

  return health;
}

/**
 * Express middleware to add security suite to app
 * @param {Object} app - Express app
 * @param {Object} options - Configuration options
 */
function applyEndpointSecuritySuite(app, options = {}) {
  const middlewares = createEndpointSecuritySuite(options);
  
  middlewares.forEach(middleware => {
    app.use(middleware);
  });

  // Add health check endpoint
  app.get('/api/security/health', (req, res) => {
    res.json(getSecuritySuiteHealth());
  });

  // Add stats endpoint (admin only)
  app.get('/api/security/stats', (req, res) => {
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Admin access required' });
    }
    res.json(getSecuritySuiteStats());
  });

  logger.info('Endpoint security suite applied to Express app');
}

module.exports = {
  createEndpointSecuritySuite,
  createEndpointTypeSecuritySuite,
  applyEndpointSecuritySuite,
  getSecuritySuiteStats,
  getSecuritySuiteHealth,
  SECURITY_LEVELS,
  SECURITY_SUITE_CONFIG
};
