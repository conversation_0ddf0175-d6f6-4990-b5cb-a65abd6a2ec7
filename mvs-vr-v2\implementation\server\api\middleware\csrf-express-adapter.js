/**
 * CSRF Express Adapter
 *
 * This module provides an adapter for using the CSRF protection middleware with Express.
 */

import { generateCsrfToken, validateCsrfToken } from './csrf-protection.js';

/**
 * Creates a CSRF protection middleware for Express
 *
 * @param {Object} options - Configuration options
 * @param {Array<string>} [options.ignorePaths=[]] - Paths to ignore CSRF protection
 * @param {Array<string>} [options.ignoreMethods=['GET', 'HEAD', 'OPTIONS']] - HTTP methods to ignore CSRF protection
 * @returns {Function} Express middleware function
 */
function csrfProtectionExpress(options = {}) {
  const ignorePaths = options.ignorePaths || [];
  const ignoreMethods = options.ignoreMethods || ['GET', 'HEAD', 'OPTIONS'];

  return async (req, res, next) => {
    // Skip CSRF check for non-mutating methods
    if (ignoreMethods.includes(req.method)) {
      return next();
    }

    // Skip CSRF check for ignored paths
    if (ignorePaths.some(path => req.path.startsWith(path))) {
      return next();
    }

    // Get token from request
    const token = req.headers['x-csrf-token'] || req.body?._csrf;

    if (!token) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'CSRF_TOKEN_MISSING',
          message: 'CSRF token is missing',
        },
      });
    }

    // Validate token
    const isValid = await validateCsrfToken(req, res, token);

    if (!isValid) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'CSRF_TOKEN_INVALID',
          message: 'CSRF token is invalid or expired',
        },
      });
    }

    next();
  };
}

/**
 * Creates a middleware that generates and adds a CSRF token to the response locals
 *
 * @returns {Function} Express middleware function
 */
function csrfTokenGeneratorExpress() {
  return (_req, res, next) => {
    // Generate token and add to response locals
    const token = generateCsrfToken();
    res.locals.csrfToken = token;
    next();
  };
}

export { csrfProtectionExpress as csrfProtection, csrfTokenGeneratorExpress as csrfTokenGenerator };
