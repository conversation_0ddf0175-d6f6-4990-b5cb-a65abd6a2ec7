/**
 * Database Recovery Script
 * 
 * This script handles recovery of database from backups.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync } = require('child_process');
const { Pool } = require('pg');

const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);

const logger = require('../../utils/logger').getLogger('database-recovery');

// Configuration
const config = {
  backupPath: process.env.DB_BACKUP_PATH || '/backups/database',
  tempPath: path.join(__dirname, '../../temp/database-recovery'),
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'mvs_vr'
  }
};

/**
 * Recover database from backup
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverDatabase(options = {}) {
  const {
    source = null,
    target = config.database.database,
    test = false,
    dropExisting = true
  } = options;
  
  logger.info('Starting database recovery');
  
  try {
    // Ensure temp directory exists
    if (!await existsAsync(config.tempPath)) {
      await mkdirAsync(config.tempPath, { recursive: true });
    }
    
    let backupSource = source;
    
    // If no source specified, find latest backup
    if (!backupSource) {
      backupSource = await findLatestBackup();
    }
    
    if (!backupSource) {
      throw new Error('No database backup found');
    }
    
    logger.info(`Recovering database from: ${backupSource}`);
    
    // Extract backup if it's compressed
    let backupFile = backupSource;
    if (backupSource.endsWith('.gz')) {
      backupFile = await extractBackup(backupSource);
    }
    
    if (test) {
      logger.info('[TEST] Would perform database recovery');
      return {
        success: true,
        test: true,
        source: backupSource,
        target
      };
    }
    
    // Perform database recovery
    const result = await performDatabaseRestore(backupFile, target, dropExisting);
    
    // Validate recovered database
    const validation = await validateDatabase(target);
    
    logger.info('Database recovery completed successfully');
    
    return {
      success: true,
      source: backupSource,
      target,
      validation,
      ...result
    };
    
  } catch (error) {
    logger.error(`Database recovery failed: ${error.message}`, { error });
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Find latest database backup
 * @returns {Promise<string|null>} Latest backup path
 */
async function findLatestBackup() {
  try {
    if (!await existsAsync(config.backupPath)) {
      return null;
    }
    
    // List backup files
    const files = await fs.promises.readdir(config.backupPath);
    const dbBackups = files
      .filter(file => file.startsWith('db-') && (file.endsWith('.sql') || file.endsWith('.sql.gz')))
      .map(file => ({
        name: file,
        path: path.join(config.backupPath, file),
        mtime: fs.statSync(path.join(config.backupPath, file)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);
    
    return dbBackups.length > 0 ? dbBackups[0].path : null;
  } catch (error) {
    logger.error(`Failed to find latest backup: ${error.message}`);
    return null;
  }
}

/**
 * Extract compressed backup
 * @param {string} backupPath - Compressed backup file path
 * @returns {Promise<string>} Extracted file path
 */
async function extractBackup(backupPath) {
  const extractedFile = path.join(config.tempPath, `extracted-${Date.now()}.sql`);
  
  try {
    execSync(`gunzip -c "${backupPath}" > "${extractedFile}"`, { stdio: 'inherit' });
    logger.info(`Extracted backup to: ${extractedFile}`);
    return extractedFile;
  } catch (error) {
    logger.error(`Failed to extract backup: ${error.message}`);
    throw error;
  }
}

/**
 * Perform database restore
 * @param {string} backupFile - Backup file path
 * @param {string} targetDatabase - Target database name
 * @param {boolean} dropExisting - Whether to drop existing database
 * @returns {Promise<Object>} Restore result
 */
async function performDatabaseRestore(backupFile, targetDatabase, dropExisting = true) {
  const startTime = Date.now();
  
  try {
    // Connect to PostgreSQL (to postgres database for admin operations)
    const adminPool = new Pool({
      ...config.database,
      database: 'postgres' // Connect to postgres database for admin operations
    });
    
    try {
      if (dropExisting) {
        // Terminate existing connections to target database
        await adminPool.query(`
          SELECT pg_terminate_backend(pid)
          FROM pg_stat_activity
          WHERE datname = $1 AND pid <> pg_backend_pid()
        `, [targetDatabase]);
        
        // Drop existing database
        await adminPool.query(`DROP DATABASE IF EXISTS "${targetDatabase}"`);
        logger.info(`Dropped existing database: ${targetDatabase}`);
      }
      
      // Create new database
      await adminPool.query(`CREATE DATABASE "${targetDatabase}"`);
      logger.info(`Created database: ${targetDatabase}`);
      
    } finally {
      await adminPool.end();
    }
    
    // Restore database from backup
    const restoreCommand = `psql -h ${config.database.host} -p ${config.database.port} -U ${config.database.user} -d ${targetDatabase} -f "${backupFile}"`;
    
    // Set PGPASSWORD environment variable for authentication
    const env = { ...process.env };
    if (config.database.password) {
      env.PGPASSWORD = config.database.password;
    }
    
    execSync(restoreCommand, { 
      stdio: 'inherit',
      env
    });
    
    const duration = Date.now() - startTime;
    logger.info(`Database restore completed in ${duration}ms`);
    
    return {
      duration,
      backupFile,
      targetDatabase
    };
    
  } catch (error) {
    logger.error(`Database restore failed: ${error.message}`);
    throw error;
  }
}

/**
 * Validate recovered database
 * @param {string} databaseName - Database name to validate
 * @returns {Promise<Object>} Validation result
 */
async function validateDatabase(databaseName) {
  const validation = {
    valid: true,
    errors: [],
    warnings: [],
    stats: {}
  };
  
  try {
    const pool = new Pool({
      ...config.database,
      database: databaseName
    });
    
    try {
      // Test connection
      await pool.query('SELECT 1');
      
      // Get database statistics
      const statsQueries = [
        {
          name: 'table_count',
          query: `SELECT count(*) as count FROM information_schema.tables WHERE table_schema = 'public'`
        },
        {
          name: 'row_counts',
          query: `
            SELECT schemaname, tablename, n_tup_ins as inserts, n_tup_upd as updates, n_tup_del as deletes
            FROM pg_stat_user_tables
            ORDER BY schemaname, tablename
          `
        },
        {
          name: 'database_size',
          query: `SELECT pg_size_pretty(pg_database_size(current_database())) as size`
        }
      ];
      
      for (const { name, query } of statsQueries) {
        try {
          const result = await pool.query(query);
          validation.stats[name] = result.rows;
        } catch (error) {
          validation.warnings.push(`Failed to get ${name}: ${error.message}`);
        }
      }
      
      // Check for essential tables
      const essentialTables = ['users', 'products', 'orders']; // Add your essential tables
      const tableCountResult = await pool.query(`
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = ANY($1)
      `, [essentialTables]);
      
      const existingTables = tableCountResult.rows.map(row => row.table_name);
      const missingTables = essentialTables.filter(table => !existingTables.includes(table));
      
      if (missingTables.length > 0) {
        validation.warnings.push(`Missing essential tables: ${missingTables.join(', ')}`);
      }
      
    } finally {
      await pool.end();
    }
    
  } catch (error) {
    validation.valid = false;
    validation.errors.push(`Database validation failed: ${error.message}`);
  }
  
  return validation;
}

/**
 * Test database connection
 * @param {string} databaseName - Database name
 * @returns {Promise<boolean>} Connection status
 */
async function testConnection(databaseName = config.database.database) {
  try {
    const pool = new Pool({
      ...config.database,
      database: databaseName
    });
    
    try {
      await pool.query('SELECT 1');
      return true;
    } finally {
      await pool.end();
    }
  } catch (error) {
    logger.error(`Database connection test failed: ${error.message}`);
    return false;
  }
}

// If script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    source: args.find(arg => arg.startsWith('--source='))?.split('=')[1],
    target: args.find(arg => arg.startsWith('--target='))?.split('=')[1],
    dropExisting: !args.includes('--no-drop')
  };
  
  recoverDatabase(options)
    .then(result => {
      if (result.success) {
        console.log('Database recovery completed successfully');
        if (result.validation) {
          console.log(`Tables found: ${result.validation.stats.table_count?.[0]?.count || 'unknown'}`);
        }
        process.exit(0);
      } else {
        console.error('Database recovery failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  recoverDatabase,
  findLatestBackup,
  validateDatabase,
  testConnection
};
