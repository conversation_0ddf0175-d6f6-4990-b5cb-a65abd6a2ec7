{"services": [{"id": "database", "name": "Primary Database", "type": "infrastructure", "businessCriticality": 5, "rto": 60, "rpo": 15, "dependencies": ["storage"], "businessImpact": {"revenue": 5, "customer": 5, "operational": 5, "compliance": 5, "reputation": 4}, "recoveryProcedures": [{"step": 1, "action": "Assess database cluster status", "estimatedTime": 5, "responsible": "Database Administrator", "tools": ["database-monitoring", "cluster-tools"]}, {"step": 2, "action": "Verify storage connectivity and integrity", "estimatedTime": 10, "responsible": "Storage Administrator", "tools": ["storage-tools", "network-tools"]}, {"step": 3, "action": "Run database integrity checks", "estimatedTime": 15, "responsible": "Database Administrator", "tools": ["database-tools", "integrity-checkers"]}, {"step": 4, "action": "Restore from backup if corruption detected", "estimatedTime": 30, "responsible": "Database Administrator", "tools": ["backup-tools", "restore-tools"]}, {"step": 5, "action": "Validate data consistency and application connectivity", "estimatedTime": 10, "responsible": "Database Administrator", "tools": ["validation-tools", "monitoring"]}], "resources": {"personnel": 3, "infrastructure": "enterprise", "budget": "critical", "expertise": "expert", "tools": ["database-tools", "backup-tools", "monitoring", "clustering"]}}, {"id": "storage", "name": "Storage Service", "type": "infrastructure", "businessCriticality": 4, "rto": 120, "rpo": 30, "dependencies": [], "businessImpact": {"revenue": 4, "customer": 4, "operational": 5, "compliance": 3, "reputation": 3}, "recoveryProcedures": [{"step": 1, "action": "Check storage cluster health and node status", "estimatedTime": 10, "responsible": "Storage Administrator", "tools": ["storage-monitoring", "cluster-tools"]}, {"step": 2, "action": "Verify network connectivity to storage nodes", "estimatedTime": 5, "responsible": "Network Administrator", "tools": ["network-tools", "ping", "traceroute"]}, {"step": 3, "action": "Run storage integrity and consistency checks", "estimatedTime": 30, "responsible": "Storage Administrator", "tools": ["fsck", "storage-validation", "integrity-tools"]}, {"step": 4, "action": "Restore from backup storage if needed", "estimatedTime": 60, "responsible": "Storage Administrator", "tools": ["backup-tools", "replication-tools"]}, {"step": 5, "action": "Validate file accessibility and performance", "estimatedTime": 15, "responsible": "Storage Administrator", "tools": ["performance-tools", "access-validation"]}], "resources": {"personnel": 2, "infrastructure": "distributed", "budget": "high", "expertise": "senior", "tools": ["storage-tools", "backup-tools", "monitoring", "replication"]}}, {"id": "authentication", "name": "Authentication Service", "type": "core", "businessCriticality": 5, "rto": 30, "rpo": 10, "dependencies": ["database", "user-directory"], "businessImpact": {"revenue": 5, "customer": 5, "operational": 4, "compliance": 5, "reputation": 4}, "recoveryProcedures": [{"step": 1, "action": "Check user directory connectivity and status", "estimatedTime": 5, "responsible": "Identity Administrator", "tools": ["ldap-tools", "directory-monitoring"]}, {"step": 2, "action": "Verify database access and authentication data", "estimatedTime": 5, "responsible": "Database Administrator", "tools": ["database-tools", "query-tools"]}, {"step": 3, "action": "Restart authentication service with health checks", "estimatedTime": 10, "responsible": "Security Team Lead", "tools": ["service-management", "health-checks"]}, {"step": 4, "action": "Clear authentication cache and sessions", "estimatedTime": 5, "responsible": "Security Team Lead", "tools": ["cache-management", "session-tools"]}, {"step": 5, "action": "Test login functionality and token validation", "estimatedTime": 5, "responsible": "Security Team Lead", "tools": ["testing-tools", "validation-scripts"]}], "resources": {"personnel": 2, "infrastructure": "high-availability", "budget": "high", "expertise": "senior", "tools": ["o<PERSON>h", "ldap", "monitoring", "security-tools"]}}, {"id": "api-gateway", "name": "API Gateway", "type": "core", "businessCriticality": 5, "rto": 15, "rpo": 5, "dependencies": ["authentication", "database"], "businessImpact": {"revenue": 5, "customer": 5, "operational": 5, "compliance": 4, "reputation": 5}, "recoveryProcedures": [{"step": 1, "action": "Check load balancer health and routing", "estimatedTime": 3, "responsible": "API Team Lead", "tools": ["load-balancer-tools", "routing-checks"]}, {"step": 2, "action": "Verify database connectivity and response times", "estimatedTime": 2, "responsible": "API Team Lead", "tools": ["database-connectivity", "performance-monitoring"]}, {"step": 3, "action": "Restart API gateway service instances", "estimatedTime": 5, "responsible": "API Team Lead", "tools": ["kubernetes", "service-management"]}, {"step": 4, "action": "Validate authentication service integration", "estimatedTime": 3, "responsible": "API Team Lead", "tools": ["auth-validation", "integration-tests"]}, {"step": 5, "action": "Run comprehensive health checks and API tests", "estimatedTime": 2, "responsible": "API Team Lead", "tools": ["health-checks", "api-testing", "monitoring"]}], "resources": {"personnel": 2, "infrastructure": "high-availability", "budget": "high", "expertise": "senior", "tools": ["kubernetes", "monitoring", "logging", "alerting"]}}, {"id": "vendor-portal", "name": "Vendor Portal", "type": "core", "businessCriticality": 4, "rto": 240, "rpo": 60, "dependencies": ["api-gateway", "authentication", "database"], "businessImpact": {"revenue": 4, "customer": 5, "operational": 4, "compliance": 3, "reputation": 4}, "recoveryProcedures": [{"step": 1, "action": "Check API gateway connectivity and status", "estimatedTime": 10, "responsible": "Vendor Portal Manager", "tools": ["api-monitoring", "connectivity-tests"]}, {"step": 2, "action": "Verify authentication service integration", "estimatedTime": 15, "responsible": "Vendor Portal Manager", "tools": ["auth-testing", "integration-validation"]}, {"step": 3, "action": "Restart vendor portal service and dependencies", "estimatedTime": 30, "responsible": "Vendor Portal Manager", "tools": ["service-management", "dependency-restart"]}, {"step": 4, "action": "Clear application cache and temporary data", "estimatedTime": 15, "responsible": "Vendor Portal Manager", "tools": ["cache-management", "cleanup-tools"]}, {"step": 5, "action": "Test vendor login flow and core functionality", "estimatedTime": 30, "responsible": "Vendor Portal Manager", "tools": ["functional-testing", "user-flow-validation"]}], "resources": {"personnel": 2, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["web-server", "monitoring", "caching"]}}, {"id": "user-directory", "name": "User Directory Service", "type": "supporting", "businessCriticality": 3, "rto": 180, "rpo": 60, "dependencies": [], "businessImpact": {"revenue": 3, "customer": 4, "operational": 4, "compliance": 5, "reputation": 3}, "recoveryProcedures": [{"step": 1, "action": "Check directory server status and replication", "estimatedTime": 20, "responsible": "Identity Administrator", "tools": ["ldap-tools", "replication-monitoring"]}, {"step": 2, "action": "Verify replication health between directory nodes", "estimatedTime": 30, "responsible": "Identity Administrator", "tools": ["replication-tools", "consistency-checks"]}, {"step": 3, "action": "Restart directory service with configuration validation", "estimatedTime": 45, "responsible": "Identity Administrator", "tools": ["service-management", "config-validation"]}, {"step": 4, "action": "Synchronize with backup directory if needed", "estimatedTime": 60, "responsible": "Identity Administrator", "tools": ["sync-tools", "backup-restore"]}, {"step": 5, "action": "Test user lookups and authentication queries", "estimatedTime": 25, "responsible": "Identity Administrator", "tools": ["query-testing", "user-validation"]}], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["ldap-tools", "monitoring", "backup-tools"]}}, {"id": "monitoring", "name": "Monitoring Service", "type": "supporting", "businessCriticality": 3, "rto": 300, "rpo": 60, "dependencies": ["database"], "businessImpact": {"revenue": 2, "customer": 2, "operational": 5, "compliance": 3, "reputation": 2}, "recoveryProcedures": [{"step": 1, "action": "Check monitoring agent status across all nodes", "estimatedTime": 30, "responsible": "Monitoring Engineer", "tools": ["agent-management", "node-monitoring"]}, {"step": 2, "action": "Verify database connectivity for metrics storage", "estimatedTime": 15, "responsible": "Monitoring Engineer", "tools": ["database-connectivity", "metrics-validation"]}, {"step": 3, "action": "Restart monitoring service and data collectors", "estimatedTime": 45, "responsible": "Monitoring Engineer", "tools": ["service-restart", "collector-management"]}, {"step": 4, "action": "Reconfigure alert rules and notification channels", "estimatedTime": 60, "responsible": "Monitoring Engineer", "tools": ["alert-configuration", "notification-setup"]}, {"step": 5, "action": "Test alert delivery and dashboard functionality", "estimatedTime": 30, "responsible": "Monitoring Engineer", "tools": ["alert-testing", "dashboard-validation"]}], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["monitoring-tools", "alerting", "dashboards"]}}, {"id": "admin-portal", "name": "Admin Portal", "type": "supporting", "businessCriticality": 2, "rto": 480, "rpo": 120, "dependencies": ["api-gateway", "authentication", "database"], "businessImpact": {"revenue": 2, "customer": 2, "operational": 5, "compliance": 4, "reputation": 2}, "recoveryProcedures": [{"step": 1, "action": "Check API gateway connectivity and admin endpoints", "estimatedTime": 30, "responsible": "System Administrator", "tools": ["api-testing", "endpoint-validation"]}, {"step": 2, "action": "Verify admin authentication and authorization", "estimatedTime": 45, "responsible": "System Administrator", "tools": ["auth-testing", "permission-validation"]}, {"step": 3, "action": "Restart admin portal service and web server", "estimatedTime": 60, "responsible": "System Administrator", "tools": ["service-management", "web-server-tools"]}, {"step": 4, "action": "Clear admin cache and session data", "estimatedTime": 30, "responsible": "System Administrator", "tools": ["cache-management", "session-cleanup"]}, {"step": 5, "action": "Test admin functionality and system management features", "estimatedTime": 75, "responsible": "System Administrator", "tools": ["functional-testing", "admin-validation"]}], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "low", "expertise": "standard", "tools": ["web-server", "monitoring"]}}, {"id": "backup", "name": "Backup Service", "type": "supporting", "businessCriticality": 3, "rto": 600, "rpo": 240, "dependencies": ["storage"], "businessImpact": {"revenue": 3, "customer": 3, "operational": 4, "compliance": 5, "reputation": 3}, "recoveryProcedures": [{"step": 1, "action": "Check backup job status and recent completion", "estimatedTime": 45, "responsible": "Backup Administrator", "tools": ["backup-monitoring", "job-status-tools"]}, {"step": 2, "action": "Verify storage connectivity and backup repository", "estimatedTime": 30, "responsible": "Backup Administrator", "tools": ["storage-connectivity", "repository-validation"]}, {"step": 3, "action": "Restart backup service and scheduler", "estimatedTime": 60, "responsible": "Backup Administrator", "tools": ["service-restart", "scheduler-management"]}, {"step": 4, "action": "Run backup integrity check on recent backups", "estimatedTime": 120, "responsible": "Backup Administrator", "tools": ["integrity-validation", "backup-verification"]}, {"step": 5, "action": "Test restore functionality with sample data", "estimatedTime": 90, "responsible": "Backup Administrator", "tools": ["restore-testing", "data-validation"]}], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["backup-tools", "scheduling", "monitoring"]}}, {"id": "notification", "name": "Notification Service", "type": "supporting", "businessCriticality": 2, "rto": 720, "rpo": 300, "dependencies": ["external-email", "external-sms"], "businessImpact": {"revenue": 2, "customer": 4, "operational": 3, "compliance": 2, "reputation": 3}, "recoveryProcedures": [{"step": 1, "action": "Check external service connectivity and API status", "estimatedTime": 60, "responsible": "Communications Manager", "tools": ["api-testing", "connectivity-validation"]}, {"step": 2, "action": "Verify notification queue status and message backlog", "estimatedTime": 45, "responsible": "Communications Manager", "tools": ["queue-monitoring", "message-analysis"]}, {"step": 3, "action": "Restart notification service and queue processors", "estimatedTime": 90, "responsible": "Communications Manager", "tools": ["service-restart", "queue-management"]}, {"step": 4, "action": "Clear stuck messages and reset failed deliveries", "estimatedTime": 120, "responsible": "Communications Manager", "tools": ["message-cleanup", "delivery-reset"]}, {"step": 5, "action": "Test notification delivery for all channels", "estimatedTime": 90, "responsible": "Communications Manager", "tools": ["delivery-testing", "channel-validation"]}], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "low", "expertise": "standard", "tools": ["queue-management", "monitoring"]}}]}