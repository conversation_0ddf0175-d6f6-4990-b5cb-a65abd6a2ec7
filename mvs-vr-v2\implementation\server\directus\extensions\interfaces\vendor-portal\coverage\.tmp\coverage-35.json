{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/VisualEditorsMinimal.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27838, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27838, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 695, "endOffset": 1154, "count": 5}], "isBlockCoverage": true}, {"functionName": "setActiveTab", "ranges": [{"startOffset": 1173, "endOffset": 1230, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2746, "endOffset": 5476, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2805, "endOffset": 3195, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3251, "endOffset": 3412, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3473, "endOffset": 4042, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4111, "endOffset": 4193, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4266, "endOffset": 4734, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4823, "endOffset": 5472, "count": 1}], "isBlockCoverage": true}]}]}