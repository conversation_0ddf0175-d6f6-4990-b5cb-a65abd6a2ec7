# Team Training: Proven Testing Patterns

**Date**: January 25, 2025  
**Purpose**: Share proven patterns from Phase 3 test migration success  
**Audience**: Development Team  

## Overview

During Phase 3 of our Jest to Vitest migration, we successfully stabilized 31 core tests (69% success rate) by identifying and implementing proven patterns. This document shares these patterns for team adoption.

## ✅ Proven Patterns That Work

### 1. Simple Mock Implementations

**Pattern**: Use direct function mocking without complex dependency chains

```typescript
// ✅ GOOD: Simple, focused mocks
const mockSupabase = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
};

// ❌ AVOID: Complex mock chains that break easily
const mockSupabase = createComplexMockChain();
```

**Why it works**: Simple mocks are predictable and don't rely on complex internal state.

### 2. Isolated Test Logic

**Pattern**: Tests that don't rely on external services

```typescript
// ✅ GOOD: Self-contained test logic
it('should validate API key format', () => {
  const result = validateApiKey('test-key-123');
  expect(result).toBe(true);
});

// ❌ AVOID: Tests that require real services
it('should connect to database', async () => {
  const db = await connectToRealDatabase();
  // This will fail in test environment
});
```

### 3. Clear Interface Definitions

**Pattern**: Well-defined TypeScript interfaces for mocks

```typescript
// ✅ GOOD: Clear interface definition
type MockRedis = {
  get: ReturnType<typeof vi.fn>;
  set: ReturnType<typeof vi.fn>;
  keys: ReturnType<typeof vi.fn>;
  del: ReturnType<typeof vi.fn>;
};

const mockRedis: MockRedis = {
  get: vi.fn(),
  set: vi.fn(),
  keys: vi.fn(),
  del: vi.fn(),
};
```

### 4. Deterministic Test Data

**Pattern**: Predictable test scenarios without external dependencies

```typescript
// ✅ GOOD: Deterministic test data
const testApiKey = 'test-api-key-123';
const expectedHash = 'hash_test-api-key-123';

// ❌ AVOID: Random or time-dependent data
const testApiKey = generateRandomKey();
const timestamp = Date.now();
```

## ❌ Patterns That Need Refactoring

### 1. Module-Level Dependency Injection

**Problem**: Real services being instantiated at module load time

```javascript
// ❌ PROBLEMATIC: Hard to mock
const redis = new Redis(process.env.REDIS_URL);
const supabase = createClient(url, key);

function myFunction() {
  return redis.get('key'); // Uses real Redis
}
```

**Solution**: Use dependency injection

```javascript
// ✅ BETTER: Testable with dependency injection
function createMyModule(redis, supabase) {
  function myFunction() {
    return redis.get('key'); // Uses injected Redis
  }
  return { myFunction };
}
```

### 2. Complex Mock Chains

**Problem**: Multi-level mocking with external services

```javascript
// ❌ PROBLEMATIC: Complex chain that breaks
mockSupabase.from().select().eq().single().mockResolvedValue(data);
```

**Solution**: Simplify or use integration tests

```javascript
// ✅ BETTER: Simple mock or integration test
mockSupabase.query.mockResolvedValue(data);
```

## 🏗️ Architectural Improvements

### Dependency Injection Pattern

We've created a simple DI container to improve testability:

```javascript
// Register dependencies
container.register('redis', () => new Redis(url));
container.register('logger', () => createLogger());

// Create testable modules
const createModule = (redis, logger) => {
  // Module implementation
};

module.exports = createTestableModule(createModule, ['redis', 'logger']);
```

### Testing with DI

```javascript
// In tests
const testContainer = new DIContainer();
testContainer.register('redis', mockRedis);
testContainer.register('logger', mockLogger);

const module = createModule(testContainer);
```

## 📋 Testing Checklist

Before writing tests, ask:

- [ ] Can this be tested without external services?
- [ ] Are my mocks simple and focused?
- [ ] Do I have clear interfaces for my mocks?
- [ ] Is my test data deterministic?
- [ ] Will this test be stable over time?

## 🎯 Quick Wins

### 1. Use Our Stable Test Scripts

```bash
# Run only stable tests
npm run test:stable

# Check test stability
npm run test:stability
```

### 2. Follow Proven Patterns

- Copy patterns from `tests/unit/api-key-middleware.test.ts`
- Use simple mocks like in `tests/unit/simple-rate-limit.test.ts`
- Follow structure from `tests/unit/visual-editors/visual-editors-api.test.ts`

### 3. Move Complex Scenarios

- Use `tests/integration/complex-scenarios/` for complex tests
- Focus unit tests on simple, isolated functionality
- Save integration tests for end-to-end scenarios

## 🚀 Next Steps for Team

1. **Review Examples**: Study the 4 stable test files
2. **Apply Patterns**: Use proven patterns in new tests
3. **Refactor Gradually**: Improve existing tests incrementally
4. **Ask Questions**: Reach out when encountering mocking issues

## 📚 Resources

- **Stable Tests**: `tests/unit/api-key-middleware.test.ts` (best example)
- **DI Pattern**: `shared/utils/dependency-injection.js`
- **Integration Tests**: `tests/integration/complex-scenarios/`
- **Monitoring**: `scripts/test-stability-monitor.js`

## 🤝 Getting Help

When you encounter testing issues:

1. Check if the pattern exists in our stable tests
2. Consider if it should be an integration test instead
3. Use the DI pattern for complex dependencies
4. Ask the team for pattern guidance

Remember: **Simple, focused tests are better than complex, brittle tests.**
