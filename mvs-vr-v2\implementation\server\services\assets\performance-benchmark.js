/**
 * Performance Benchmark Service
 * 
 * This service provides performance benchmarking for adaptive compression
 * across different client device types and configurations.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { compressFile, detectClientCapabilities, determineCompressionSettings } = require('./adaptive-compression');
const logger = require('../../utils/logger').getLogger('performance-benchmark');

const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const unlinkAsync = promisify(fs.unlink);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

/**
 * Device profiles for testing
 */
const deviceProfiles = {
  'low-end-mobile': {
    name: 'Low-end Mobile',
    headers: {
      'accept-encoding': 'gzip, deflate',
      'device-memory': '1',
      'ect': '2g',
      'viewport-width': '320',
      'hardware-concurrency': '2',
      'user-agent': 'Mozilla/5.0 (Linux; Android 8.0; SM-G532M) AppleWebKit/537.36'
    }
  },
  'mid-range-mobile': {
    name: 'Mid-range Mobile',
    headers: {
      'accept-encoding': 'gzip, deflate, br',
      'device-memory': '4',
      'ect': '3g',
      'viewport-width': '375',
      'hardware-concurrency': '4',
      'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
  },
  'high-end-mobile': {
    name: 'High-end Mobile',
    headers: {
      'accept-encoding': 'gzip, deflate, br',
      'device-memory': '8',
      'ect': '4g',
      'viewport-width': '414',
      'dpr': '3',
      'hardware-concurrency': '6',
      'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15'
    }
  },
  'tablet': {
    name: 'Tablet',
    headers: {
      'accept-encoding': 'gzip, deflate, br',
      'device-memory': '6',
      'ect': '4g',
      'viewport-width': '768',
      'dpr': '2',
      'hardware-concurrency': '4',
      'user-agent': 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
    }
  },
  'low-end-desktop': {
    name: 'Low-end Desktop',
    headers: {
      'accept-encoding': 'gzip, deflate, br',
      'device-memory': '4',
      'ect': '3g',
      'viewport-width': '1366',
      'hardware-concurrency': '2',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/85.0.4183.121'
    }
  },
  'high-end-desktop': {
    name: 'High-end Desktop',
    headers: {
      'accept-encoding': 'gzip, deflate, br',
      'device-memory': '16',
      'ect': '4g',
      'viewport-width': '1920',
      'dpr': '2',
      'hardware-concurrency': '8',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
    }
  },
  'save-data-enabled': {
    name: 'Save-Data Enabled',
    headers: {
      'accept-encoding': 'gzip, deflate, br',
      'device-memory': '4',
      'ect': '3g',
      'viewport-width': '375',
      'save-data': 'on',
      'hardware-concurrency': '4',
      'user-agent': 'Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36'
    }
  }
};

/**
 * Test file configurations
 */
const testFileConfigs = [
  { name: 'small-text', size: 1024, type: 'text/plain', extension: '.txt' },
  { name: 'medium-json', size: 100 * 1024, type: 'application/json', extension: '.json' },
  { name: 'large-model', size: 5 * 1024 * 1024, type: 'model/gltf+json', extension: '.gltf' },
  { name: 'very-large-binary', size: 50 * 1024 * 1024, type: 'application/octet-stream', extension: '.bin' }
];

/**
 * Performance Benchmark Service
 */
class PerformanceBenchmarkService {
  constructor(options = {}) {
    this.outputDir = options.outputDir || path.join(__dirname, '../../temp/benchmarks');
    this.testDataDir = path.join(this.outputDir, 'test-data');
    this.resultsDir = path.join(this.outputDir, 'results');
  }

  /**
   * Initialize benchmark environment
   */
  async initialize() {
    // Create directories
    await this.ensureDirectory(this.outputDir);
    await this.ensureDirectory(this.testDataDir);
    await this.ensureDirectory(this.resultsDir);

    // Generate test files
    await this.generateTestFiles();
  }

  /**
   * Ensure directory exists
   */
  async ensureDirectory(dirPath) {
    if (!await existsAsync(dirPath)) {
      await mkdirAsync(dirPath, { recursive: true });
    }
  }

  /**
   * Generate test files of different sizes and types
   */
  async generateTestFiles() {
    logger.info('Generating test files for benchmarking...');

    for (const config of testFileConfigs) {
      const filePath = path.join(this.testDataDir, config.name + config.extension);
      
      if (await existsAsync(filePath)) {
        continue; // Skip if file already exists
      }

      let content;
      if (config.type.startsWith('text/') || config.type === 'application/json') {
        // Generate text-based content
        const chunkSize = 100;
        const chunks = Math.ceil(config.size / chunkSize);
        content = Array(chunks).fill(0).map(() => 
          Math.random().toString(36).substring(2, chunkSize + 2)
        ).join('');
      } else {
        // Generate binary content
        content = Buffer.alloc(config.size);
        for (let i = 0; i < config.size; i++) {
          content[i] = Math.floor(Math.random() * 256);
        }
      }

      await writeFileAsync(filePath, content);
      logger.info(`Generated test file: ${config.name} (${config.size} bytes)`);
    }
  }

  /**
   * Run comprehensive benchmark across all device profiles and file types
   */
  async runComprehensiveBenchmark() {
    logger.info('Starting comprehensive performance benchmark...');
    
    const results = {
      timestamp: new Date().toISOString(),
      deviceProfiles: {},
      summary: {
        totalTests: 0,
        averageCompressionRatio: 0,
        averageCompressionTime: 0,
        bestPerformingDevice: null,
        worstPerformingDevice: null
      }
    };

    let totalCompressionRatio = 0;
    let totalCompressionTime = 0;
    let testCount = 0;

    for (const [profileKey, profile] of Object.entries(deviceProfiles)) {
      logger.info(`Testing device profile: ${profile.name}`);
      
      const deviceResults = {
        name: profile.name,
        capabilities: detectClientCapabilities(profile.headers),
        fileResults: {},
        averageCompressionRatio: 0,
        averageCompressionTime: 0,
        totalDataSavings: 0
      };

      let deviceCompressionRatio = 0;
      let deviceCompressionTime = 0;
      let deviceTestCount = 0;

      for (const fileConfig of testFileConfigs) {
        const inputPath = path.join(this.testDataDir, fileConfig.name + fileConfig.extension);
        const outputPath = path.join(this.resultsDir, `${profileKey}-${fileConfig.name}.gz`);

        try {
          const startTime = Date.now();
          const result = await compressFile(inputPath, outputPath, {
            mimeType: fileConfig.type,
            clientCapabilities: deviceResults.capabilities,
            fileSize: fileConfig.size
          });
          const endTime = Date.now();

          const fileResult = {
            ...result,
            actualCompressionTime: (endTime - startTime) / 1000,
            dataSavings: result.fileSize - result.compressedSize,
            dataSavingsPercent: ((result.fileSize - result.compressedSize) / result.fileSize) * 100,
            compressionSettings: determineCompressionSettings(
              fileConfig.type, 
              deviceResults.capabilities, 
              fileConfig.size
            )
          };

          deviceResults.fileResults[fileConfig.name] = fileResult;
          
          deviceCompressionRatio += result.compressionRatio;
          deviceCompressionTime += fileResult.actualCompressionTime;
          deviceTestCount++;

          totalCompressionRatio += result.compressionRatio;
          totalCompressionTime += fileResult.actualCompressionTime;
          testCount++;

          deviceResults.totalDataSavings += fileResult.dataSavings;

          logger.info(`  ${fileConfig.name}: ${result.compressionRatio.toFixed(2)}x compression in ${fileResult.actualCompressionTime.toFixed(3)}s`);

        } catch (error) {
          logger.error(`Error testing ${fileConfig.name} with ${profile.name}:`, error);
          deviceResults.fileResults[fileConfig.name] = { error: error.message };
        }
      }

      deviceResults.averageCompressionRatio = deviceCompressionRatio / deviceTestCount;
      deviceResults.averageCompressionTime = deviceCompressionTime / deviceTestCount;
      
      results.deviceProfiles[profileKey] = deviceResults;
    }

    // Calculate summary statistics
    results.summary.totalTests = testCount;
    results.summary.averageCompressionRatio = totalCompressionRatio / testCount;
    results.summary.averageCompressionTime = totalCompressionTime / testCount;

    // Find best and worst performing devices
    let bestRatio = 0;
    let worstTime = 0;
    for (const [profileKey, deviceResult] of Object.entries(results.deviceProfiles)) {
      if (deviceResult.averageCompressionRatio > bestRatio) {
        bestRatio = deviceResult.averageCompressionRatio;
        results.summary.bestPerformingDevice = profileKey;
      }
      if (deviceResult.averageCompressionTime > worstTime) {
        worstTime = deviceResult.averageCompressionTime;
        results.summary.worstPerformingDevice = profileKey;
      }
    }

    // Save results
    const resultsPath = path.join(this.resultsDir, `benchmark-results-${Date.now()}.json`);
    await writeFileAsync(resultsPath, JSON.stringify(results, null, 2));

    logger.info('Benchmark completed successfully');
    logger.info(`Results saved to: ${resultsPath}`);
    logger.info(`Average compression ratio: ${results.summary.averageCompressionRatio.toFixed(2)}x`);
    logger.info(`Average compression time: ${results.summary.averageCompressionTime.toFixed(3)}s`);

    return results;
  }

  /**
   * Clean up benchmark files
   */
  async cleanup() {
    // Remove test files and compressed files
    for (const fileConfig of testFileConfigs) {
      const testFile = path.join(this.testDataDir, fileConfig.name + fileConfig.extension);
      if (await existsAsync(testFile)) {
        await unlinkAsync(testFile);
      }
    }

    // Remove compressed files
    const files = await fs.promises.readdir(this.resultsDir);
    for (const file of files) {
      if (file.endsWith('.gz')) {
        await unlinkAsync(path.join(this.resultsDir, file));
      }
    }
  }
}

module.exports = {
  PerformanceBenchmarkService,
  deviceProfiles,
  testFileConfigs
};
