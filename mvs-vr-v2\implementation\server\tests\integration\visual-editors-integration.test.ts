/**
 * Visual Editors Integration Tests
 * 
 * Tests cross-component interactions and data flow between Visual Editors components
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount, createLocalVue } from '@vue/test-utils';
import VisualEditors from '../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue';
import ShowroomLayoutEditor from '../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue';
import ProductConfigurator from '../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue';
import MaterialTextureEditor from '../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue';
import LightingEditor from '../../directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue';

// Mock API responses
const mockApiResponses = {
  showrooms: [
    {
      id: 'showroom-1',
      name: 'Test Showroom',
      vendor_id: 'vendor-1',
      template: 'template_1',
      items: []
    }
  ],
  products: [
    {
      id: 'product-1',
      name: 'Test Product',
      vendor_id: 'vendor-1',
      thumbnail: '/test-thumbnail.jpg'
    }
  ],
  materials: [
    {
      id: 'material-1',
      name: 'Test Material',
      vendor_id: 'vendor-1',
      type: 'Standard',
      textures: {}
    }
  ],
  lighting: [
    {
      id: 'lighting-1',
      showroom_id: 'showroom-1',
      lights: []
    }
  ]
};

// Mock API client
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

describe('Visual Editors Integration Tests', () => {
  let localVue;
  let wrapper;

  beforeEach(() => {
    localVue = createLocalVue();
    
    // Reset all mocks
    vi.clearAllMocks();
    
    // Setup default API responses
    mockApi.get.mockImplementation((url) => {
      if (url.includes('/items/showroom_layouts')) {
        return Promise.resolve({ data: { data: mockApiResponses.showrooms } });
      }
      if (url.includes('/items/products')) {
        return Promise.resolve({ data: { data: mockApiResponses.products } });
      }
      if (url.includes('/items/materials')) {
        return Promise.resolve({ data: { data: mockApiResponses.materials } });
      }
      if (url.includes('/items/showroom_lighting')) {
        return Promise.resolve({ data: { data: mockApiResponses.lighting } });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    mockApi.post.mockResolvedValue({ data: { data: { id: 'new-id' } } });
    mockApi.patch.mockResolvedValue({ data: { data: { id: 'updated-id' } } });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('Cross-Component Data Flow', () => {
    it('should share vendor data across all editor components', async () => {
      wrapper = mount(VisualEditors, {
        localVue,
        propsData: {
          vendorId: 'vendor-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Verify vendor ID is passed to all child components
      const showroomEditor = wrapper.findComponent(ShowroomLayoutEditor);
      const productConfigurator = wrapper.findComponent(ProductConfigurator);
      const materialEditor = wrapper.findComponent(MaterialTextureEditor);
      const lightingEditor = wrapper.findComponent(LightingEditor);

      expect(showroomEditor.props('vendorId')).toBe('vendor-1');
      expect(productConfigurator.props('vendorId')).toBe('vendor-1');
      expect(materialEditor.props('vendorId')).toBe('vendor-1');
      expect(lightingEditor.props('vendorId')).toBe('vendor-1');
    });

    it('should update related components when showroom changes', async () => {
      wrapper = mount(VisualEditors, {
        localVue,
        propsData: {
          vendorId: 'vendor-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Simulate showroom selection
      await wrapper.vm.selectShowroom('showroom-1');
      await wrapper.vm.$nextTick();

      // Verify lighting editor receives showroom ID
      const lightingEditor = wrapper.findComponent(LightingEditor);
      expect(lightingEditor.props('showroomId')).toBe('showroom-1');
    });

    it('should handle product-material relationships', async () => {
      wrapper = mount(VisualEditors, {
        localVue,
        propsData: {
          vendorId: 'vendor-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Switch to product configurator
      await wrapper.vm.setActiveTab('product');
      await wrapper.vm.$nextTick();

      // Select a product
      await wrapper.vm.selectProduct('product-1');
      await wrapper.vm.$nextTick();

      // Switch to material editor
      await wrapper.vm.setActiveTab('material');
      await wrapper.vm.$nextTick();

      // Verify material editor can access product context
      const materialEditor = wrapper.findComponent(MaterialTextureEditor);
      expect(materialEditor.exists()).toBe(true);
    });
  });

  describe('Data Persistence Integration', () => {
    it('should save and load showroom layout data', async () => {
      wrapper = mount(ShowroomLayoutEditor, {
        localVue,
        propsData: {
          vendorId: 'vendor-1',
          showroomId: 'showroom-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Modify layout
      wrapper.vm.layout.name = 'Updated Showroom';
      wrapper.vm.layout.items.push({
        id: 'item-1',
        productId: 'product-1',
        x: 100,
        y: 100,
        rotation: 0
      });

      // Save layout
      await wrapper.vm.saveLayout();

      // Verify API call
      expect(mockApi.patch).toHaveBeenCalledWith(
        '/items/showroom_layouts/showroom-1',
        expect.objectContaining({
          name: 'Updated Showroom',
          items: expect.arrayContaining([
            expect.objectContaining({
              id: 'item-1',
              productId: 'product-1'
            })
          ])
        })
      );
    });

    it('should save and load product configuration data', async () => {
      wrapper = mount(ProductConfigurator, {
        localVue,
        propsData: {
          vendorId: 'vendor-1',
          productId: 'product-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Load product
      await wrapper.vm.loadProduct({ id: 'product-1', name: 'Test Product' });
      await wrapper.vm.$nextTick();

      // Add configuration option
      wrapper.vm.addOptionGroup();
      wrapper.vm.configuration.optionGroups[0].name = 'Color';
      wrapper.vm.configuration.optionGroups[0].options.push({
        id: 'option-1',
        name: 'Red',
        value: '#FF0000'
      });

      // Save configuration
      await wrapper.vm.saveConfiguration();

      // Verify API call
      expect(mockApi.post).toHaveBeenCalledWith(
        '/items/product_configurations',
        expect.objectContaining({
          product_id: 'product-1',
          option_groups: expect.arrayContaining([
            expect.objectContaining({
              name: 'Color'
            })
          ])
        })
      );
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle API errors gracefully across components', async () => {
      // Mock API error
      mockApi.get.mockRejectedValue(new Error('API Error'));

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      wrapper = mount(VisualEditors, {
        localVue,
        propsData: {
          vendorId: 'vendor-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Verify error handling
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('should validate data before saving', async () => {
      wrapper = mount(ShowroomLayoutEditor, {
        localVue,
        propsData: {
          vendorId: 'vendor-1'
        },
        mocks: {
          $api: mockApi
        }
      });

      await wrapper.vm.$nextTick();

      // Try to save invalid layout
      wrapper.vm.layout.name = '';
      
      // Should not call API with invalid data
      await wrapper.vm.saveLayout();
      
      // Verify validation prevents save
      expect(mockApi.post).not.toHaveBeenCalled();
    });
  });
});
