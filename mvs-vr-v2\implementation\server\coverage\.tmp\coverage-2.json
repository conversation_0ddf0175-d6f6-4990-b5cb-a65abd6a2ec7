{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1436", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/LightingEditor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 58737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 58737, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2284, "endOffset": 19639, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2342, "endOffset": 3180, "count": 39}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2431, "endOffset": 2770, "count": 39}, {"startOffset": 2495, "endOffset": 2575, "count": 0}, {"startOffset": 2710, "endOffset": 2769, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3217, "endOffset": 3278, "count": 39}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 4949, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3405, "endOffset": 3573, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3656, "endOffset": 4158, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4224, "endOffset": 4415, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4486, "endOffset": 4681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4751, "endOffset": 4943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5005, "endOffset": 5690, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5074, "endOffset": 5289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5361, "endOffset": 5684, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5742, "endOffset": 6621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5837, "endOffset": 6053, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6147, "endOffset": 6615, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6677, "endOffset": 9008, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6738, "endOffset": 7256, "count": 1}, {"startOffset": 6865, "endOffset": 7255, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7311, "endOffset": 7617, "count": 1}, {"startOffset": 7506, "endOffset": 7616, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7675, "endOffset": 8306, "count": 1}, {"startOffset": 7937, "endOffset": 8305, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8361, "endOffset": 8627, "count": 1}, {"startOffset": 8544, "endOffset": 8626, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8696, "endOffset": 9002, "count": 1}, {"startOffset": 8923, "endOffset": 9001, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9064, "endOffset": 11965, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9133, "endOffset": 9434, "count": 1}, {"startOffset": 9346, "endOffset": 9433, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9498, "endOffset": 9805, "count": 1}, {"startOffset": 9714, "endOffset": 9804, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9866, "endOffset": 10136, "count": 1}, {"startOffset": 10065, "endOffset": 10135, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10201, "endOffset": 10467, "count": 1}, {"startOffset": 10398, "endOffset": 10466, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10545, "endOffset": 10845, "count": 1}, {"startOffset": 10776, "endOffset": 10844, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10692, "endOffset": 10717, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10916, "endOffset": 11583, "count": 1}, {"startOffset": 11444, "endOffset": 11582, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11646, "endOffset": 11959, "count": 1}, {"startOffset": 11880, "endOffset": 11958, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12025, "endOffset": 13289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12086, "endOffset": 12292, "count": 1}, {"startOffset": 12168, "endOffset": 12291, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12358, "endOffset": 12645, "count": 1}, {"startOffset": 12433, "endOffset": 12644, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12698, "endOffset": 12946, "count": 1}, {"startOffset": 12829, "endOffset": 12945, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13008, "endOffset": 13283, "count": 1}, {"startOffset": 13080, "endOffset": 13282, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13345, "endOffset": 14751, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13414, "endOffset": 14291, "count": 1}, {"startOffset": 14003, "endOffset": 14290, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14362, "endOffset": 14745, "count": 1}, {"startOffset": 14490, "endOffset": 14744, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14819, "endOffset": 16092, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14887, "endOffset": 15269, "count": 1}, {"startOffset": 15093, "endOffset": 15268, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15345, "endOffset": 15750, "count": 1}, {"startOffset": 15641, "endOffset": 15749, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15811, "endOffset": 16086, "count": 1}, {"startOffset": 15931, "endOffset": 16085, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16160, "endOffset": 17928, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16255, "endOffset": 16633, "count": 1}, {"startOffset": 16397, "endOffset": 16632, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16719, "endOffset": 17103, "count": 1}, {"startOffset": 16845, "endOffset": 17102, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17184, "endOffset": 17563, "count": 1}, {"startOffset": 17390, "endOffset": 17562, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17643, "endOffset": 17922, "count": 1}, {"startOffset": 17769, "endOffset": 17921, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17984, "endOffset": 18744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18062, "endOffset": 18248, "count": 1}, {"startOffset": 18141, "endOffset": 18247, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18312, "endOffset": 18459, "count": 1}, {"startOffset": 18376, "endOffset": 18458, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18526, "endOffset": 18738, "count": 1}, {"startOffset": 18645, "endOffset": 18737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18798, "endOffset": 19635, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18875, "endOffset": 19217, "count": 1}, {"startOffset": 19112, "endOffset": 19216, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18982, "endOffset": 18997, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19289, "endOffset": 19629, "count": 1}, {"startOffset": 19524, "endOffset": 19628, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19396, "endOffset": 19411, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1437", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 101166, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 101166, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1036, "endOffset": 1168, "count": 40}], "isBlockCoverage": true}, {"functionName": "selectedLight", "ranges": [{"startOffset": 1189, "endOffset": 1295, "count": 79}, {"startOffset": 1281, "endOffset": 1288, "count": 40}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1237, "endOffset": 1279, "count": 39}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1306, "endOffset": 1449, "count": 79}, {"startOffset": 1356, "endOffset": 1442, "count": 39}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 1458, "endOffset": 1494, "count": 40}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 1514, "endOffset": 2442, "count": 40}, {"startOffset": 1561, "endOffset": 1568, "count": 1}, {"startOffset": 1568, "endOffset": 1861, "count": 39}, {"startOffset": 1863, "endOffset": 1934, "count": 39}, {"startOffset": 1918, "endOffset": 1923, "count": 0}, {"startOffset": 1934, "endOffset": 2054, "count": 0}, {"startOffset": 2054, "endOffset": 2292, "count": 39}, {"startOffset": 2301, "endOffset": 2386, "count": 0}, {"startOffset": 2386, "endOffset": 2436, "count": 39}], "isBlockCoverage": true}, {"functionName": "getDefaultLighting", "ranges": [{"startOffset": 2453, "endOffset": 2964, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLightIcon", "ranges": [{"startOffset": 2975, "endOffset": 3255, "count": 78}, {"startOffset": 3026, "endOffset": 3059, "count": 39}, {"startOffset": 3068, "endOffset": 3100, "count": 0}, {"startOffset": 3109, "endOffset": 3147, "count": 39}, {"startOffset": 3156, "endOffset": 3196, "count": 0}, {"startOffset": 3205, "endOffset": 3241, "count": 0}], "isBlockCoverage": true}, {"functionName": "getLightTypeName", "ranges": [{"startOffset": 3266, "endOffset": 3551, "count": 78}, {"startOffset": 3321, "endOffset": 3356, "count": 39}, {"startOffset": 3365, "endOffset": 3398, "count": 0}, {"startOffset": 3407, "endOffset": 3454, "count": 39}, {"startOffset": 3463, "endOffset": 3502, "count": 0}, {"startOffset": 3511, "endOffset": 3537, "count": 0}], "isBlockCoverage": true}, {"functionName": "createLight", "ranges": [{"startOffset": 3562, "endOffset": 4414, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectLight", "ranges": [{"startOffset": 4425, "endOffset": 4491, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteLight", "ranges": [{"startOffset": 4502, "endOffset": 5004, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLighting", "ranges": [{"startOffset": 5015, "endOffset": 6089, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetLighting", "ranges": [{"startOffset": 6100, "endOffset": 6632, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 10054, "endOffset": 40902, "count": 79}, {"startOffset": 10405, "endOffset": 10770, "count": 40}, {"startOffset": 10964, "endOffset": 11051, "count": 40}, {"startOffset": 11116, "endOffset": 11348, "count": 40}, {"startOffset": 11502, "endOffset": 11591, "count": 40}, {"startOffset": 11656, "endOffset": 11892, "count": 40}, {"startOffset": 12163, "endOffset": 12283, "count": 40}, {"startOffset": 12380, "endOffset": 12508, "count": 40}, {"startOffset": 12731, "endOffset": 12789, "count": 40}, {"startOffset": 12818, "endOffset": 13074, "count": 40}, {"startOffset": 13223, "endOffset": 13280, "count": 40}, {"startOffset": 13309, "endOffset": 13564, "count": 40}, {"startOffset": 13713, "endOffset": 13777, "count": 40}, {"startOffset": 13806, "endOffset": 14067, "count": 40}, {"startOffset": 14216, "endOffset": 14276, "count": 40}, {"startOffset": 14305, "endOffset": 14568, "count": 40}, {"startOffset": 14726, "endOffset": 15247, "count": 40}, {"startOffset": 15262, "endOffset": 15318, "count": 39}, {"startOffset": 17353, "endOffset": 17757, "count": 40}, {"startOffset": 17768, "endOffset": 40878, "count": 39}, {"startOffset": 23216, "endOffset": 26470, "count": 0}, {"startOffset": 26707, "endOffset": 32909, "count": 0}, {"startOffset": 35341, "endOffset": 35455, "count": 0}, {"startOffset": 35740, "endOffset": 35815, "count": 0}, {"startOffset": 36699, "endOffset": 36813, "count": 0}, {"startOffset": 37098, "endOffset": 37173, "count": 0}, {"startOffset": 38047, "endOffset": 38103, "count": 0}, {"startOffset": 38271, "endOffset": 40253, "count": 0}, {"startOffset": 40272, "endOffset": 40328, "count": 0}, {"startOffset": 40372, "endOffset": 40862, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 10980, "endOffset": 11050, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 11518, "endOffset": 11590, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 12747, "endOffset": 12788, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 13239, "endOffset": 13279, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 13729, "endOffset": 13776, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 14232, "endOffset": 14275, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15498, "endOffset": 17176, "count": 78}, {"startOffset": 16896, "endOffset": 17058, "count": 39}], "isBlockCoverage": true}, {"functionName": "onClick", "ranges": [{"startOffset": 15799, "endOffset": 15841, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16805, "endOffset": 16847, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 18774, "endOffset": 18824, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 19680, "endOffset": 19733, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 20817, "endOffset": 20868, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 21275, "endOffset": 21326, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 22265, "endOffset": 22320, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 24216, "endOffset": 24270, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 25631, "endOffset": 25682, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 27704, "endOffset": 27758, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 29117, "endOffset": 29168, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 30645, "endOffset": 30699, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 32070, "endOffset": 32121, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 34399, "endOffset": 34456, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 35757, "endOffset": 35814, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 37115, "endOffset": 37172, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 39289, "endOffset": 39346, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 39785, "endOffset": 39842, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1448", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1449", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/%00plugin-vue:export-helper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_exports__.default", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}, {"startOffset": 308, "endOffset": 336, "count": 3}], "isBlockCoverage": true}]}]}