#!/usr/bin/env node

/**
 * Comprehensive Jest to Vitest Migration Script
 * This script automatically migrates Jest test files to Vitest syntax
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  testDirs: ['tests', 'src'],
  testFilePatterns: [/\.test\.js$/, /\.spec\.js$/],
  backupDir: '.migration-backup',
  dryRun: false, // Set to true to see what would be changed without making changes
};

// Migration patterns
const migrationPatterns = [
  // Import statements
  {
    name: 'Add Vitest imports',
    pattern: /^(import.*from ['"][^'"]*['"];?\s*\n)/m,
    replacement: (match, imports) => {
      if (imports.includes('vitest')) return match;
      return `import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';\n${imports}`;
    }
  },
  
  // Jest mock calls
  {
    name: 'Replace jest.mock with vi.mock',
    pattern: /jest\.mock\(/g,
    replacement: 'vi.mock('
  },
  
  // Jest function calls
  {
    name: 'Replace jest.fn with vi.fn',
    pattern: /jest\.fn\(/g,
    replacement: 'vi.fn('
  },
  
  // Jest spy calls
  {
    name: 'Replace jest.spyOn with vi.spyOn',
    pattern: /jest\.spyOn\(/g,
    replacement: 'vi.spyOn('
  },
  
  // Jest clear calls
  {
    name: 'Replace jest.clearAllMocks with vi.clearAllMocks',
    pattern: /jest\.clearAllMocks\(/g,
    replacement: 'vi.clearAllMocks('
  },
  
  // Jest reset calls
  {
    name: 'Replace jest.resetAllMocks with vi.resetAllMocks',
    pattern: /jest\.resetAllMocks\(/g,
    replacement: 'vi.resetAllMocks('
  },
  
  // Mock return values
  {
    name: 'Fix mock return values for ES modules',
    pattern: /vi\.mock\(['"]([^'"]+)['"],\s*\(\)\s*=>\s*\(\{([^}]+)\}\)\)/g,
    replacement: (match, modulePath, mockContent) => {
      // Add default export wrapper for ES modules
      return `vi.mock('${modulePath}', () => ({\n  default: {\n${mockContent}\n  }\n}))`;
    }
  },
  
  // Fix axios mock calls
  {
    name: 'Fix axios mock calls',
    pattern: /vi\.mocked\(axios\.create\(\)\.(\w+)\)/g,
    replacement: 'mockAxiosInstance.$1'
  }
];

// Utility functions
function findTestFiles(dir, patterns) {
  const files = [];
  
  function scanDir(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDir(fullPath);
      } else if (stat.isFile()) {
        const isTestFile = patterns.some(pattern => pattern.test(item));
        if (isTestFile) {
          files.push(fullPath);
        }
      }
    }
  }
  
  if (fs.existsSync(dir)) {
    scanDir(dir);
  }
  
  return files;
}

function hasJestSyntax(content) {
  const jestPatterns = [
    /jest\.mock\(/,
    /jest\.fn\(/,
    /jest\.spyOn\(/,
    /jest\.clearAllMocks\(/,
    /jest\.resetAllMocks\(/,
    /from ['"]jest['"]/
  ];
  
  return jestPatterns.some(pattern => pattern.test(content));
}

function createBackup(filePath) {
  const backupPath = path.join(config.backupDir, filePath);
  const backupDir = path.dirname(backupPath);
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  fs.copyFileSync(filePath, backupPath);
  console.log(`📁 Backup created: ${backupPath}`);
}

function migrateFile(filePath) {
  console.log(`\n🔄 Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Check if file needs migration
  if (!hasJestSyntax(content)) {
    console.log(`⏭️  Skipping: No Jest syntax found`);
    return false;
  }
  
  // Create backup
  if (!config.dryRun) {
    createBackup(filePath);
  }
  
  // Apply migration patterns
  let changesMade = false;
  
  for (const pattern of migrationPatterns) {
    const beforeLength = content.length;
    
    if (typeof pattern.replacement === 'function') {
      content = content.replace(pattern.pattern, pattern.replacement);
    } else {
      content = content.replace(pattern.pattern, pattern.replacement);
    }
    
    if (content.length !== beforeLength || content !== originalContent) {
      console.log(`  ✅ Applied: ${pattern.name}`);
      changesMade = true;
    }
  }
  
  // Write changes
  if (changesMade && !config.dryRun) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  💾 File updated`);
  } else if (config.dryRun) {
    console.log(`  🔍 DRY RUN: Would update file`);
  }
  
  return changesMade;
}

// Main execution
function main() {
  console.log('🚀 Starting Jest to Vitest Migration\n');
  
  if (config.dryRun) {
    console.log('🔍 DRY RUN MODE - No files will be modified\n');
  }
  
  // Find all test files
  const allTestFiles = [];
  for (const dir of config.testDirs) {
    const files = findTestFiles(dir, config.testFilePatterns);
    allTestFiles.push(...files);
  }
  
  console.log(`📊 Found ${allTestFiles.length} test files\n`);
  
  // Process each file
  let migratedCount = 0;
  
  for (const filePath of allTestFiles) {
    const wasMigrated = migrateFile(filePath);
    if (wasMigrated) {
      migratedCount++;
    }
  }
  
  console.log(`\n✅ Migration Complete!`);
  console.log(`📊 Files processed: ${allTestFiles.length}`);
  console.log(`🔄 Files migrated: ${migratedCount}`);
  console.log(`⏭️  Files skipped: ${allTestFiles.length - migratedCount}`);
  
  if (!config.dryRun && migratedCount > 0) {
    console.log(`\n📁 Backups stored in: ${config.backupDir}`);
    console.log(`\n🧪 Run tests to verify migration: npm run test:vitest`);
  }
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = { migrateFile, findTestFiles, hasJestSyntax };
