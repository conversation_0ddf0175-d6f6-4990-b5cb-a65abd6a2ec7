/**
 * API Response Auditor
 * 
 * This middleware audits API responses for sensitive information disclosure
 * and provides comprehensive reporting and alerting capabilities.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const crypto = require('crypto');

const writeFileAsync = promisify(fs.writeFile);
const appendFileAsync = promisify(fs.appendFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

const logger = require('../utils/logger').getLogger('api-response-auditor');

// Audit configuration
const AUDIT_CONFIG = {
  enabled: process.env.API_AUDIT_ENABLED !== 'false',
  logPath: process.env.API_AUDIT_LOG_PATH || path.join(__dirname, '../logs/audit'),
  alertThreshold: parseInt(process.env.API_AUDIT_ALERT_THRESHOLD || '10', 10),
  retentionDays: parseInt(process.env.API_AUDIT_RETENTION_DAYS || '90', 10),
  sensitivityLevels: {
    LOW: 1,
    MEDIUM: 2,
    HIGH: 3,
    CRITICAL: 4
  }
};

// Sensitive data patterns with severity levels
const SENSITIVE_PATTERNS = [
  // Critical severity
  {
    name: 'credit_card',
    pattern: /\b(?:\d[ -]*?){13,16}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL,
    description: 'Credit card number detected'
  },
  {
    name: 'ssn',
    pattern: /\b\d{3}[-]?\d{2}[-]?\d{4}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL,
    description: 'Social Security Number detected'
  },
  {
    name: 'api_key',
    pattern: /\b[A-Za-z0-9_]{32,}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL,
    description: 'API key detected'
  },
  
  // High severity
  {
    name: 'email',
    pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.HIGH,
    description: 'Email address detected'
  },
  {
    name: 'phone',
    pattern: /\b(?:\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.HIGH,
    description: 'Phone number detected'
  },
  {
    name: 'ip_address',
    pattern: /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.HIGH,
    description: 'IP address detected'
  },
  
  // Medium severity
  {
    name: 'date_of_birth',
    pattern: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4}\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.MEDIUM,
    description: 'Date of birth detected'
  },
  {
    name: 'postal_code',
    pattern: /\b\d{5}(?:-\d{4})?\b/g,
    severity: AUDIT_CONFIG.sensitivityLevels.MEDIUM,
    description: 'Postal code detected'
  },
  
  // Low severity
  {
    name: 'username',
    pattern: /\busername["\s]*[:=]["\s]*([^"\s,}]+)/gi,
    severity: AUDIT_CONFIG.sensitivityLevels.LOW,
    description: 'Username detected'
  }
];

// Sensitive field patterns
const SENSITIVE_FIELDS = [
  { pattern: /password/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /secret/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /token/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /key/i, severity: AUDIT_CONFIG.sensitivityLevels.HIGH },
  { pattern: /credential/i, severity: AUDIT_CONFIG.sensitivityLevels.HIGH },
  { pattern: /private/i, severity: AUDIT_CONFIG.sensitivityLevels.HIGH },
  { pattern: /ssn/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /social.*security/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /credit.*card/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /card.*number/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /cvv/i, severity: AUDIT_CONFIG.sensitivityLevels.CRITICAL },
  { pattern: /pin/i, severity: AUDIT_CONFIG.sensitivityLevels.HIGH },
  { pattern: /address/i, severity: AUDIT_CONFIG.sensitivityLevels.MEDIUM },
  { pattern: /phone/i, severity: AUDIT_CONFIG.sensitivityLevels.MEDIUM },
  { pattern: /email/i, severity: AUDIT_CONFIG.sensitivityLevels.MEDIUM }
];

// Audit statistics
let auditStats = {
  totalRequests: 0,
  sensitiveDataDetected: 0,
  alertsTriggered: 0,
  lastReset: Date.now()
};

/**
 * Initialize audit system
 */
async function initializeAudit() {
  try {
    if (!await existsAsync(AUDIT_CONFIG.logPath)) {
      await mkdirAsync(AUDIT_CONFIG.logPath, { recursive: true });
    }
    
    logger.info('API Response Auditor initialized');
  } catch (error) {
    logger.error('Failed to initialize audit system:', error);
  }
}

/**
 * Scan response data for sensitive information
 * @param {any} data - Response data to scan
 * @param {string} path - Current path in the data structure
 * @returns {Array} Array of detected sensitive data
 */
function scanForSensitiveData(data, path = '') {
  const detections = [];
  
  if (data == null) {
    return detections;
  }
  
  // Scan string content
  if (typeof data === 'string') {
    for (const pattern of SENSITIVE_PATTERNS) {
      const matches = data.match(pattern.pattern);
      if (matches) {
        detections.push({
          type: 'content',
          pattern: pattern.name,
          severity: pattern.severity,
          description: pattern.description,
          path,
          matches: matches.length,
          sample: matches[0].substring(0, 20) + '...'
        });
      }
    }
  }
  
  // Scan object fields
  if (typeof data === 'object' && !Array.isArray(data)) {
    for (const [key, value] of Object.entries(data)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      // Check field name against sensitive patterns
      for (const field of SENSITIVE_FIELDS) {
        if (field.pattern.test(key)) {
          detections.push({
            type: 'field',
            pattern: field.pattern.source,
            severity: field.severity,
            description: `Sensitive field name: ${key}`,
            path: currentPath,
            fieldName: key
          });
        }
      }
      
      // Recursively scan nested data
      detections.push(...scanForSensitiveData(value, currentPath));
    }
  }
  
  // Scan arrays
  if (Array.isArray(data)) {
    data.forEach((item, index) => {
      const currentPath = `${path}[${index}]`;
      detections.push(...scanForSensitiveData(item, currentPath));
    });
  }
  
  return detections;
}

/**
 * Generate audit report
 * @param {Object} req - Express request object
 * @param {any} responseData - Response data
 * @param {Array} detections - Detected sensitive data
 * @returns {Object} Audit report
 */
function generateAuditReport(req, responseData, detections) {
  const report = {
    timestamp: new Date().toISOString(),
    requestId: req.id || crypto.randomUUID(),
    method: req.method,
    path: req.path,
    query: req.query,
    user: {
      id: req.user?.id || 'anonymous',
      role: req.user?.role || 'unknown',
      ip: req.ip || req.connection?.remoteAddress
    },
    userAgent: req.get('User-Agent'),
    detections: detections.map(detection => ({
      ...detection,
      hash: crypto.createHash('sha256').update(JSON.stringify(detection)).digest('hex').substring(0, 16)
    })),
    severity: detections.length > 0 ? Math.max(...detections.map(d => d.severity)) : 0,
    responseSize: JSON.stringify(responseData).length,
    sensitiveDataCount: detections.length
  };
  
  return report;
}

/**
 * Log audit report
 * @param {Object} report - Audit report
 */
async function logAuditReport(report) {
  try {
    const logFile = path.join(AUDIT_CONFIG.logPath, `audit-${new Date().toISOString().split('T')[0]}.json`);
    const logEntry = JSON.stringify(report) + '\n';
    
    await appendFileAsync(logFile, logEntry);
    
    // Log to application logger based on severity
    if (report.severity >= AUDIT_CONFIG.sensitivityLevels.CRITICAL) {
      logger.error('CRITICAL: Sensitive data detected in API response', {
        requestId: report.requestId,
        path: report.path,
        user: report.user.id,
        detections: report.detections.length
      });
    } else if (report.severity >= AUDIT_CONFIG.sensitivityLevels.HIGH) {
      logger.warn('HIGH: Sensitive data detected in API response', {
        requestId: report.requestId,
        path: report.path,
        user: report.user.id,
        detections: report.detections.length
      });
    } else if (report.detections.length > 0) {
      logger.info('Sensitive data detected in API response', {
        requestId: report.requestId,
        path: report.path,
        user: report.user.id,
        detections: report.detections.length
      });
    }
    
  } catch (error) {
    logger.error('Failed to log audit report:', error);
  }
}

/**
 * Check if alert should be triggered
 * @param {Object} report - Audit report
 * @returns {boolean} Whether to trigger alert
 */
function shouldTriggerAlert(report) {
  // Trigger alert for critical severity
  if (report.severity >= AUDIT_CONFIG.sensitivityLevels.CRITICAL) {
    return true;
  }
  
  // Trigger alert if threshold exceeded
  if (auditStats.sensitiveDataDetected >= AUDIT_CONFIG.alertThreshold) {
    return true;
  }
  
  return false;
}

/**
 * Trigger security alert
 * @param {Object} report - Audit report
 */
async function triggerAlert(report) {
  try {
    auditStats.alertsTriggered++;
    
    const alert = {
      type: 'SENSITIVE_DATA_DISCLOSURE',
      severity: report.severity,
      timestamp: new Date().toISOString(),
      report,
      stats: { ...auditStats }
    };
    
    // Log alert
    logger.error('SECURITY ALERT: Sensitive data disclosure detected', alert);
    
    // Save alert to file
    const alertFile = path.join(AUDIT_CONFIG.logPath, `alerts-${new Date().toISOString().split('T')[0]}.json`);
    await appendFileAsync(alertFile, JSON.stringify(alert) + '\n');
    
    // TODO: Integrate with alerting system (email, Slack, etc.)
    
  } catch (error) {
    logger.error('Failed to trigger alert:', error);
  }
}

/**
 * API Response Auditor middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function apiResponseAuditor(options = {}) {
  const {
    enabled = AUDIT_CONFIG.enabled,
    skipPaths = ['/health', '/metrics', '/favicon.ico'],
    skipMethods = ['OPTIONS'],
    auditLevel = 'ALL' // 'ALL', 'SENSITIVE_ONLY', 'CRITICAL_ONLY'
  } = options;
  
  // Initialize audit system
  initializeAudit();
  
  return (req, res, next) => {
    if (!enabled) {
      return next();
    }
    
    // Skip certain paths and methods
    if (skipPaths.some(path => req.path.startsWith(path)) || 
        skipMethods.includes(req.method)) {
      return next();
    }
    
    // Store original res.json method
    const originalJson = res.json;
    
    // Override res.json method
    res.json = function(data) {
      auditStats.totalRequests++;
      
      try {
        // Scan response data for sensitive information
        const detections = scanForSensitiveData(data);
        
        // Generate audit report
        const report = generateAuditReport(req, data, detections);
        
        // Filter based on audit level
        let shouldAudit = false;
        if (auditLevel === 'ALL') {
          shouldAudit = true;
        } else if (auditLevel === 'SENSITIVE_ONLY' && detections.length > 0) {
          shouldAudit = true;
        } else if (auditLevel === 'CRITICAL_ONLY' && report.severity >= AUDIT_CONFIG.sensitivityLevels.CRITICAL) {
          shouldAudit = true;
        }
        
        if (shouldAudit) {
          // Log audit report
          logAuditReport(report);
          
          // Update statistics
          if (detections.length > 0) {
            auditStats.sensitiveDataDetected++;
          }
          
          // Check for alerts
          if (shouldTriggerAlert(report)) {
            triggerAlert(report);
          }
        }
        
      } catch (error) {
        logger.error('Error during response auditing:', error);
      }
      
      // Call original json method
      return originalJson.call(this, data);
    };
    
    next();
  };
}

/**
 * Get audit statistics
 * @returns {Object} Audit statistics
 */
function getAuditStats() {
  return {
    ...auditStats,
    uptime: Date.now() - auditStats.lastReset
  };
}

/**
 * Reset audit statistics
 */
function resetAuditStats() {
  auditStats = {
    totalRequests: 0,
    sensitiveDataDetected: 0,
    alertsTriggered: 0,
    lastReset: Date.now()
  };
}

module.exports = {
  apiResponseAuditor,
  getAuditStats,
  resetAuditStats,
  scanForSensitiveData,
  AUDIT_CONFIG,
  SENSITIVE_PATTERNS
};
