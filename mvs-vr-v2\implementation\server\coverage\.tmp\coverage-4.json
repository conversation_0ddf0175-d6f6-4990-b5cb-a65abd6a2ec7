{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1449", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/%00plugin-vue:export-helper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_exports__.default", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}, {"startOffset": 308, "endOffset": 336, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "1468", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/ProductConfigurator.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 54411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 54411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2148, "endOffset": 18453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2206, "endOffset": 3276, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2295, "endOffset": 2870, "count": 101}, {"startOffset": 2350, "endOffset": 2428, "count": 0}, {"startOffset": 2472, "endOffset": 2547, "count": 33}, {"startOffset": 2547, "endOffset": 2605, "count": 68}, {"startOffset": 2605, "endOffset": 2687, "count": 35}, {"startOffset": 2687, "endOffset": 2810, "count": 33}, {"startOffset": 2810, "endOffset": 2869, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3313, "endOffset": 3374, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3433, "endOffset": 4802, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3501, "endOffset": 3674, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3757, "endOffset": 4265, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4344, "endOffset": 4618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4690, "endOffset": 4796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4858, "endOffset": 5539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4927, "endOffset": 5142, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5213, "endOffset": 5533, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5591, "endOffset": 6499, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5661, "endOffset": 5868, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5964, "endOffset": 6185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6276, "endOffset": 6493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6556, "endOffset": 7596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6635, "endOffset": 6963, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7033, "endOffset": 7302, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7396, "endOffset": 7590, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7659, "endOffset": 9175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7727, "endOffset": 8332, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8394, "endOffset": 8731, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8804, "endOffset": 9169, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9232, "endOffset": 10884, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9299, "endOffset": 9910, "count": 1}, {"startOffset": 9554, "endOffset": 9909, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9977, "endOffset": 10332, "count": 1}, {"startOffset": 10238, "endOffset": 10331, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10399, "endOffset": 10878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10957, "endOffset": 12348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11041, "endOffset": 11387, "count": 1}, {"startOffset": 11291, "endOffset": 11386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11465, "endOffset": 11998, "count": 1}, {"startOffset": 11746, "endOffset": 11997, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12068, "endOffset": 12342, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12412, "endOffset": 13188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12491, "endOffset": 12765, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12864, "endOffset": 13182, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13245, "endOffset": 14127, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13329, "endOffset": 13688, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13762, "endOffset": 14121, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14195, "endOffset": 16225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14289, "endOffset": 14751, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14823, "endOffset": 15290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15370, "endOffset": 15830, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15909, "endOffset": 16219, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16286, "endOffset": 17453, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16364, "endOffset": 16825, "count": 1}, {"startOffset": 16621, "endOffset": 16824, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16893, "endOffset": 17447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17507, "endOffset": 18449, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17584, "endOffset": 18036, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17691, "endOffset": 17706, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18108, "endOffset": 18443, "count": 1}, {"startOffset": 18338, "endOffset": 18442, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18215, "endOffset": 18230, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1469", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 168790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 168790, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1042, "endOffset": 1978, "count": 33}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1999, "endOffset": 2158, "count": 54}, {"startOffset": 2054, "endOffset": 2151, "count": 21}], "isBlockCoverage": true}, {"functionName": "totalOptionsPrice", "ranges": [{"startOffset": 2165, "endOffset": 2983, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2260, "endOffset": 2954, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalPrice", "ranges": [{"startOffset": 2990, "endOffset": 3129, "count": 1}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 3138, "endOffset": 3174, "count": 33}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 3194, "endOffset": 3929, "count": 33}, {"startOffset": 3414, "endOffset": 3419, "count": 0}, {"startOffset": 3633, "endOffset": 3638, "count": 0}, {"startOffset": 3847, "endOffset": 3923, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterProducts", "ranges": [{"startOffset": 3936, "endOffset": 4322, "count": 1}, {"startOffset": 3984, "endOffset": 4061, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4166, "endOffset": 4307, "count": 2}, {"startOffset": 4228, "endOffset": 4307, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 4329, "endOffset": 4492, "count": 171}, {"startOffset": 4452, "endOffset": 4467, "count": 105}, {"startOffset": 4468, "endOffset": 4485, "count": 66}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4403, "endOffset": 4427, "count": 157}], "isBlockCoverage": true}, {"functionName": "selectProductById", "ranges": [{"startOffset": 4499, "endOffset": 4668, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4571, "endOffset": 4594, "count": 33}], "isBlockCoverage": true}, {"functionName": "selectProduct", "ranges": [{"startOffset": 4675, "endOffset": 5762, "count": 35}, {"startOffset": 5089, "endOffset": 5094, "count": 0}, {"startOffset": 5183, "endOffset": 5348, "count": 0}, {"startOffset": 5443, "endOffset": 5756, "count": 0}], "isBlockCoverage": true}, {"functionName": "addOptionGroup", "ranges": [{"startOffset": 5769, "endOffset": 5986, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeOptionGroup", "ranges": [{"startOffset": 5993, "endOffset": 6583, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6290, "endOffset": 6525, "count": 0}], "isBlockCoverage": false}, {"functionName": "addOption", "ranges": [{"startOffset": 6590, "endOffset": 6849, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeOption", "ranges": [{"startOffset": 6856, "endOffset": 7261, "count": 1}, {"startOffset": 7086, "endOffset": 7255, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectOption", "ranges": [{"startOffset": 7268, "endOffset": 9495, "count": 2}, {"startOffset": 7408, "endOffset": 8249, "count": 0}, {"startOffset": 8395, "endOffset": 8424, "count": 0}, {"startOffset": 8594, "endOffset": 9489, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7946, "endOffset": 8029, "count": 0}], "isBlockCoverage": false}, {"functionName": "isOptionSelected", "ranges": [{"startOffset": 9502, "endOffset": 9831, "count": 2}, {"startOffset": 9591, "endOffset": 9604, "count": 0}, {"startOffset": 9665, "endOffset": 9745, "count": 0}], "isBlockCoverage": true}, {"functionName": "isOptionDisabled", "ranges": [{"startOffset": 9920, "endOffset": 10030, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveConfiguration", "ranges": [{"startOffset": 10037, "endOffset": 10943, "count": 4}, {"startOffset": 10347, "endOffset": 10601, "count": 0}, {"startOffset": 10845, "endOffset": 10937, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetConfiguration", "ranges": [{"startOffset": 10950, "endOffset": 11111, "count": 1}], "isBlockCoverage": true}, {"functionName": "addDependency", "ranges": [{"startOffset": 11178, "endOffset": 11787, "count": 1}, {"startOffset": 11720, "endOffset": 11781, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11616, "endOffset": 11717, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeDependency", "ranges": [{"startOffset": 11794, "endOffset": 12037, "count": 0}], "isBlockCoverage": false}, {"functionName": "addIncompatibility", "ranges": [{"startOffset": 12044, "endOffset": 12698, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeIncompatibility", "ranges": [{"startOffset": 12705, "endOffset": 12968, "count": 0}], "isBlockCoverage": false}, {"functionName": "canSelectOption", "ranges": [{"startOffset": 13061, "endOffset": 14083, "count": 2}, {"startOffset": 13280, "endOffset": 14057, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13543, "endOffset": 13624, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13904, "endOffset": 14011, "count": 0}], "isBlockCoverage": false}, {"functionName": "areDependenciesSatisfied", "ranges": [{"startOffset": 14163, "endOffset": 14659, "count": 0}], "isBlockCoverage": false}, {"functionName": "addConfigRule", "ranges": [{"startOffset": 14698, "endOffset": 14998, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeConfigRule", "ranges": [{"startOffset": 15040, "endOffset": 15120, "count": 0}], "isBlockCoverage": false}, {"functionName": "showAddDependencyModal", "ranges": [{"startOffset": 15184, "endOffset": 15445, "count": 0}], "isBlockCoverage": false}, {"functionName": "hideAddDependencyModal", "ranges": [{"startOffset": 15452, "endOffset": 15524, "count": 0}], "isBlockCoverage": false}, {"functionName": "confirmAddDependency", "ranges": [{"startOffset": 15531, "endOffset": 15972, "count": 0}], "isBlockCoverage": false}, {"functionName": "showAddIncompatibilityModal", "ranges": [{"startOffset": 15979, "endOffset": 16250, "count": 0}], "isBlockCoverage": false}, {"functionName": "hideAddIncompatibilityModal", "ranges": [{"startOffset": 16257, "endOffset": 16339, "count": 0}], "isBlockCoverage": false}, {"functionName": "confirmAddIncompatibility", "ranges": [{"startOffset": 16346, "endOffset": 16802, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDependencyLabel", "ranges": [{"startOffset": 16875, "endOffset": 17231, "count": 0}], "isBlockCoverage": false}, {"functionName": "getIncompatibilityLabel", "ranges": [{"startOffset": 17238, "endOffset": 17624, "count": 0}], "isBlockCoverage": false}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 22669, "endOffset": 63321, "count": 119}, {"startOffset": 23018, "endOffset": 23385, "count": 33}, {"startOffset": 23587, "endOffset": 23684, "count": 33}, {"startOffset": 23753, "endOffset": 23991, "count": 33}, {"startOffset": 24151, "endOffset": 24250, "count": 33}, {"startOffset": 24319, "endOffset": 24561, "count": 33}, {"startOffset": 24844, "endOffset": 24975, "count": 33}, {"startOffset": 25284, "endOffset": 25341, "count": 33}, {"startOffset": 25378, "endOffset": 25469, "count": 33}, {"startOffset": 25647, "endOffset": 25767, "count": 33}, {"startOffset": 27802, "endOffset": 44548, "count": 51}, {"startOffset": 27930, "endOffset": 28065, "count": 32}, {"startOffset": 44113, "endOffset": 44204, "count": 24}, {"startOffset": 44237, "endOffset": 44510, "count": 24}, {"startOffset": 44561, "endOffset": 44617, "count": 68}, {"startOffset": 44748, "endOffset": 45136, "count": 68}, {"startOffset": 44858, "endOffset": 45134, "count": 33}, {"startOffset": 45149, "endOffset": 53046, "count": 40}, {"startOffset": 45518, "endOffset": 45657, "count": 24}, {"startOffset": 46242, "endOffset": 46644, "count": 0}, {"startOffset": 46838, "endOffset": 47291, "count": 16}, {"startOffset": 47314, "endOffset": 52988, "count": 1}, {"startOffset": 51734, "endOffset": 51774, "count": 0}, {"startOffset": 51923, "endOffset": 52396, "count": 0}, {"startOffset": 53181, "endOffset": 58087, "count": 0}, {"startOffset": 58094, "endOffset": 58150, "count": 85}, {"startOffset": 58268, "endOffset": 63224, "count": 0}, {"startOffset": 63231, "endOffset": 63287, "count": 85}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 23603, "endOffset": 23683, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 24167, "endOffset": 24249, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 25300, "endOffset": 25340, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onInput._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 25394, "endOffset": 25468, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 26049, "endOffset": 27697, "count": 170}, {"startOffset": 26876, "endOffset": 27174, "count": 0}], "isBlockCoverage": true}, {"functionName": "onClick", "ranges": [{"startOffset": 26368, "endOffset": 26411, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 28350, "endOffset": 43925, "count": 12}, {"startOffset": 43540, "endOffset": 43825, "count": 1}], "isBlockCoverage": true}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 28976, "endOffset": 29009, "count": 0}], "isBlockCoverage": false}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 29679, "endOffset": 29716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 29956, "endOffset": 30360, "count": 48}], "isBlockCoverage": true}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 31002, "endOffset": 31039, "count": 0}], "isBlockCoverage": false}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 31744, "endOffset": 31784, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 32497, "endOffset": 32547, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 33132, "endOffset": 43257, "count": 11}, {"startOffset": 37881, "endOffset": 39477, "count": 0}, {"startOffset": 39512, "endOffset": 39632, "count": 0}, {"startOffset": 39900, "endOffset": 40011, "count": 0}, {"startOffset": 40345, "endOffset": 40538, "count": 0}, {"startOffset": 40710, "endOffset": 42341, "count": 0}, {"startOffset": 42376, "endOffset": 42501, "count": 0}, {"startOffset": 42953, "endOffset": 43139, "count": 0}], "isBlockCoverage": true}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 33814, "endOffset": 33848, "count": 0}], "isBlockCoverage": false}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 34657, "endOffset": 34692, "count": 0}], "isBlockCoverage": false}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 35677, "endOffset": 35710, "count": 0}], "isBlockCoverage": false}, {"functionName": "onUpdate:modelValue", "ranges": [{"startOffset": 36478, "endOffset": 36519, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 37396, "endOffset": 37464, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 38193, "endOffset": 39408, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 40218, "endOffset": 40291, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 41027, "endOffset": 42272, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 42845, "endOffset": 42903, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 43454, "endOffset": 43496, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 44129, "endOffset": 44203, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 47627, "endOffset": 51218, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 48381, "endOffset": 51096, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 53726, "endOffset": 53816, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 54854, "endOffset": 54901, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 55132, "endOffset": 55519, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 56212, "endOffset": 56260, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 56533, "endOffset": 56947, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 57511, "endOffset": 57601, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 57796, "endOffset": 57882, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 58820, "endOffset": 58920, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 59955, "endOffset": 60002, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 60233, "endOffset": 60621, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 61317, "endOffset": 61365, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 61638, "endOffset": 62053, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 62620, "endOffset": 62720, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 62917, "endOffset": 63013, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1470", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 570, "count": 1}], "isBlockCoverage": true}]}]}