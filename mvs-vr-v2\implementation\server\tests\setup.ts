/**
 * Vitest setup file
 *
 * This file is executed before each test file.
 */

import { vi } from 'vitest';
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.test' });

// Set up environment variables for testing if not already set
process.env.NODE_ENV = process.env.NODE_ENV || 'test';
process.env.NEXT_PUBLIC_SUPABASE_URL =
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://test-supabase-url.com';
process.env.SUPABASE_SERVICE_ROLE_KEY =
  process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-role-key';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'test-jwt-secret';
process.env.API_KEY_SECRET = process.env.API_KEY_SECRET || 'test-api-key-secret';

// Mock Redis
vi.mock('ioredis', () => {
  const redisMock = {
    get: vi.fn(),
    set: vi.fn(),
    del: vi.fn(),
    incr: vi.fn(),
    decr: vi.fn(),
    expire: vi.fn(),
    hincrby: vi.fn(),
    hset: vi.fn(),
    hget: vi.fn(),
    hgetall: vi.fn(),
    lpush: vi.fn(),
    ltrim: vi.fn(),
    keys: vi.fn(),
    exists: vi.fn(),
    sendCommand: vi.fn(),
    call: vi.fn(),
    on: vi.fn(),
    connect: vi.fn(),
    quit: vi.fn(),
  };

  return {
    default: vi.fn(() => redisMock),
  };
});

// Mock Supabase client
vi.mock('@supabase/supabase-js', () => {
  const mockSupabase = {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    neq: vi.fn().mockReturnThis(),
    gt: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lt: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    contains: vi.fn().mockReturnThis(),
    auth: {
      signInWithPassword: vi.fn(),
      signOut: vi.fn(),
      getSession: vi.fn(),
      getUser: vi.fn(),
      refreshSession: vi.fn(),
      admin: {
        createUser: vi.fn(),
        deleteUser: vi.fn(),
        updateUser: vi.fn(),
      },
    },
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn(),
        download: vi.fn(),
        remove: vi.fn(),
        list: vi.fn(),
        getPublicUrl: vi.fn(),
      }),
    },
  };

  return {
    createClient: vi.fn().mockReturnValue(mockSupabase),
  };
});

// Mock winston logger
vi.mock('winston', () => ({
  default: {
    createLogger: vi.fn(() => ({
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    })),
    format: {
      combine: vi.fn(() => vi.fn()),
      timestamp: vi.fn(() => vi.fn()),
      errors: vi.fn(() => vi.fn()),
      json: vi.fn(() => vi.fn()),
      colorize: vi.fn(() => vi.fn()),
      simple: vi.fn(() => vi.fn()),
    },
    transports: {
      Console: vi.fn(),
      File: vi.fn(),
    },
  },
}));

// Mock jsonwebtoken
vi.mock('jsonwebtoken', () => ({
  default: {
    verify: vi.fn(),
    sign: vi.fn(),
  },
}));

// Mock node-jose
vi.mock('node-jose', () => ({
  default: {
    JWK: {
      createKeyStore: vi.fn(() => ({
        generate: vi.fn(),
        get: vi.fn(),
        toJSON: vi.fn(() => ({})),
      })),
      asKeyStore: vi.fn(),
    },
    JWE: {
      createEncrypt: vi.fn(() => ({
        update: vi.fn().mockReturnThis(),
        encrypt: vi.fn(),
      })),
      createDecrypt: vi.fn(() => ({
        decrypt: vi.fn(() => ({
          plaintext: { toString: vi.fn(() => 'decrypted-token') },
        })),
      })),
    },
  },
}));

// Mock rate-limit-redis
vi.mock('rate-limit-redis', () => ({
  default: vi.fn().mockImplementation(() => ({
    increment: vi.fn(),
    decrement: vi.fn(),
    resetKey: vi.fn(),
  })),
}));

// Mock express-rate-limit
vi.mock('express-rate-limit', () => ({
  default: vi.fn(() => vi.fn()),
}));
