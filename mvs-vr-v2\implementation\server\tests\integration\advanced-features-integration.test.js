/**
 * Advanced Features Integration Tests
 * Tests the integration of all advanced features
 */

import { describe, it, expect, beforeAll, afterAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { createServer } from '../../server.ts';
import CSRFProtection from '../../middleware/security/csrf-protection.js';
import SecurityHeaders from '../../middleware/security/security-headers.js';
import { ServiceMesh } from '../../services/mesh/service-mesh.js';
import { DashboardManager } from '../../services/dashboard/dashboard-framework.js';

describe('Advanced Features Integration', () => {
  let app;
  let server;
  let serviceMesh;
  let dashboardManager;

  beforeAll(async () => {
    app = await createServer();
    server = app.listen(0);
    serviceMesh = new ServiceMesh();
    dashboardManager = new DashboardManager();
  });

  afterAll(async () => {
    if (server) {
      server.close();
    }
  });

  describe('CSRF Protection Integration', () => {
    let csrfProtection;

    beforeEach(() => {
      csrfProtection = new CSRFProtection({
        secure: false, // For testing
      });
    });

    it('should generate CSRF token', async () => {
      const token = await csrfProtection.generateCSRFToken();
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.length).toBeGreaterThan(0);
    });

    it('should verify valid CSRF token', async () => {
      const secret = await csrfProtection.createSecret();
      const token = csrfProtection.generateTokenFromSecret(secret);
      const isValid = csrfProtection.verifyToken(token, secret);
      expect(isValid).toBe(true);
    });

    it('should reject invalid CSRF token', () => {
      const isValid = csrfProtection.verifyToken('invalid-token', 'secret');
      expect(isValid).toBe(false);
    });

    it('should protect POST requests', async () => {
      const middleware = csrfProtection.middleware();
      const req = {
        method: 'POST',
        session: {},
        headers: {},
        body: {},
      };
      const res = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn(),
        cookie: vi.fn(),
        locals: {},
      };

      await new Promise((resolve, reject) => {
        middleware(req, res, error => {
          if (error) reject(error);
          else resolve();
        });
      }).catch(() => {
        expect(res.status).toHaveBeenCalledWith(403);
      });
    });
  });

  describe('Security Headers Integration', () => {
    let securityHeaders;

    beforeEach(() => {
      securityHeaders = new SecurityHeaders();
    });

    it('should set security headers', () => {
      const middleware = securityHeaders.middleware();
      const req = { secure: true };
      const res = {
        setHeader: vi.fn(),
        removeHeader: vi.fn(),
      };

      middleware(req, res, () => {});

      expect(res.setHeader).toHaveBeenCalledWith('Content-Security-Policy', expect.any(String));
      expect(res.setHeader).toHaveBeenCalledWith('Strict-Transport-Security', expect.any(String));
      expect(res.setHeader).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
      expect(res.removeHeader).toHaveBeenCalledWith('X-Powered-By');
    });

    it('should build CSP header correctly', () => {
      const cspHeader = securityHeaders.buildCSPHeader();
      expect(cspHeader).toContain("default-src 'self'");
      expect(cspHeader).toContain("object-src 'none'");
    });

    it('should return security status', () => {
      const status = securityHeaders.getStatus();
      expect(status).toHaveProperty('csp', true);
      expect(status).toHaveProperty('hsts', true);
      expect(status).toHaveProperty('frameOptions', true);
    });
  });

  describe('Service Mesh Integration', () => {
    beforeEach(() => {
      serviceMesh = new ServiceMesh();
    });

    it('should register and discover services', () => {
      const serviceName = 'test-service';
      const instance = {
        host: 'localhost',
        port: 3000,
        version: '1.0.0',
      };

      serviceMesh.registerService(serviceName, instance);
      const discovered = serviceMesh.registry.discover(serviceName);

      expect(discovered).toHaveLength(1);
      expect(discovered[0]).toMatchObject(instance);
    });

    it('should load balance requests', async () => {
      const serviceName = 'load-balance-test';

      // Register multiple instances
      serviceMesh.registerService(serviceName, { host: 'host1', port: 3001 });
      serviceMesh.registerService(serviceName, { host: 'host2', port: 3002 });
      serviceMesh.registerService(serviceName, { host: 'host3', port: 3003 });

      const callCounts = new Map();

      // Make multiple calls
      for (let i = 0; i < 6; i++) {
        await serviceMesh.callService(serviceName, instance => {
          const key = `${instance.host}:${instance.port}`;
          callCounts.set(key, (callCounts.get(key) || 0) + 1);
          return Promise.resolve(`Response from ${key}`);
        });
      }

      // Verify round-robin distribution
      expect(callCounts.size).toBe(3);
      for (const count of callCounts.values()) {
        expect(count).toBe(2);
      }
    });

    it('should handle circuit breaker', async () => {
      const serviceName = 'circuit-breaker-test';
      serviceMesh.registerService(serviceName, { host: 'localhost', port: 3000 });

      // Simulate failures to trigger circuit breaker
      const failingFunction = () => Promise.reject(new Error('Service unavailable'));

      // Make enough calls to trigger circuit breaker
      for (let i = 0; i < 5; i++) {
        try {
          await serviceMesh.callService(serviceName, failingFunction);
        } catch (error) {
          // Expected to fail
        }
      }

      // Circuit breaker should now be open
      const stats = serviceMesh.getServiceStats();
      expect(stats.circuitBreakers[serviceName].state).toBe('OPEN');
    });
  });

  describe('Dashboard Framework Integration', () => {
    beforeEach(() => {
      dashboardManager = new DashboardManager();
    });

    it('should create and manage dashboards', () => {
      const dashboardConfig = {
        id: 'test-dashboard',
        title: 'Test Dashboard',
        layout: [],
      };

      const dashboard = dashboardManager.createDashboard(dashboardConfig);
      expect(dashboard.id).toBe('test-dashboard');
      expect(dashboard.title).toBe('Test Dashboard');

      const retrieved = dashboardManager.getDashboard('test-dashboard');
      expect(retrieved).toBe(dashboard);
    });

    it('should add and manage widgets', async () => {
      const dashboard = dashboardManager.createDashboard({
        id: 'widget-test',
        title: 'Widget Test',
      });

      const widgetConfig = {
        id: 'test-widget',
        type: 'metric',
        title: 'Test Metric',
        dataSource: () => Promise.resolve({ value: 42 }),
        refreshInterval: 0, // Disable auto-refresh for test
      };

      const widget = dashboard.addWidget(widgetConfig);
      expect(widget.id).toBe('test-widget');
      expect(widget.type).toBe('metric');

      await dashboard.refreshWidget('test-widget');
      expect(widget.data).toEqual({ value: 42 });
    });

    it('should handle widget refresh intervals', done => {
      const dashboard = dashboardManager.createDashboard({
        id: 'refresh-test',
        title: 'Refresh Test',
      });

      let callCount = 0;
      const widgetConfig = {
        id: 'refresh-widget',
        type: 'metric',
        title: 'Refresh Metric',
        dataSource: () => {
          callCount++;
          return Promise.resolve({ value: callCount });
        },
        refreshInterval: 100, // 100ms for fast test
      };

      dashboard.addWidget(widgetConfig);
      dashboard.activate();

      // Check after 250ms that it was called multiple times
      setTimeout(() => {
        expect(callCount).toBeGreaterThan(1);
        dashboard.deactivate();
        done();
      }, 250);
    });

    it('should serialize dashboard state', () => {
      const dashboard = dashboardManager.createDashboard({
        id: 'serialize-test',
        title: 'Serialize Test',
      });

      dashboard.addWidget({
        id: 'widget1',
        type: 'chart',
        title: 'Chart Widget',
        dataSource: () => Promise.resolve([]),
      });

      const serialized = dashboard.serialize();
      expect(serialized.id).toBe('serialize-test');
      expect(serialized.widgets).toHaveLength(1);
      expect(serialized.widgets[0].id).toBe('widget1');
    });
  });

  describe('End-to-End Integration', () => {
    it('should integrate all security features', async () => {
      const csrfProtection = new CSRFProtection({ secure: false });
      const securityHeaders = new SecurityHeaders();

      // Test that both middlewares work together
      const req = {
        method: 'GET',
        session: {},
        secure: false,
      };
      const res = {
        setHeader: vi.fn(),
        removeHeader: vi.fn(),
        cookie: vi.fn(),
        locals: {},
      };

      // Apply security headers first
      securityHeaders.middleware()(req, res, () => {});

      // Then CSRF protection
      await new Promise(resolve => {
        csrfProtection.middleware()(req, res, resolve);
      });

      expect(res.setHeader).toHaveBeenCalled();
      expect(res.cookie).toHaveBeenCalled();
    });

    it('should integrate service mesh with dashboard', async () => {
      // Register a service that provides dashboard data
      serviceMesh.registerService('dashboard-data', {
        host: 'localhost',
        port: 3000,
        getData: () => ({ metrics: { cpu: 75, memory: 60 } }),
      });

      // Create dashboard that uses the service
      const dashboard = dashboardManager.createDashboard({
        id: 'service-dashboard',
        title: 'Service Dashboard',
      });

      dashboard.addWidget({
        id: 'service-widget',
        type: 'metric',
        title: 'Service Metrics',
        dataSource: async () => {
          return await serviceMesh.callService('dashboard-data', instance => {
            return Promise.resolve(instance.getData());
          });
        },
      });

      await dashboard.refreshWidget('service-widget');
      const widget = dashboard.widgets.get('service-widget');
      expect(widget.data).toEqual({ metrics: { cpu: 75, memory: 60 } });
    });
  });
});
