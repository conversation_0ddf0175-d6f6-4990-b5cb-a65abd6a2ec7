/**
 * Business Service Monitor
 * 
 * This service provides comprehensive business service monitoring with health indicators,
 * dependency mapping, and business impact calculation for continuity planning.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const EventEmitter = require('events');
const axios = require('axios');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

const logger = require('../../utils/logger').getLogger('business-service-monitor');

// Service health states
const HEALTH_STATES = {
  HEALTHY: 'healthy',
  DEGRADED: 'degraded',
  UNHEALTHY: 'unhealthy',
  CRITICAL: 'critical',
  UNKNOWN: 'unknown'
};

// Business impact levels
const IMPACT_LEVELS = {
  CRITICAL: 5,
  HIGH: 4,
  MEDIUM: 3,
  LOW: 2,
  MINIMAL: 1
};

// Service types
const SERVICE_TYPES = {
  CORE: 'core',
  SUPPORTING: 'supporting',
  EXTERNAL: 'external',
  INFRASTRUCTURE: 'infrastructure'
};

class BusinessServiceMonitor extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      configPath: options.configPath || path.join(__dirname, '../../config/business-services.json'),
      outputPath: options.outputPath || path.join(__dirname, '../../data/service-monitoring'),
      checkInterval: options.checkInterval || 60000, // 1 minute
      healthTimeout: options.healthTimeout || 10000, // 10 seconds
      retryAttempts: options.retryAttempts || 3,
      retryDelay: options.retryDelay || 2000,
      ...options
    };
    
    // Service data
    this.services = new Map();
    this.serviceHealth = new Map();
    this.dependencyGraph = new Map();
    this.businessMetrics = new Map();
    
    // Monitoring state
    this.monitoringInterval = null;
    this.isMonitoring = false;
    
    // Statistics
    this.stats = {
      totalServices: 0,
      healthyServices: 0,
      degradedServices: 0,
      unhealthyServices: 0,
      criticalServices: 0,
      unknownServices: 0,
      totalChecks: 0,
      lastCheck: null,
      startTime: Date.now()
    };
    
    this.initialize();
  }
  
  /**
   * Initialize the service monitor
   */
  async initialize() {
    try {
      // Ensure output directory exists
      if (!await existsAsync(this.options.outputPath)) {
        await mkdirAsync(this.options.outputPath, { recursive: true });
      }
      
      // Load service configuration
      await this.loadServiceConfiguration();
      
      // Build dependency graph
      this.buildDependencyGraph();
      
      logger.info('Business service monitor initialized');
    } catch (error) {
      logger.error('Failed to initialize business service monitor:', error);
      throw error;
    }
  }
  
  /**
   * Load service configuration
   */
  async loadServiceConfiguration() {
    try {
      const configData = await readFileAsync(this.options.configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // Load services
      for (const serviceConfig of config.services || []) {
        const service = {
          id: serviceConfig.id,
          name: serviceConfig.name,
          description: serviceConfig.description,
          type: serviceConfig.type || SERVICE_TYPES.SUPPORTING,
          priority: serviceConfig.priority || 3,
          healthEndpoint: serviceConfig.healthEndpoint,
          dependencies: serviceConfig.dependencies || [],
          businessImpact: serviceConfig.businessImpact || {},
          sla: serviceConfig.sla || {},
          contacts: serviceConfig.contacts || [],
          recoveryProcedures: serviceConfig.recoveryProcedures || []
        };
        
        this.services.set(service.id, service);
        this.serviceHealth.set(service.id, {
          state: HEALTH_STATES.UNKNOWN,
          lastCheck: null,
          responseTime: null,
          errorCount: 0,
          uptime: 0,
          details: {}
        });
      }
      
      this.stats.totalServices = this.services.size;
      logger.info(`Loaded ${this.services.size} services from configuration`);
      
    } catch (error) {
      logger.error('Failed to load service configuration:', error);
      throw error;
    }
  }
  
  /**
   * Build dependency graph
   */
  buildDependencyGraph() {
    this.dependencyGraph.clear();
    
    for (const [serviceId, service] of this.services.entries()) {
      const dependencies = new Set();
      const dependents = new Set();
      
      // Add direct dependencies
      for (const depId of service.dependencies) {
        if (this.services.has(depId)) {
          dependencies.add(depId);
        }
      }
      
      // Find dependents (services that depend on this service)
      for (const [otherId, otherService] of this.services.entries()) {
        if (otherService.dependencies.includes(serviceId)) {
          dependents.add(otherId);
        }
      }
      
      this.dependencyGraph.set(serviceId, {
        dependencies: Array.from(dependencies),
        dependents: Array.from(dependents)
      });
    }
    
    logger.debug('Built dependency graph for services');
  }
  
  /**
   * Start monitoring services
   */
  startMonitoring() {
    if (this.isMonitoring) {
      logger.warn('Service monitoring is already running');
      return;
    }
    
    this.isMonitoring = true;
    
    // Initial health check
    this.checkAllServices();
    
    // Schedule regular checks
    this.monitoringInterval = setInterval(() => {
      this.checkAllServices();
    }, this.options.checkInterval);
    
    logger.info('Started business service monitoring');
  }
  
  /**
   * Stop monitoring services
   */
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    logger.info('Stopped business service monitoring');
  }
  
  /**
   * Check health of all services
   */
  async checkAllServices() {
    const startTime = Date.now();
    const healthChanges = [];
    
    // Reset statistics
    this.stats.healthyServices = 0;
    this.stats.degradedServices = 0;
    this.stats.unhealthyServices = 0;
    this.stats.criticalServices = 0;
    this.stats.unknownServices = 0;
    
    // Check each service
    for (const [serviceId, service] of this.services.entries()) {
      try {
        const previousHealth = this.serviceHealth.get(serviceId);
        const currentHealth = await this.checkServiceHealth(service);
        
        // Update health data
        this.serviceHealth.set(serviceId, currentHealth);
        
        // Update statistics
        this.updateHealthStatistics(currentHealth.state);
        
        // Check for health state changes
        if (previousHealth.state !== currentHealth.state) {
          const change = {
            serviceId,
            serviceName: service.name,
            previousState: previousHealth.state,
            currentState: currentHealth.state,
            timestamp: new Date().toISOString(),
            businessImpact: this.calculateBusinessImpact(service, currentHealth.state)
          };
          
          healthChanges.push(change);
          
          // Emit health change event
          this.emit('healthStateChange', change);
        }
        
      } catch (error) {
        logger.error(`Error checking service ${serviceId}:`, error);
        
        // Mark as unknown on error
        this.serviceHealth.set(serviceId, {
          state: HEALTH_STATES.UNKNOWN,
          lastCheck: new Date().toISOString(),
          responseTime: null,
          errorCount: (this.serviceHealth.get(serviceId)?.errorCount || 0) + 1,
          uptime: 0,
          details: { error: error.message }
        });
        
        this.stats.unknownServices++;
      }
    }
    
    // Update global statistics
    this.stats.totalChecks++;
    this.stats.lastCheck = new Date().toISOString();
    
    const checkDuration = Date.now() - startTime;
    logger.debug(`Completed health check for ${this.services.size} services in ${checkDuration}ms`);
    
    // Process health changes
    if (healthChanges.length > 0) {
      await this.processHealthChanges(healthChanges);
    }
    
    // Update business metrics
    this.updateBusinessMetrics();
    
    // Emit monitoring cycle complete event
    this.emit('monitoringCycleComplete', {
      duration: checkDuration,
      servicesChecked: this.services.size,
      healthChanges: healthChanges.length,
      statistics: this.getStatistics()
    });
  }
  
  /**
   * Check health of a specific service
   * @param {Object} service - Service to check
   * @returns {Promise<Object>} Health data
   */
  async checkServiceHealth(service) {
    const startTime = Date.now();
    
    try {
      if (!service.healthEndpoint) {
        // No health endpoint, assume healthy if it's a configured service
        return {
          state: HEALTH_STATES.HEALTHY,
          lastCheck: new Date().toISOString(),
          responseTime: 0,
          errorCount: 0,
          uptime: 100,
          details: { message: 'No health endpoint configured' }
        };
      }
      
      // Make health check request with timeout
      const response = await axios.get(service.healthEndpoint, {
        timeout: this.options.healthTimeout,
        validateStatus: (status) => status < 500 // Accept 4xx as degraded, not unhealthy
      });
      
      const responseTime = Date.now() - startTime;
      
      // Determine health state based on response
      let healthState;
      if (response.status >= 200 && response.status < 300) {
        healthState = HEALTH_STATES.HEALTHY;
      } else if (response.status >= 300 && response.status < 500) {
        healthState = HEALTH_STATES.DEGRADED;
      } else {
        healthState = HEALTH_STATES.UNHEALTHY;
      }
      
      // Check response data for additional health information
      const healthData = response.data || {};
      if (healthData.status) {
        switch (healthData.status.toLowerCase()) {
          case 'healthy':
          case 'ok':
            healthState = HEALTH_STATES.HEALTHY;
            break;
          case 'degraded':
          case 'warning':
            healthState = HEALTH_STATES.DEGRADED;
            break;
          case 'unhealthy':
          case 'error':
            healthState = HEALTH_STATES.UNHEALTHY;
            break;
          case 'critical':
            healthState = HEALTH_STATES.CRITICAL;
            break;
        }
      }
      
      return {
        state: healthState,
        lastCheck: new Date().toISOString(),
        responseTime,
        errorCount: 0,
        uptime: this.calculateUptime(service.id, healthState),
        details: {
          statusCode: response.status,
          responseData: healthData
        }
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        state: HEALTH_STATES.UNHEALTHY,
        lastCheck: new Date().toISOString(),
        responseTime,
        errorCount: (this.serviceHealth.get(service.id)?.errorCount || 0) + 1,
        uptime: this.calculateUptime(service.id, HEALTH_STATES.UNHEALTHY),
        details: {
          error: error.message,
          errorCode: error.code
        }
      };
    }
  }
  
  /**
   * Calculate service uptime percentage
   * @param {string} serviceId - Service ID
   * @param {string} currentState - Current health state
   * @returns {number} Uptime percentage
   */
  calculateUptime(serviceId, currentState) {
    // Simplified uptime calculation
    // In a real implementation, this would track historical data
    const currentHealth = this.serviceHealth.get(serviceId);
    
    if (!currentHealth) {
      return currentState === HEALTH_STATES.HEALTHY ? 100 : 0;
    }
    
    // Simple calculation based on error count
    const errorCount = currentHealth.errorCount || 0;
    const totalChecks = this.stats.totalChecks || 1;
    
    return Math.max(0, Math.min(100, ((totalChecks - errorCount) / totalChecks) * 100));
  }
  
  /**
   * Calculate business impact of service state
   * @param {Object} service - Service configuration
   * @param {string} healthState - Current health state
   * @returns {Object} Business impact assessment
   */
  calculateBusinessImpact(service, healthState) {
    const impactConfig = service.businessImpact || {};
    let impactScore = 0;
    let impactLevel = 'minimal';
    
    // Base impact based on service priority and type
    const priorityMultiplier = service.priority || 3;
    const typeMultiplier = service.type === SERVICE_TYPES.CORE ? 2 : 1;
    
    // State-specific impact
    switch (healthState) {
      case HEALTH_STATES.CRITICAL:
        impactScore = (impactConfig.critical || 5) * priorityMultiplier * typeMultiplier;
        impactLevel = 'critical';
        break;
      case HEALTH_STATES.UNHEALTHY:
        impactScore = (impactConfig.unhealthy || 4) * priorityMultiplier * typeMultiplier;
        impactLevel = 'high';
        break;
      case HEALTH_STATES.DEGRADED:
        impactScore = (impactConfig.degraded || 2) * priorityMultiplier * typeMultiplier;
        impactLevel = 'medium';
        break;
      case HEALTH_STATES.HEALTHY:
        impactScore = 0;
        impactLevel = 'minimal';
        break;
      default:
        impactScore = 1;
        impactLevel = 'low';
    }
    
    // Calculate dependent services impact
    const dependents = this.dependencyGraph.get(service.id)?.dependents || [];
    const dependentImpact = dependents.length * 0.5; // Each dependent adds 0.5 to impact
    
    return {
      score: Math.min(25, impactScore + dependentImpact), // Cap at 25
      level: impactLevel,
      affectedServices: dependents.length,
      estimatedDowntime: this.estimateDowntime(service, healthState),
      recoveryPriority: this.calculateRecoveryPriority(service, impactScore)
    };
  }
  
  /**
   * Estimate service downtime
   * @param {Object} service - Service configuration
   * @param {string} healthState - Current health state
   * @returns {number} Estimated downtime in minutes
   */
  estimateDowntime(service, healthState) {
    const sla = service.sla || {};
    
    switch (healthState) {
      case HEALTH_STATES.CRITICAL:
        return sla.criticalRecoveryTime || 60; // 1 hour
      case HEALTH_STATES.UNHEALTHY:
        return sla.unhealthyRecoveryTime || 30; // 30 minutes
      case HEALTH_STATES.DEGRADED:
        return sla.degradedRecoveryTime || 15; // 15 minutes
      default:
        return 0;
    }
  }
  
  /**
   * Calculate recovery priority
   * @param {Object} service - Service configuration
   * @param {number} impactScore - Business impact score
   * @returns {number} Recovery priority (1-10, higher is more urgent)
   */
  calculateRecoveryPriority(service, impactScore) {
    const basePriority = service.priority || 3;
    const impactPriority = Math.min(5, Math.ceil(impactScore / 5));
    const typePriority = service.type === SERVICE_TYPES.CORE ? 3 : 1;
    
    return Math.min(10, basePriority + impactPriority + typePriority);
  }
  
  /**
   * Update health statistics
   * @param {string} healthState - Health state to count
   */
  updateHealthStatistics(healthState) {
    switch (healthState) {
      case HEALTH_STATES.HEALTHY:
        this.stats.healthyServices++;
        break;
      case HEALTH_STATES.DEGRADED:
        this.stats.degradedServices++;
        break;
      case HEALTH_STATES.UNHEALTHY:
        this.stats.unhealthyServices++;
        break;
      case HEALTH_STATES.CRITICAL:
        this.stats.criticalServices++;
        break;
      default:
        this.stats.unknownServices++;
    }
  }
  
  /**
   * Process health changes
   * @param {Array<Object>} healthChanges - Array of health changes
   */
  async processHealthChanges(healthChanges) {
    try {
      // Save health changes to file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `health-changes-${timestamp}.json`;
      const filePath = path.join(this.options.outputPath, fileName);
      
      await writeFileAsync(filePath, JSON.stringify({
        timestamp: new Date().toISOString(),
        changes: healthChanges,
        summary: {
          totalChanges: healthChanges.length,
          criticalChanges: healthChanges.filter(c => c.currentState === HEALTH_STATES.CRITICAL).length,
          unhealthyChanges: healthChanges.filter(c => c.currentState === HEALTH_STATES.UNHEALTHY).length,
          recoveredServices: healthChanges.filter(c => c.currentState === HEALTH_STATES.HEALTHY).length
        }
      }, null, 2));
      
      logger.info(`Saved ${healthChanges.length} health changes to ${fileName}`);
      
    } catch (error) {
      logger.error('Failed to save health changes:', error);
    }
  }
  
  /**
   * Update business metrics
   */
  updateBusinessMetrics() {
    const timestamp = new Date().toISOString();
    
    // Overall system health score (0-100)
    const totalServices = this.stats.totalServices || 1;
    const healthScore = (
      (this.stats.healthyServices * 100) +
      (this.stats.degradedServices * 75) +
      (this.stats.unhealthyServices * 25) +
      (this.stats.criticalServices * 0) +
      (this.stats.unknownServices * 50)
    ) / totalServices;
    
    // Service availability percentage
    const availability = (this.stats.healthyServices / totalServices) * 100;
    
    // Critical services count
    const criticalServicesDown = this.stats.criticalServices;
    
    // Update metrics
    this.businessMetrics.set('system_health_score', {
      value: Math.round(healthScore),
      timestamp,
      unit: 'percentage'
    });
    
    this.businessMetrics.set('service_availability', {
      value: Math.round(availability * 100) / 100,
      timestamp,
      unit: 'percentage'
    });
    
    this.businessMetrics.set('critical_services_down', {
      value: criticalServicesDown,
      timestamp,
      unit: 'count'
    });
    
    this.businessMetrics.set('total_services_monitored', {
      value: totalServices,
      timestamp,
      unit: 'count'
    });
  }
  
  /**
   * Get service health data
   * @param {string} serviceId - Service ID (optional)
   * @returns {Object} Health data
   */
  getServiceHealth(serviceId = null) {
    if (serviceId) {
      const service = this.services.get(serviceId);
      const health = this.serviceHealth.get(serviceId);
      
      if (!service || !health) {
        return null;
      }
      
      return {
        service,
        health,
        dependencies: this.dependencyGraph.get(serviceId)
      };
    }
    
    // Return all services health
    const allHealth = {};
    for (const [id, service] of this.services.entries()) {
      const health = this.serviceHealth.get(id);
      allHealth[id] = {
        service,
        health,
        dependencies: this.dependencyGraph.get(id)
      };
    }
    
    return allHealth;
  }
  
  /**
   * Get dependency graph
   * @returns {Object} Dependency graph
   */
  getDependencyGraph() {
    const graph = {
      nodes: [],
      edges: []
    };
    
    // Add nodes
    for (const [serviceId, service] of this.services.entries()) {
      const health = this.serviceHealth.get(serviceId);
      graph.nodes.push({
        id: serviceId,
        name: service.name,
        type: service.type,
        priority: service.priority,
        healthState: health.state,
        uptime: health.uptime
      });
    }
    
    // Add edges
    for (const [serviceId, deps] of this.dependencyGraph.entries()) {
      for (const depId of deps.dependencies) {
        graph.edges.push({
          from: serviceId,
          to: depId,
          type: 'depends_on'
        });
      }
    }
    
    return graph;
  }
  
  /**
   * Get business metrics
   * @returns {Object} Business metrics
   */
  getBusinessMetrics() {
    const metrics = {};
    for (const [name, data] of this.businessMetrics.entries()) {
      metrics[name] = data;
    }
    return metrics;
  }
  
  /**
   * Get monitoring statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      isMonitoring: this.isMonitoring
    };
  }
  
  /**
   * Shutdown the monitor
   */
  shutdown() {
    this.stopMonitoring();
    logger.info('Business service monitor shutdown');
  }
}

module.exports = {
  BusinessServiceMonitor,
  HEALTH_STATES,
  IMPACT_LEVELS,
  SERVICE_TYPES
};
