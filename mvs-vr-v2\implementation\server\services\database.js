/**
 * Database Service
 * 
 * This service manages database connections and operations for the MVS-VR platform.
 * It provides connection pooling, health checks, and query utilities.
 */

const { Pool } = require('pg');
const configService = require('./config');

let pool = null;
let isConnected = false;
let lastHealthCheck = null;
const HEALTH_CHECK_INTERVAL = 30 * 1000; // 30 seconds

/**
 * Initialize database connection pool
 * @returns {Promise<void>}
 */
async function initialize() {
  try {
    const dbConfig = await configService.getConfig('database');
    
    pool = new Pool({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.username,
      password: dbConfig.password,
      ssl: dbConfig.ssl,
      min: dbConfig.pool?.min || 2,
      max: dbConfig.pool?.max || 10,
      idleTimeoutMillis: dbConfig.pool?.idle || 10000,
      connectionTimeoutMillis: 5000,
      statement_timeout: 30000,
      query_timeout: 30000
    });
    
    // Test connection
    await ping();
    isConnected = true;
    
    console.log('Database connection pool initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database connection pool:', error);
    throw error;
  }
}

/**
 * Get database connection pool
 * @returns {Pool} Database connection pool
 */
function getPool() {
  if (!pool) {
    throw new Error('Database not initialized. Call initialize() first.');
  }
  return pool;
}

/**
 * Execute a query
 * @param {string} text - SQL query text
 * @param {Array} params - Query parameters
 * @returns {Promise<Object>} Query result
 */
async function query(text, params = []) {
  const client = await getPool().connect();
  try {
    const start = Date.now();
    const result = await client.query(text, params);
    const duration = Date.now() - start;
    
    // Log slow queries
    if (duration > 1000) {
      console.warn(`Slow query detected (${duration}ms):`, text);
    }
    
    return result;
  } finally {
    client.release();
  }
}

/**
 * Execute a transaction
 * @param {Function} callback - Transaction callback function
 * @returns {Promise<any>} Transaction result
 */
async function transaction(callback) {
  const client = await getPool().connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Ping database to check connectivity
 * @returns {Promise<boolean>} Connection status
 */
async function ping() {
  try {
    const result = await query('SELECT 1 as ping');
    const isHealthy = result.rows.length > 0 && result.rows[0].ping === 1;
    lastHealthCheck = Date.now();
    return isHealthy;
  } catch (error) {
    console.error('Database ping failed:', error);
    isConnected = false;
    return false;
  }
}

/**
 * Get database health status
 * @returns {Promise<Object>} Health status
 */
async function getHealthStatus() {
  try {
    const isHealthy = await ping();
    const poolInfo = pool ? {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount
    } : null;
    
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      connected: isConnected,
      timestamp: new Date().toISOString(),
      lastHealthCheck: lastHealthCheck ? new Date(lastHealthCheck).toISOString() : null,
      pool: poolInfo
    };
  } catch (error) {
    return {
      status: 'error',
      connected: false,
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

/**
 * Get database statistics
 * @returns {Promise<Object>} Database statistics
 */
async function getStatistics() {
  try {
    const queries = [
      {
        name: 'database_size',
        query: `SELECT pg_size_pretty(pg_database_size(current_database())) as size`
      },
      {
        name: 'table_count',
        query: `SELECT count(*) as count FROM information_schema.tables WHERE table_schema = 'public'`
      },
      {
        name: 'connection_count',
        query: `SELECT count(*) as count FROM pg_stat_activity WHERE datname = current_database()`
      },
      {
        name: 'active_queries',
        query: `SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active' AND datname = current_database()`
      }
    ];
    
    const stats = {};
    for (const { name, query: queryText } of queries) {
      try {
        const result = await query(queryText);
        stats[name] = result.rows[0];
      } catch (error) {
        stats[name] = { error: error.message };
      }
    }
    
    return {
      timestamp: new Date().toISOString(),
      statistics: stats
    };
  } catch (error) {
    return {
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

/**
 * Close database connections
 * @returns {Promise<void>}
 */
async function close() {
  if (pool) {
    await pool.end();
    pool = null;
    isConnected = false;
    console.log('Database connection pool closed');
  }
}

/**
 * Check if database is initialized
 * @returns {boolean} Initialization status
 */
function isInitialized() {
  return !!pool;
}

/**
 * Check if database is connected
 * @returns {boolean} Connection status
 */
function isConnectedStatus() {
  return isConnected;
}

/**
 * Execute multiple queries in sequence
 * @param {Array} queries - Array of query objects with text and params
 * @returns {Promise<Array>} Array of query results
 */
async function batchQuery(queries) {
  const results = [];
  for (const { text, params = [] } of queries) {
    const result = await query(text, params);
    results.push(result);
  }
  return results;
}

/**
 * Get table information
 * @param {string} tableName - Table name
 * @returns {Promise<Object>} Table information
 */
async function getTableInfo(tableName) {
  const queries = [
    {
      name: 'columns',
      query: `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = $1 AND table_schema = 'public'
        ORDER BY ordinal_position
      `,
      params: [tableName]
    },
    {
      name: 'row_count',
      query: `SELECT count(*) as count FROM ${tableName}`,
      params: []
    },
    {
      name: 'size',
      query: `SELECT pg_size_pretty(pg_total_relation_size($1)) as size`,
      params: [tableName]
    }
  ];
  
  const info = {};
  for (const { name, query: queryText, params } of queries) {
    try {
      const result = await query(queryText, params);
      info[name] = name === 'columns' ? result.rows : result.rows[0];
    } catch (error) {
      info[name] = { error: error.message };
    }
  }
  
  return info;
}

module.exports = {
  initialize,
  getPool,
  query,
  transaction,
  ping,
  getHealthStatus,
  getStatistics,
  close,
  isInitialized,
  isConnected: isConnectedStatus,
  batchQuery,
  getTableInfo
};
