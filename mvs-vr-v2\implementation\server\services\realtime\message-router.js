/**
 * Message Router Service
 * Handles intelligent message routing, filtering, and transformation
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';

export class MessageRouter extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enableRouting: options.enableRouting || true,
      enableFiltering: options.enableFiltering || true,
      enableTransformation: options.enableTransformation || true,
      enablePrioritization: options.enablePrioritization || true,
      enableRateLimiting: options.enableRateLimiting || true,
      defaultPriority: options.defaultPriority || 5,
      maxQueueSize: options.maxQueueSize || 10000,
      processingInterval: options.processingInterval || 10,
      enableMetrics: options.enableMetrics || true,
      ...options
    };

    // Routing configuration
    this.routes = new Map(); // route pattern -> route config
    this.filters = new Map(); // filter name -> filter function
    this.transformers = new Map(); // transformer name -> transformer function
    this.middlewares = []; // array of middleware functions
    
    // Message queues by priority
    this.messageQueues = new Map(); // priority -> array of messages
    this.processingQueue = [];
    
    // Rate limiting
    this.rateLimiters = new Map(); // source -> rate limiter state
    
    // Metrics
    this.metrics = {
      messagesRouted: 0,
      messagesFiltered: 0,
      messagesTransformed: 0,
      messagesDropped: 0,
      averageProcessingTime: 0,
      queueSize: 0,
      errorCount: 0,
      lastReset: Date.now()
    };

    this.initialize();
  }

  initialize() {
    console.log('🧭 Initializing Message Router');
    
    // Initialize priority queues
    for (let priority = 1; priority <= 10; priority++) {
      this.messageQueues.set(priority, []);
    }
    
    // Start message processing
    this.startMessageProcessing();
    
    if (this.options.enableMetrics) {
      this.startMetricsCollection();
    }
    
    console.log('✅ Message Router initialized');
    this.emit('ready');
  }

  /**
   * Add a routing rule
   * @param {string} pattern - Route pattern (supports wildcards)
   * @param {Object} config - Route configuration
   */
  addRoute(pattern, config) {
    const route = {
      pattern,
      target: config.target,
      priority: config.priority || this.options.defaultPriority,
      filters: config.filters || [],
      transformers: config.transformers || [],
      rateLimit: config.rateLimit || null,
      metadata: config.metadata || {},
      createdAt: Date.now(),
      ...config
    };

    this.routes.set(pattern, route);
    console.log(`🛤️ Added route: ${pattern} -> ${route.target}`);
    this.emit('routeAdded', { pattern, route });
  }

  /**
   * Remove a routing rule
   * @param {string} pattern - Route pattern to remove
   */
  removeRoute(pattern) {
    const removed = this.routes.delete(pattern);
    if (removed) {
      console.log(`🗑️ Removed route: ${pattern}`);
      this.emit('routeRemoved', { pattern });
    }
    return removed;
  }

  /**
   * Add a message filter
   * @param {string} name - Filter name
   * @param {Function} filterFunction - Filter function
   */
  addFilter(name, filterFunction) {
    this.filters.set(name, filterFunction);
    console.log(`🔍 Added filter: ${name}`);
  }

  /**
   * Add a message transformer
   * @param {string} name - Transformer name
   * @param {Function} transformFunction - Transform function
   */
  addTransformer(name, transformFunction) {
    this.transformers.set(name, transformFunction);
    console.log(`🔄 Added transformer: ${name}`);
  }

  /**
   * Add middleware
   * @param {Function} middleware - Middleware function
   */
  addMiddleware(middleware) {
    this.middlewares.push(middleware);
    console.log(`⚙️ Added middleware`);
  }

  /**
   * Route a message through the system
   * @param {Object} message - Message to route
   * @param {Object} context - Routing context
   */
  async routeMessage(message, context = {}) {
    const startTime = performance.now();
    
    try {
      // Create routing context
      const routingContext = {
        ...context,
        messageId: message.id || this.generateMessageId(),
        timestamp: Date.now(),
        source: context.source || 'unknown',
        originalMessage: { ...message }
      };

      // Apply middleware
      let processedMessage = message;
      for (const middleware of this.middlewares) {
        processedMessage = await middleware(processedMessage, routingContext);
        if (!processedMessage) {
          this.metrics.messagesDropped++;
          return null; // Message was dropped by middleware
        }
      }

      // Find matching routes
      const matchingRoutes = this.findMatchingRoutes(processedMessage, routingContext);
      
      if (matchingRoutes.length === 0) {
        console.warn(`⚠️ No routes found for message: ${routingContext.messageId}`);
        this.emit('noRouteFound', { message: processedMessage, context: routingContext });
        return null;
      }

      // Process each matching route
      const routingResults = [];
      for (const route of matchingRoutes) {
        const result = await this.processRoute(processedMessage, route, routingContext);
        if (result) {
          routingResults.push(result);
        }
      }

      this.metrics.messagesRouted++;
      
      const processingTime = performance.now() - startTime;
      this.updateProcessingTimeMetrics(processingTime);

      this.emit('messageRouted', { 
        message: processedMessage, 
        context: routingContext, 
        results: routingResults,
        processingTime 
      });

      return routingResults;
    } catch (error) {
      console.error('Message routing error:', error);
      this.metrics.errorCount++;
      this.emit('routingError', { message, context, error });
      throw error;
    }
  }

  /**
   * Find routes that match the message
   * @param {Object} message - Message to match
   * @param {Object} context - Routing context
   */
  findMatchingRoutes(message, context) {
    const matchingRoutes = [];
    
    for (const [pattern, route] of this.routes) {
      if (this.matchesPattern(message, pattern, context)) {
        matchingRoutes.push(route);
      }
    }

    // Sort by priority (higher priority first)
    return matchingRoutes.sort((a, b) => b.priority - a.priority);
  }

  /**
   * Check if message matches route pattern
   * @param {Object} message - Message to check
   * @param {string} pattern - Route pattern
   * @param {Object} context - Routing context
   */
  matchesPattern(message, pattern, context) {
    // Simple pattern matching - can be extended for more complex patterns
    if (pattern === '*') return true;
    
    // Check message type
    if (pattern.startsWith('type:')) {
      const expectedType = pattern.substring(5);
      return message.type === expectedType;
    }
    
    // Check channel
    if (pattern.startsWith('channel:')) {
      const expectedChannel = pattern.substring(8);
      return message.channel === expectedChannel || this.matchesWildcard(message.channel, expectedChannel);
    }
    
    // Check source
    if (pattern.startsWith('source:')) {
      const expectedSource = pattern.substring(7);
      return context.source === expectedSource;
    }
    
    // Check custom pattern
    if (pattern.startsWith('custom:')) {
      const customPattern = pattern.substring(7);
      return this.matchesCustomPattern(message, customPattern, context);
    }
    
    // Default: check if pattern matches message type or channel
    return message.type === pattern || message.channel === pattern;
  }

  matchesWildcard(value, pattern) {
    if (!value || !pattern) return false;
    
    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
    return regex.test(value);
  }

  matchesCustomPattern(message, pattern, context) {
    // Implement custom pattern matching logic here
    // This could involve complex rules, regex, or even external rule engines
    return false;
  }

  /**
   * Process a message through a specific route
   * @param {Object} message - Message to process
   * @param {Object} route - Route configuration
   * @param {Object} context - Routing context
   */
  async processRoute(message, route, context) {
    try {
      // Check rate limiting
      if (route.rateLimit && !this.checkRateLimit(context.source, route.rateLimit)) {
        console.warn(`⚠️ Rate limit exceeded for route ${route.pattern} from ${context.source}`);
        this.metrics.messagesDropped++;
        return null;
      }

      let processedMessage = { ...message };

      // Apply filters
      if (this.options.enableFiltering && route.filters.length > 0) {
        for (const filterName of route.filters) {
          const filter = this.filters.get(filterName);
          if (filter) {
            const passed = await filter(processedMessage, context);
            if (!passed) {
              this.metrics.messagesFiltered++;
              return null; // Message filtered out
            }
          }
        }
      }

      // Apply transformers
      if (this.options.enableTransformation && route.transformers.length > 0) {
        for (const transformerName of route.transformers) {
          const transformer = this.transformers.get(transformerName);
          if (transformer) {
            processedMessage = await transformer(processedMessage, context);
            this.metrics.messagesTransformed++;
          }
        }
      }

      // Queue message for delivery
      const queuedMessage = {
        message: processedMessage,
        route,
        context,
        priority: route.priority,
        queuedAt: Date.now()
      };

      this.queueMessage(queuedMessage);

      return {
        route: route.pattern,
        target: route.target,
        priority: route.priority,
        messageId: context.messageId
      };
    } catch (error) {
      console.error(`Route processing error for ${route.pattern}:`, error);
      this.metrics.errorCount++;
      throw error;
    }
  }

  /**
   * Queue message for processing
   * @param {Object} queuedMessage - Message with routing info
   */
  queueMessage(queuedMessage) {
    const priority = Math.max(1, Math.min(10, queuedMessage.priority));
    const queue = this.messageQueues.get(priority);
    
    if (queue.length >= this.options.maxQueueSize) {
      console.warn(`⚠️ Queue full for priority ${priority}, dropping message`);
      this.metrics.messagesDropped++;
      return false;
    }

    queue.push(queuedMessage);
    this.metrics.queueSize++;
    
    return true;
  }

  /**
   * Start message processing loop
   */
  startMessageProcessing() {
    setInterval(() => {
      this.processMessageQueues();
    }, this.options.processingInterval);
  }

  /**
   * Process messages from priority queues
   */
  async processMessageQueues() {
    // Process queues in priority order (10 = highest priority)
    for (let priority = 10; priority >= 1; priority--) {
      const queue = this.messageQueues.get(priority);
      
      if (queue.length > 0) {
        const message = queue.shift();
        this.metrics.queueSize--;
        
        try {
          await this.deliverMessage(message);
        } catch (error) {
          console.error('Message delivery error:', error);
          this.metrics.errorCount++;
        }
      }
    }
  }

  /**
   * Deliver message to its target
   * @param {Object} queuedMessage - Queued message with routing info
   */
  async deliverMessage(queuedMessage) {
    const { message, route, context } = queuedMessage;
    
    try {
      // Emit event for message delivery
      this.emit('messageDelivery', {
        message,
        target: route.target,
        route: route.pattern,
        context,
        queueTime: Date.now() - queuedMessage.queuedAt
      });

      // In a real implementation, this would deliver to the actual target
      // For now, we'll just emit an event
      console.log(`📤 Delivering message ${context.messageId} to ${route.target}`);
      
    } catch (error) {
      console.error(`Delivery failed for message ${context.messageId}:`, error);
      this.emit('deliveryError', { message, route, context, error });
      throw error;
    }
  }

  /**
   * Check rate limiting for a source
   * @param {string} source - Message source
   * @param {Object} rateLimit - Rate limit configuration
   */
  checkRateLimit(source, rateLimit) {
    const now = Date.now();
    const windowMs = rateLimit.windowMs || 60000;
    const maxRequests = rateLimit.maxRequests || 100;
    
    if (!this.rateLimiters.has(source)) {
      this.rateLimiters.set(source, {
        count: 1,
        resetTime: now + windowMs
      });
      return true;
    }

    const limiter = this.rateLimiters.get(source);
    
    if (now > limiter.resetTime) {
      limiter.count = 1;
      limiter.resetTime = now + windowMs;
      return true;
    }

    if (limiter.count >= maxRequests) {
      return false;
    }

    limiter.count++;
    return true;
  }

  /**
   * Get routing statistics
   */
  getRoutingStats() {
    const routeStats = Array.from(this.routes.entries()).map(([pattern, route]) => ({
      pattern,
      target: route.target,
      priority: route.priority,
      filters: route.filters.length,
      transformers: route.transformers.length,
      createdAt: route.createdAt
    }));

    const queueStats = Array.from(this.messageQueues.entries()).map(([priority, queue]) => ({
      priority,
      size: queue.length
    }));

    return {
      routes: routeStats,
      queues: queueStats,
      filters: Array.from(this.filters.keys()),
      transformers: Array.from(this.transformers.keys()),
      middlewares: this.middlewares.length,
      rateLimiters: this.rateLimiters.size
    };
  }

  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  startMetricsCollection() {
    setInterval(() => {
      this.updateMetrics();
    }, 5000);
  }

  updateMetrics() {
    const now = Date.now();
    const timeDiff = now - this.metrics.lastReset;
    
    // Calculate rates
    this.metrics.routingRate = this.metrics.messagesRouted / (timeDiff / 1000);
    this.metrics.filterRate = this.metrics.messagesFiltered / (timeDiff / 1000);
    this.metrics.transformRate = this.metrics.messagesTransformed / (timeDiff / 1000);
    this.metrics.dropRate = this.metrics.messagesDropped / (timeDiff / 1000);
    this.metrics.errorRate = this.metrics.errorCount / (timeDiff / 1000);
    
    // Update queue size
    this.metrics.queueSize = Array.from(this.messageQueues.values())
      .reduce((sum, queue) => sum + queue.length, 0);
    
    this.metrics.lastReset = now;
    
    // Reset counters
    this.metrics.messagesRouted = 0;
    this.metrics.messagesFiltered = 0;
    this.metrics.messagesTransformed = 0;
    this.metrics.messagesDropped = 0;
    this.metrics.errorCount = 0;

    this.emit('metrics', this.metrics);
  }

  updateProcessingTimeMetrics(processingTime) {
    // Simple moving average
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime * 0.9) + (processingTime * 0.1);
  }

  getMetrics() {
    return {
      ...this.metrics,
      routes: this.routes.size,
      filters: this.filters.size,
      transformers: this.transformers.size,
      middlewares: this.middlewares.length,
      queueSizes: Array.from(this.messageQueues.entries()).reduce((obj, [priority, queue]) => {
        obj[`priority_${priority}`] = queue.length;
        return obj;
      }, {})
    };
  }

  /**
   * Clear all queues
   */
  clearQueues() {
    for (const queue of this.messageQueues.values()) {
      queue.length = 0;
    }
    this.metrics.queueSize = 0;
    console.log('🧹 Cleared all message queues');
  }

  /**
   * Gracefully shutdown the message router
   */
  async shutdown() {
    console.log('🛑 Shutting down Message Router...');
    
    // Process remaining messages in queues
    console.log('📤 Processing remaining messages...');
    while (this.metrics.queueSize > 0) {
      await this.processMessageQueues();
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    // Clear all data structures
    this.routes.clear();
    this.filters.clear();
    this.transformers.clear();
    this.middlewares.length = 0;
    this.rateLimiters.clear();
    
    console.log('✅ Message Router shutdown complete');
  }
}

export default MessageRouter;
