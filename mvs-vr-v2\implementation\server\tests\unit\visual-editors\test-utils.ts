/**
 * Test utilities for Visual Editors components
 */

import { vi } from 'vitest';

// Mock Vue components for testing
export const createMockVueComponent = (name: string, props: string[] = []) => ({
  name,
  props: props.reduce((acc, prop) => {
    acc[prop] = { type: String, required: false };
    return acc;
  }, {} as Record<string, any>),
  template: `<div class="${name.toLowerCase()}-mock">${name} Mock Component</div>`,
  emits: ['update', 'change', 'save', 'reset'],
  setup() {
    return {};
  }
});

// Mock API client
export const createMockApiClient = () => ({
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
  put: vi.fn()
});

// Mock Directus client
export const createMockDirectusClient = () => ({
  request: vi.fn(),
  items: vi.fn(() => ({
    readByQuery: vi.fn(),
    readOne: vi.fn(),
    createOne: vi.fn(),
    updateOne: vi.fn(),
    deleteOne: vi.fn()
  })),
  auth: {
    login: vi.fn(),
    logout: vi.fn(),
    refresh: vi.fn(),
    token: 'mock-token'
  },
  files: {
    upload: vi.fn(),
    delete: vi.fn()
  }
});

// Mock Supabase client
export const createMockSupabaseClient = () => ({
  auth: {
    signInWithPassword: vi.fn(),
    signOut: vi.fn(),
    getSession: vi.fn(),
    onAuthStateChange: vi.fn()
  },
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    upsert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    then: vi.fn()
  })),
  storage: {
    from: vi.fn(() => ({
      upload: vi.fn(),
      download: vi.fn(),
      remove: vi.fn(),
      list: vi.fn()
    }))
  }
});

// Mock data generators
export const generateMockVendor = (overrides = {}) => ({
  id: 'vendor-1',
  name: 'Test Vendor',
  email: '<EMAIL>',
  status: 'active',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const generateMockProduct = (overrides = {}) => ({
  id: 'product-1',
  name: 'Test Product',
  vendor_id: 'vendor-1',
  category: 'furniture',
  description: 'A test product',
  thumbnail: '/test-thumbnail.jpg',
  status: 'active',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const generateMockMaterial = (overrides = {}) => ({
  id: 'material-1',
  name: 'Test Material',
  vendor_id: 'vendor-1',
  type: 'Standard',
  category: 'wood',
  properties: {
    roughness: 0.8,
    metallic: 0.0,
    specular: 0.5,
    emission: 0.0
  },
  textures: {
    diffuse: '/textures/test_diffuse.jpg',
    normal: '/textures/test_normal.jpg'
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const generateMockAnimation = (overrides = {}) => ({
  id: 'animation-1',
  name: 'Test Animation',
  vendor_id: 'vendor-1',
  target_object: 'object-1',
  duration: 2.0,
  loop: false,
  auto_play: false,
  keyframes: [
    {
      id: 'keyframe-1',
      time: 0.0,
      properties: {
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 }
      },
      easing: 'linear'
    }
  ],
  triggers: [],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const generateMockShowroom = (overrides = {}) => ({
  id: 'showroom-1',
  name: 'Test Showroom',
  vendor_id: 'vendor-1',
  template: 'template_1',
  size: 'medium',
  items: [],
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

export const generateMockLightingSetup = (overrides = {}) => ({
  id: 'lighting-1',
  name: 'Test Lighting',
  vendor_id: 'vendor-1',
  showroom_id: 'showroom-1',
  lights: [
    {
      id: 'light-1',
      type: 'directional',
      name: 'Sun Light',
      position: { x: 0, y: 10, z: 5 },
      rotation: { x: -45, y: 0, z: 0 },
      color: '#FFFFFF',
      intensity: 1.0,
      shadows: true,
      enabled: true
    }
  ],
  environment: {
    skybox: '/environments/studio.hdr',
    ambient_intensity: 0.3,
    ambient_color: '#404040',
    fog_enabled: false
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

// Test helper functions
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

export const createMockEvent = (type: string, properties = {}) => {
  const event = new Event(type);
  Object.assign(event, properties);
  return event;
};

export const createMockFile = (name: string, type: string, size = 1024) => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

// Mock localStorage for tests
export const createMockLocalStorage = () => {
  const storage: Record<string, string> = {};
  return {
    getItem: vi.fn((key: string) => storage[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      storage[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete storage[key];
    }),
    clear: vi.fn(() => {
      Object.keys(storage).forEach(key => delete storage[key]);
    }),
    length: 0,
    key: vi.fn()
  };
};

// Mock window.URL for file handling tests
export const createMockURL = () => ({
  createObjectURL: vi.fn(() => 'blob:mock-url'),
  revokeObjectURL: vi.fn()
});

// Error simulation helpers
export const simulateNetworkError = () => {
  throw new Error('Network Error');
};

export const simulateApiError = (status = 500, message = 'Internal Server Error') => {
  const error = new Error(message);
  (error as any).status = status;
  throw error;
};

// Async test helpers
export const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0));

export const waitFor = async (condition: () => boolean, timeout = 1000) => {
  const start = Date.now();
  while (!condition() && Date.now() - start < timeout) {
    await new Promise(resolve => setTimeout(resolve, 10));
  }
  if (!condition()) {
    throw new Error('Condition not met within timeout');
  }
};
