/**
 * API Key Middleware Tests
 *
 * This file contains tests for the API key authentication middleware.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the entire API key middleware module
vi.mock('../../../api/middleware/api-key-middleware.js', async () => {
  const actual = await vi.importActual('../../../api/middleware/api-key-middleware.js');

  return {
    ...actual,
    getApiKeyData: vi.fn(),
    isRateLimited: vi.fn().mockResolvedValue(false),
    trackApiKeyUsage: vi.fn(),
  };
});

// Mock dependencies
vi.mock('@supabase/supabase-js', () => ({
  createClient: vi.fn(),
}));

vi.mock('../../../api/middleware/auth-middleware.js', () => ({
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
  redis: {
    hincrby: vi.fn().mockResolvedValue(1),
    lpush: vi.fn().mockResolvedValue(1),
    ltrim: vi.fn().mockResolvedValue(1),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    incr: vi.fn().mockResolvedValue(1),
    expire: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    del: vi.fn().mockResolvedValue(1),
    eval: vi.fn().mockResolvedValue(1),
    evalsha: vi.fn().mockResolvedValue(1),
    script: vi.fn().mockReturnValue({
      load: vi.fn().mockResolvedValue('sha1'),
    }),
    sendCommand: vi.fn().mockResolvedValue('OK'),
    call: vi.fn().mockResolvedValue('OK'),
  },
}));

// Mock crypto
vi.mock('crypto', () => ({
  createHash: vi.fn().mockReturnValue({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn().mockReturnValue('hashed_api_key'),
  }),
}));

import {
  authenticateApiKey,
  hasRequiredPermissions,
  hasRequiredScopes,
} from '../../../api/middleware/api-key-middleware.js';

describe('API Key Middleware', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock request, response, and next function
    req = {
      headers: {
        'x-api-key': 'test_api_key',
      },
      query: {},
      ip: '127.0.0.1',
      originalUrl: '/api/test',
      method: 'GET',
    };

    res = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn(),
    };

    next = vi.fn();
  });

  describe('authenticateApiKey - Basic Cases', () => {
    it('should continue if API key is not required', async () => {
      // Remove API key from request
      req.headers['x-api-key'] = undefined;

      // Call middleware with required: false
      await authenticateApiKey({ required: false })(req, res, next);

      // Expect next to be called
      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should return 401 if API key is required but not provided', async () => {
      // Remove API key from request
      req.headers['x-api-key'] = undefined;

      // Call middleware with required: true
      await authenticateApiKey({ required: true })(req, res, next);

      // Expect 401 response
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'API_KEY_REQUIRED',
          message: 'API key is required',
        },
      });
      expect(next).not.toHaveBeenCalled();
    });

    // NOTE: Complex API key validation tests moved to integration tests
    // due to complex mocking requirements with Supabase and Redis
    it.todo('Complex API key validation scenarios moved to integration tests');
  });

  describe('hasRequiredPermissions', () => {
    it('should return true if no permissions are required', () => {
      expect(hasRequiredPermissions(['test:read'], [])).toBe(true);
    });

    it('should return true if key has wildcard permission', () => {
      expect(hasRequiredPermissions(['*'], ['test:read'])).toBe(true);
    });

    it('should return true if key has all required permissions', () => {
      expect(hasRequiredPermissions(['test:read', 'test:write'], ['test:read'])).toBe(true);
    });

    it('should return true if key has wildcard for required permission category', () => {
      expect(hasRequiredPermissions(['test:*'], ['test:read'])).toBe(true);
    });

    it('should return false if key does not have required permissions', () => {
      expect(hasRequiredPermissions(['test:read'], ['test:write'])).toBe(false);
    });
  });

  describe('hasRequiredScopes', () => {
    it('should return true if no scopes are required', () => {
      expect(hasRequiredScopes(['api'], [])).toBe(true);
    });

    it('should return true if key has wildcard scope', () => {
      expect(hasRequiredScopes(['*'], ['api'])).toBe(true);
    });

    it('should return true if key has all required scopes', () => {
      expect(hasRequiredScopes(['api', 'admin'], ['api'])).toBe(true);
    });

    it('should return false if key does not have required scopes', () => {
      expect(hasRequiredScopes(['api'], ['admin'])).toBe(false);
    });
  });
});
