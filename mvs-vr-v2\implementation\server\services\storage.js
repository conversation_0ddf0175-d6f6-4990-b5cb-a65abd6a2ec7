/**
 * Storage Service
 * 
 * This service manages file storage operations for the MVS-VR platform.
 * It supports both local file system and cloud storage providers.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const configService = require('./config');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);
const statAsync = promisify(fs.stat);
const readdirAsync = promisify(fs.readdir);

let storageConfig = null;
let isInitialized = false;

/**
 * Initialize storage service
 * @returns {Promise<void>}
 */
async function initialize() {
  try {
    storageConfig = await configService.getConfig('storage');
    
    // Ensure local upload directory exists if using local storage
    if (storageConfig.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      if (!await existsAsync(uploadPath)) {
        await mkdirAsync(uploadPath, { recursive: true });
      }
    }
    
    isInitialized = true;
    console.log(`Storage service initialized with provider: ${storageConfig.provider}`);
  } catch (error) {
    console.error('Failed to initialize storage service:', error);
    throw error;
  }
}

/**
 * Check if storage is accessible
 * @returns {Promise<boolean>} Access status
 */
async function checkAccess() {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    if (storageConfig.provider === 'local') {
      // Check if upload directory is accessible
      const uploadPath = storageConfig.local.uploadPath;
      const stats = await statAsync(uploadPath);
      return stats.isDirectory();
    } else if (storageConfig.provider === 's3') {
      // For S3, we would check bucket access here
      // This is a placeholder implementation
      return true;
    }
    
    return false;
  } catch (error) {
    console.error('Storage access check failed:', error);
    return false;
  }
}

/**
 * Store a file
 * @param {string} fileName - File name
 * @param {Buffer|string} data - File data
 * @param {Object} options - Storage options
 * @returns {Promise<Object>} Storage result
 */
async function storeFile(fileName, data, options = {}) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    const {
      directory = '',
      contentType = 'application/octet-stream',
      metadata = {}
    } = options;
    
    if (storageConfig.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      const fullDirectory = path.join(uploadPath, directory);
      const filePath = path.join(fullDirectory, fileName);
      
      // Ensure directory exists
      if (!await existsAsync(fullDirectory)) {
        await mkdirAsync(fullDirectory, { recursive: true });
      }
      
      // Write file
      await writeFileAsync(filePath, data);
      
      // Get file stats
      const stats = await statAsync(filePath);
      
      return {
        success: true,
        path: filePath,
        url: `/uploads/${directory}/${fileName}`.replace(/\/+/g, '/'),
        size: stats.size,
        contentType,
        metadata: {
          ...metadata,
          uploadedAt: new Date().toISOString(),
          provider: 'local'
        }
      };
    } else if (storageConfig.provider === 's3') {
      // S3 implementation would go here
      throw new Error('S3 storage not implemented yet');
    }
    
    throw new Error(`Unsupported storage provider: ${storageConfig.provider}`);
  } catch (error) {
    console.error('File storage failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Retrieve a file
 * @param {string} filePath - File path
 * @returns {Promise<Object>} File data
 */
async function retrieveFile(filePath) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    if (storageConfig.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      const fullPath = path.join(uploadPath, filePath);
      
      if (!await existsAsync(fullPath)) {
        throw new Error('File not found');
      }
      
      const data = await readFileAsync(fullPath);
      const stats = await statAsync(fullPath);
      
      return {
        success: true,
        data,
        size: stats.size,
        lastModified: stats.mtime,
        metadata: {
          provider: 'local',
          path: fullPath
        }
      };
    } else if (storageConfig.provider === 's3') {
      // S3 implementation would go here
      throw new Error('S3 storage not implemented yet');
    }
    
    throw new Error(`Unsupported storage provider: ${storageConfig.provider}`);
  } catch (error) {
    console.error('File retrieval failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Delete a file
 * @param {string} filePath - File path
 * @returns {Promise<Object>} Deletion result
 */
async function deleteFile(filePath) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    if (storageConfig.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      const fullPath = path.join(uploadPath, filePath);
      
      if (await existsAsync(fullPath)) {
        await fs.promises.unlink(fullPath);
      }
      
      return {
        success: true,
        message: 'File deleted successfully'
      };
    } else if (storageConfig.provider === 's3') {
      // S3 implementation would go here
      throw new Error('S3 storage not implemented yet');
    }
    
    throw new Error(`Unsupported storage provider: ${storageConfig.provider}`);
  } catch (error) {
    console.error('File deletion failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * List files in a directory
 * @param {string} directory - Directory path
 * @returns {Promise<Object>} File list
 */
async function listFiles(directory = '') {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    if (storageConfig.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      const fullDirectory = path.join(uploadPath, directory);
      
      if (!await existsAsync(fullDirectory)) {
        return {
          success: true,
          files: [],
          directories: []
        };
      }
      
      const items = await readdirAsync(fullDirectory);
      const files = [];
      const directories = [];
      
      for (const item of items) {
        const itemPath = path.join(fullDirectory, item);
        const stats = await statAsync(itemPath);
        
        if (stats.isFile()) {
          files.push({
            name: item,
            size: stats.size,
            lastModified: stats.mtime,
            path: path.join(directory, item)
          });
        } else if (stats.isDirectory()) {
          directories.push({
            name: item,
            path: path.join(directory, item)
          });
        }
      }
      
      return {
        success: true,
        files,
        directories
      };
    } else if (storageConfig.provider === 's3') {
      // S3 implementation would go here
      throw new Error('S3 storage not implemented yet');
    }
    
    throw new Error(`Unsupported storage provider: ${storageConfig.provider}`);
  } catch (error) {
    console.error('File listing failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get storage health status
 * @returns {Promise<Object>} Health status
 */
async function getHealthStatus() {
  try {
    const accessible = await checkAccess();
    
    let stats = {};
    if (storageConfig?.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      if (await existsAsync(uploadPath)) {
        const dirStats = await statAsync(uploadPath);
        stats = {
          provider: 'local',
          uploadPath,
          accessible: dirStats.isDirectory(),
          lastModified: dirStats.mtime
        };
      }
    }
    
    return {
      status: accessible ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      initialized: isInitialized,
      provider: storageConfig?.provider || 'unknown',
      stats
    };
  } catch (error) {
    return {
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

/**
 * Get storage statistics
 * @returns {Promise<Object>} Storage statistics
 */
async function getStatistics() {
  try {
    if (storageConfig?.provider === 'local') {
      const uploadPath = storageConfig.local.uploadPath;
      const result = await listFiles('');
      
      if (result.success) {
        const totalFiles = result.files.length;
        const totalSize = result.files.reduce((sum, file) => sum + file.size, 0);
        
        return {
          timestamp: new Date().toISOString(),
          provider: 'local',
          totalFiles,
          totalSize,
          totalDirectories: result.directories.length,
          uploadPath
        };
      }
    }
    
    return {
      timestamp: new Date().toISOString(),
      provider: storageConfig?.provider || 'unknown',
      error: 'Statistics not available'
    };
  } catch (error) {
    return {
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

module.exports = {
  initialize,
  checkAccess,
  storeFile,
  retrieveFile,
  deleteFile,
  listFiles,
  getHealthStatus,
  getStatistics
};
