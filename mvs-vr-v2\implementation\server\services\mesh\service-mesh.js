/**
 * Service Mesh Implementation
 * Provides service discovery, load balancing, and circuit breaker patterns
 */

const EventEmitter = require('events');

class CircuitBreaker extends EventEmitter {
  constructor(options = {}) {
    super();
    this.options = {
      failureThreshold: options.failureThreshold || 5,
      resetTimeout: options.resetTimeout || 60000,
      monitoringPeriod: options.monitoringPeriod || 10000,
      ...options
    };
    
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failureCount = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
    this.requestCount = 0;
  }

  async execute(fn) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.options.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.emit('stateChange', 'HALF_OPEN');
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  onSuccess() {
    this.failureCount = 0;
    this.successCount++;
    this.requestCount++;
    
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.emit('stateChange', 'CLOSED');
    }
  }

  onFailure() {
    this.failureCount++;
    this.requestCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.options.failureThreshold) {
      this.state = 'OPEN';
      this.emit('stateChange', 'OPEN');
    }
  }

  getStats() {
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      requestCount: this.requestCount,
      lastFailureTime: this.lastFailureTime
    };
  }
}

class ServiceRegistry {
  constructor() {
    this.services = new Map();
    this.healthChecks = new Map();
  }

  register(serviceName, instance) {
    if (!this.services.has(serviceName)) {
      this.services.set(serviceName, []);
    }
    
    this.services.get(serviceName).push({
      ...instance,
      id: `${serviceName}-${Date.now()}-${Math.random()}`,
      registeredAt: new Date(),
      healthy: true
    });
  }

  unregister(serviceName, instanceId) {
    if (this.services.has(serviceName)) {
      const instances = this.services.get(serviceName);
      const index = instances.findIndex(i => i.id === instanceId);
      if (index !== -1) {
        instances.splice(index, 1);
      }
    }
  }

  discover(serviceName) {
    const instances = this.services.get(serviceName) || [];
    return instances.filter(instance => instance.healthy);
  }

  getAllServices() {
    const result = {};
    for (const [serviceName, instances] of this.services.entries()) {
      result[serviceName] = instances.filter(i => i.healthy);
    }
    return result;
  }

  setHealthCheck(serviceName, healthCheckFn) {
    this.healthChecks.set(serviceName, healthCheckFn);
  }

  async runHealthChecks() {
    for (const [serviceName, instances] of this.services.entries()) {
      const healthCheck = this.healthChecks.get(serviceName);
      
      if (healthCheck) {
        for (const instance of instances) {
          try {
            const isHealthy = await healthCheck(instance);
            instance.healthy = isHealthy;
            instance.lastHealthCheck = new Date();
          } catch (error) {
            instance.healthy = false;
            instance.lastHealthCheck = new Date();
            instance.healthError = error.message;
          }
        }
      }
    }
  }
}

class LoadBalancer {
  constructor(strategy = 'round-robin') {
    this.strategy = strategy;
    this.counters = new Map();
  }

  selectInstance(instances) {
    if (!instances || instances.length === 0) {
      throw new Error('No healthy instances available');
    }

    switch (this.strategy) {
      case 'round-robin':
        return this.roundRobin(instances);
      case 'random':
        return this.random(instances);
      case 'least-connections':
        return this.leastConnections(instances);
      default:
        return this.roundRobin(instances);
    }
  }

  roundRobin(instances) {
    const key = instances.map(i => i.id).join(',');
    const counter = this.counters.get(key) || 0;
    const instance = instances[counter % instances.length];
    this.counters.set(key, counter + 1);
    return instance;
  }

  random(instances) {
    const index = Math.floor(Math.random() * instances.length);
    return instances[index];
  }

  leastConnections(instances) {
    return instances.reduce((least, current) => {
      const leastConnections = least.connections || 0;
      const currentConnections = current.connections || 0;
      return currentConnections < leastConnections ? current : least;
    });
  }
}

class ServiceMesh extends EventEmitter {
  constructor(options = {}) {
    super();
    this.registry = new ServiceRegistry();
    this.loadBalancer = new LoadBalancer(options.loadBalancingStrategy);
    this.circuitBreakers = new Map();
    this.options = {
      healthCheckInterval: options.healthCheckInterval || 30000,
      ...options
    };
    
    this.startHealthChecks();
  }

  registerService(serviceName, instance, healthCheck) {
    this.registry.register(serviceName, instance);
    
    if (healthCheck) {
      this.registry.setHealthCheck(serviceName, healthCheck);
    }
    
    this.emit('serviceRegistered', { serviceName, instance });
  }

  unregisterService(serviceName, instanceId) {
    this.registry.unregister(serviceName, instanceId);
    this.emit('serviceUnregistered', { serviceName, instanceId });
  }

  async callService(serviceName, fn, options = {}) {
    const instances = this.registry.discover(serviceName);
    
    if (instances.length === 0) {
      throw new Error(`No healthy instances found for service: ${serviceName}`);
    }

    const instance = this.loadBalancer.selectInstance(instances);
    
    // Get or create circuit breaker for this service
    if (!this.circuitBreakers.has(serviceName)) {
      this.circuitBreakers.set(serviceName, new CircuitBreaker(options.circuitBreaker));
    }
    
    const circuitBreaker = this.circuitBreakers.get(serviceName);
    
    try {
      // Increment connection count
      instance.connections = (instance.connections || 0) + 1;
      
      const result = await circuitBreaker.execute(() => fn(instance));
      
      // Decrement connection count
      instance.connections = Math.max(0, (instance.connections || 1) - 1);
      
      return result;
    } catch (error) {
      // Decrement connection count on error
      instance.connections = Math.max(0, (instance.connections || 1) - 1);
      throw error;
    }
  }

  getServiceStats() {
    const services = this.registry.getAllServices();
    const circuitBreakerStats = {};
    
    for (const [serviceName, breaker] of this.circuitBreakers.entries()) {
      circuitBreakerStats[serviceName] = breaker.getStats();
    }
    
    return {
      services,
      circuitBreakers: circuitBreakerStats,
      totalServices: Object.keys(services).length,
      totalInstances: Object.values(services).reduce((sum, instances) => sum + instances.length, 0)
    };
  }

  startHealthChecks() {
    setInterval(async () => {
      try {
        await this.registry.runHealthChecks();
        this.emit('healthCheckCompleted');
      } catch (error) {
        this.emit('healthCheckError', error);
      }
    }, this.options.healthCheckInterval);
  }
}

module.exports = {
  ServiceMesh,
  CircuitBreaker,
  ServiceRegistry,
  LoadBalancer
};
