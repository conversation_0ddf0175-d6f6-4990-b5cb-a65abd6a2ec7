/**
 * Performance Benchmark Tests
 * 
 * This file contains tests for the performance benchmark service
 * to validate adaptive compression across different device types.
 */

const { expect } = require('chai');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const { PerformanceBenchmarkService, deviceProfiles, testFileConfigs } = require('../services/assets/performance-benchmark');

const existsAsync = promisify(fs.exists);

describe('Performance Benchmark Service', () => {
  let benchmarkService;
  
  before(async function() {
    this.timeout(30000); // Increase timeout for benchmark setup
    
    benchmarkService = new PerformanceBenchmarkService({
      outputDir: path.join(__dirname, 'temp-benchmark')
    });
    
    await benchmarkService.initialize();
  });
  
  after(async function() {
    this.timeout(10000);
    
    if (benchmarkService) {
      await benchmarkService.cleanup();
    }
  });
  
  describe('Initialization', () => {
    it('should create necessary directories', async () => {
      expect(await existsAsync(benchmarkService.outputDir)).to.be.true;
      expect(await existsAsync(benchmarkService.testDataDir)).to.be.true;
      expect(await existsAsync(benchmarkService.resultsDir)).to.be.true;
    });
    
    it('should generate test files', async () => {
      for (const config of testFileConfigs) {
        const filePath = path.join(benchmarkService.testDataDir, config.name + config.extension);
        expect(await existsAsync(filePath)).to.be.true;
        
        const stats = await fs.promises.stat(filePath);
        expect(stats.size).to.be.at.least(config.size * 0.9); // Allow some variance
      }
    });
  });
  
  describe('Device Profiles', () => {
    it('should have comprehensive device profiles', () => {
      expect(deviceProfiles).to.be.an('object');
      expect(Object.keys(deviceProfiles)).to.have.length.at.least(6);
      
      // Check required profiles
      expect(deviceProfiles).to.have.property('low-end-mobile');
      expect(deviceProfiles).to.have.property('high-end-desktop');
      expect(deviceProfiles).to.have.property('save-data-enabled');
    });
    
    it('should have valid headers for each profile', () => {
      for (const [profileKey, profile] of Object.entries(deviceProfiles)) {
        expect(profile).to.have.property('name');
        expect(profile).to.have.property('headers');
        expect(profile.headers).to.have.property('accept-encoding');
        expect(profile.headers).to.have.property('user-agent');
      }
    });
  });
  
  describe('Comprehensive Benchmark', () => {
    it('should run benchmark across all device profiles', async function() {
      this.timeout(60000); // Increase timeout for comprehensive benchmark
      
      const results = await benchmarkService.runComprehensiveBenchmark();
      
      expect(results).to.be.an('object');
      expect(results).to.have.property('timestamp');
      expect(results).to.have.property('deviceProfiles');
      expect(results).to.have.property('summary');
      
      // Check that all device profiles were tested
      expect(Object.keys(results.deviceProfiles)).to.have.length(Object.keys(deviceProfiles).length);
      
      // Check summary statistics
      expect(results.summary.totalTests).to.be.greaterThan(0);
      expect(results.summary.averageCompressionRatio).to.be.greaterThan(1);
      expect(results.summary.averageCompressionTime).to.be.a('number');
    });
    
    it('should show performance differences between device types', async function() {
      this.timeout(60000);
      
      const results = await benchmarkService.runComprehensiveBenchmark();
      
      const lowEndMobile = results.deviceProfiles['low-end-mobile'];
      const highEndDesktop = results.deviceProfiles['high-end-desktop'];
      const saveDataEnabled = results.deviceProfiles['save-data-enabled'];
      
      // Low-end mobile should use faster compression (lower compression time)
      expect(lowEndMobile.averageCompressionTime).to.be.lessThan(highEndDesktop.averageCompressionTime);
      
      // Save-Data enabled should achieve higher compression ratios
      expect(saveDataEnabled.averageCompressionRatio).to.be.greaterThan(lowEndMobile.averageCompressionRatio);
      
      // High-end desktop should handle large files better
      const largeFileResult = highEndDesktop.fileResults['very-large-binary'];
      expect(largeFileResult).to.have.property('compressionSettings');
      expect(largeFileResult.compressionSettings.enableStreaming).to.be.true;
    });
    
    it('should adapt compression settings based on device capabilities', async function() {
      this.timeout(30000);
      
      const results = await benchmarkService.runComprehensiveBenchmark();
      
      for (const [profileKey, deviceResult] of Object.entries(results.deviceProfiles)) {
        expect(deviceResult).to.have.property('capabilities');
        expect(deviceResult.capabilities).to.have.property('level');
        expect(deviceResult.capabilities).to.have.property('deviceType');
        
        // Check that compression settings are adapted
        for (const [fileName, fileResult] of Object.entries(deviceResult.fileResults)) {
          if (fileResult.compressionSettings) {
            const settings = fileResult.compressionSettings;
            
            // Mobile devices should use lower quality settings
            if (deviceResult.capabilities.deviceType === 'mobile') {
              expect(settings.quality).to.equal('low');
            }
            
            // Save-Data enabled should use higher compression
            if (deviceResult.capabilities.saveData) {
              expect(settings.level).to.be.at.least(6);
              expect(settings.quality).to.equal('low');
            }
            
            // Large files should enable progressive loading on capable devices
            if (fileName.includes('large') && deviceResult.capabilities.webGLSupport) {
              expect(settings.enableProgressive).to.be.true;
            }
          }
        }
      }
    });
    
    it('should generate performance metrics', async function() {
      this.timeout(30000);
      
      const results = await benchmarkService.runComprehensiveBenchmark();
      
      for (const [profileKey, deviceResult] of Object.entries(results.deviceProfiles)) {
        expect(deviceResult.averageCompressionRatio).to.be.a('number');
        expect(deviceResult.averageCompressionTime).to.be.a('number');
        expect(deviceResult.totalDataSavings).to.be.a('number');
        
        for (const [fileName, fileResult] of Object.entries(deviceResult.fileResults)) {
          if (!fileResult.error) {
            expect(fileResult.compressionRatio).to.be.greaterThan(0);
            expect(fileResult.actualCompressionTime).to.be.a('number');
            expect(fileResult.dataSavings).to.be.a('number');
            expect(fileResult.dataSavingsPercent).to.be.a('number');
          }
        }
      }
    });
  });
  
  describe('Performance Validation', () => {
    it('should meet performance targets for different device types', async function() {
      this.timeout(45000);
      
      const results = await benchmarkService.runComprehensiveBenchmark();
      
      // Performance targets
      const targets = {
        'low-end-mobile': {
          maxCompressionTime: 2.0, // seconds
          minCompressionRatio: 1.5
        },
        'high-end-desktop': {
          maxCompressionTime: 5.0, // seconds
          minCompressionRatio: 2.0
        },
        'save-data-enabled': {
          maxCompressionTime: 10.0, // Allow longer for better compression
          minCompressionRatio: 2.5
        }
      };
      
      for (const [profileKey, target] of Object.entries(targets)) {
        const deviceResult = results.deviceProfiles[profileKey];
        
        if (deviceResult) {
          expect(deviceResult.averageCompressionTime).to.be.lessThan(target.maxCompressionTime);
          expect(deviceResult.averageCompressionRatio).to.be.greaterThan(target.minCompressionRatio);
          
          console.log(`${deviceResult.name}: ${deviceResult.averageCompressionRatio.toFixed(2)}x compression in ${deviceResult.averageCompressionTime.toFixed(3)}s`);
        }
      }
    });
    
    it('should demonstrate data savings across device types', async function() {
      this.timeout(30000);
      
      const results = await benchmarkService.runComprehensiveBenchmark();
      
      let totalOriginalSize = 0;
      let totalCompressedSize = 0;
      
      for (const [profileKey, deviceResult] of Object.entries(results.deviceProfiles)) {
        for (const [fileName, fileResult] of Object.entries(deviceResult.fileResults)) {
          if (!fileResult.error) {
            totalOriginalSize += fileResult.fileSize;
            totalCompressedSize += fileResult.compressedSize;
          }
        }
      }
      
      const overallCompressionRatio = totalOriginalSize / totalCompressedSize;
      const overallDataSavings = totalOriginalSize - totalCompressedSize;
      const overallDataSavingsPercent = (overallDataSavings / totalOriginalSize) * 100;
      
      expect(overallCompressionRatio).to.be.greaterThan(1.5);
      expect(overallDataSavingsPercent).to.be.greaterThan(30);
      
      console.log(`Overall compression: ${overallCompressionRatio.toFixed(2)}x (${overallDataSavingsPercent.toFixed(1)}% data savings)`);
    });
  });
});
