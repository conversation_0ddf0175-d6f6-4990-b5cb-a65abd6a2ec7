/**
 * Enhanced Data Masking Middleware
 * 
 * This middleware provides sophisticated data masking capabilities
 * with different masking strategies for different data types.
 */

const crypto = require('crypto');
const logger = require('../utils/logger').getLogger('enhanced-data-masking');

// Masking strategies
const MASKING_STRATEGIES = {
  REDACT: 'redact',           // Replace with [REDACTED]
  PARTIAL: 'partial',         // Show first/last few characters
  HASH: 'hash',              // Replace with hash
  TOKENIZE: 'tokenize',      // Replace with reversible token
  ZERO: 'zero',              // Replace with zero/empty values
  PRESERVE_FORMAT: 'preserve_format' // Maintain format but mask content
};

// Data type configurations
const DATA_TYPE_CONFIGS = {
  // Critical data - full redaction
  password: {
    strategy: MASKING_STRATEGIES.REDACT,
    replacement: '[REDACTED]'
  },
  secret: {
    strategy: MASKING_STRATEGIES.REDACT,
    replacement: '[REDACTED]'
  },
  api_key: {
    strategy: MASKING_STRATEGIES.REDACT,
    replacement: '[REDACTED]'
  },
  private_key: {
    strategy: MASKING_STRATEGIES.REDACT,
    replacement: '[REDACTED]'
  },
  
  // High sensitivity - partial masking
  email: {
    strategy: MASKING_STRATEGIES.PARTIAL,
    visibleStart: 2,
    visibleEnd: 0,
    maskChar: '*',
    preserveDomain: true
  },
  phone: {
    strategy: MASKING_STRATEGIES.PARTIAL,
    visibleStart: 3,
    visibleEnd: 2,
    maskChar: '*',
    preserveFormat: true
  },
  credit_card: {
    strategy: MASKING_STRATEGIES.PARTIAL,
    visibleStart: 0,
    visibleEnd: 4,
    maskChar: '*',
    preserveFormat: true
  },
  ssn: {
    strategy: MASKING_STRATEGIES.PARTIAL,
    visibleStart: 0,
    visibleEnd: 4,
    maskChar: '*',
    preserveFormat: true
  },
  
  // Medium sensitivity - format preservation
  ip_address: {
    strategy: MASKING_STRATEGIES.PRESERVE_FORMAT,
    maskChar: 'X',
    preserveStructure: true
  },
  address: {
    strategy: MASKING_STRATEGIES.PARTIAL,
    visibleStart: 0,
    visibleEnd: 0,
    maskChar: '*',
    replacement: '[ADDRESS REDACTED]'
  },
  
  // Low sensitivity - tokenization
  username: {
    strategy: MASKING_STRATEGIES.TOKENIZE,
    tokenPrefix: 'USER_'
  },
  user_id: {
    strategy: MASKING_STRATEGIES.HASH,
    algorithm: 'sha256',
    truncate: 8
  }
};

// Field pattern mappings
const FIELD_PATTERNS = [
  { pattern: /password/i, type: 'password' },
  { pattern: /secret/i, type: 'secret' },
  { pattern: /api[_-]?key/i, type: 'api_key' },
  { pattern: /private[_-]?key/i, type: 'private_key' },
  { pattern: /email/i, type: 'email' },
  { pattern: /phone/i, type: 'phone' },
  { pattern: /credit[_-]?card/i, type: 'credit_card' },
  { pattern: /card[_-]?number/i, type: 'credit_card' },
  { pattern: /ssn/i, type: 'ssn' },
  { pattern: /social[_-]?security/i, type: 'ssn' },
  { pattern: /ip[_-]?address/i, type: 'ip_address' },
  { pattern: /address/i, type: 'address' },
  { pattern: /username/i, type: 'username' },
  { pattern: /user[_-]?id/i, type: 'user_id' }
];

// Content pattern detection
const CONTENT_PATTERNS = [
  {
    name: 'email',
    pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    type: 'email'
  },
  {
    name: 'phone',
    pattern: /\b(?:\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}\b/g,
    type: 'phone'
  },
  {
    name: 'credit_card',
    pattern: /\b(?:\d[ -]*?){13,16}\b/g,
    type: 'credit_card'
  },
  {
    name: 'ssn',
    pattern: /\b\d{3}[-]?\d{2}[-]?\d{4}\b/g,
    type: 'ssn'
  },
  {
    name: 'ip_address',
    pattern: /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
    type: 'ip_address'
  }
];

// Token storage for reversible masking
const tokenStore = new Map();

/**
 * Determine data type from field name
 * @param {string} fieldName - Field name to analyze
 * @returns {string|null} Data type or null if not sensitive
 */
function determineDataType(fieldName) {
  for (const { pattern, type } of FIELD_PATTERNS) {
    if (pattern.test(fieldName)) {
      return type;
    }
  }
  return null;
}

/**
 * Apply redaction masking
 * @param {any} value - Value to mask
 * @param {Object} config - Masking configuration
 * @returns {any} Masked value
 */
function applyRedactionMasking(value, config) {
  return config.replacement || '[REDACTED]';
}

/**
 * Apply partial masking
 * @param {string} value - Value to mask
 * @param {Object} config - Masking configuration
 * @returns {string} Masked value
 */
function applyPartialMasking(value, config) {
  if (typeof value !== 'string') {
    return '[REDACTED]';
  }
  
  const {
    visibleStart = 0,
    visibleEnd = 0,
    maskChar = '*',
    preserveDomain = false,
    preserveFormat = false
  } = config;
  
  // Special handling for email
  if (preserveDomain && value.includes('@')) {
    const [localPart, domain] = value.split('@');
    const maskedLocal = applyPartialMasking(localPart, { ...config, preserveDomain: false });
    return `${maskedLocal}@${domain}`;
  }
  
  // Preserve format for structured data
  if (preserveFormat) {
    return value.replace(/\d/g, maskChar);
  }
  
  const length = value.length;
  
  if (length <= visibleStart + visibleEnd) {
    return maskChar.repeat(length);
  }
  
  const start = value.substring(0, visibleStart);
  const end = value.substring(length - visibleEnd);
  const middle = maskChar.repeat(length - visibleStart - visibleEnd);
  
  return start + middle + end;
}

/**
 * Apply hash masking
 * @param {any} value - Value to mask
 * @param {Object} config - Masking configuration
 * @returns {string} Masked value
 */
function applyHashMasking(value, config) {
  const {
    algorithm = 'sha256',
    truncate = null
  } = config;
  
  const hash = crypto.createHash(algorithm).update(String(value)).digest('hex');
  return truncate ? hash.substring(0, truncate) : hash;
}

/**
 * Apply tokenization masking
 * @param {any} value - Value to mask
 * @param {Object} config - Masking configuration
 * @returns {string} Masked value
 */
function applyTokenizationMasking(value, config) {
  const {
    tokenPrefix = 'TOKEN_'
  } = config;
  
  // Generate or retrieve token
  const valueKey = String(value);
  if (tokenStore.has(valueKey)) {
    return tokenStore.get(valueKey);
  }
  
  const token = tokenPrefix + crypto.randomBytes(8).toString('hex').toUpperCase();
  tokenStore.set(valueKey, token);
  
  return token;
}

/**
 * Apply preserve format masking
 * @param {string} value - Value to mask
 * @param {Object} config - Masking configuration
 * @returns {string} Masked value
 */
function applyPreserveFormatMasking(value, config) {
  const {
    maskChar = 'X',
    preserveStructure = true
  } = config;
  
  if (typeof value !== 'string') {
    return '[REDACTED]';
  }
  
  if (preserveStructure) {
    // Preserve dots, dashes, spaces, etc.
    return value.replace(/[a-zA-Z0-9]/g, maskChar);
  }
  
  return maskChar.repeat(value.length);
}

/**
 * Apply zero masking
 * @param {any} value - Value to mask
 * @param {Object} config - Masking configuration
 * @returns {any} Masked value
 */
function applyZeroMasking(value, config) {
  if (typeof value === 'string') {
    return '';
  } else if (typeof value === 'number') {
    return 0;
  } else if (typeof value === 'boolean') {
    return false;
  } else if (Array.isArray(value)) {
    return [];
  } else if (typeof value === 'object') {
    return {};
  }
  return null;
}

/**
 * Mask a value based on its data type
 * @param {any} value - Value to mask
 * @param {string} dataType - Data type
 * @param {string} fieldName - Field name for context
 * @returns {any} Masked value
 */
function maskValue(value, dataType, fieldName) {
  const config = DATA_TYPE_CONFIGS[dataType];
  if (!config) {
    return value;
  }
  
  try {
    switch (config.strategy) {
      case MASKING_STRATEGIES.REDACT:
        return applyRedactionMasking(value, config);
      
      case MASKING_STRATEGIES.PARTIAL:
        return applyPartialMasking(value, config);
      
      case MASKING_STRATEGIES.HASH:
        return applyHashMasking(value, config);
      
      case MASKING_STRATEGIES.TOKENIZE:
        return applyTokenizationMasking(value, config);
      
      case MASKING_STRATEGIES.ZERO:
        return applyZeroMasking(value, config);
      
      case MASKING_STRATEGIES.PRESERVE_FORMAT:
        return applyPreserveFormatMasking(value, config);
      
      default:
        return applyRedactionMasking(value, { replacement: '[REDACTED]' });
    }
  } catch (error) {
    logger.error(`Error masking value for field ${fieldName}:`, error);
    return '[MASKING_ERROR]';
  }
}

/**
 * Mask content patterns in strings
 * @param {string} content - Content to mask
 * @returns {string} Masked content
 */
function maskContentPatterns(content) {
  if (typeof content !== 'string') {
    return content;
  }
  
  let maskedContent = content;
  
  for (const { pattern, type } of CONTENT_PATTERNS) {
    maskedContent = maskedContent.replace(pattern, (match) => {
      return maskValue(match, type, `content_${type}`);
    });
  }
  
  return maskedContent;
}

/**
 * Enhanced data masking middleware
 * @param {Object} options - Middleware options
 * @returns {Function} Express middleware
 */
function enhancedDataMasking(options = {}) {
  const {
    enabled = true,
    maskContent = true,
    logMasking = false,
    customMaskingRules = {},
    preserveStructure = true
  } = options;
  
  // Merge custom masking rules
  Object.assign(DATA_TYPE_CONFIGS, customMaskingRules);
  
  return (req, res, next) => {
    if (!enabled) {
      return next();
    }
    
    // Store original res.json method
    const originalJson = res.json;
    
    // Override res.json method
    res.json = function(data) {
      try {
        // Apply enhanced masking
        const maskedData = maskDataRecursively(data, '', new Set());
        
        if (logMasking) {
          logger.info('Enhanced data masking applied', {
            path: req.path,
            method: req.method,
            user: req.user?.id || 'anonymous'
          });
        }
        
        // Call original json method with masked data
        return originalJson.call(this, maskedData);
        
      } catch (error) {
        logger.error('Error in enhanced data masking:', error);
        return originalJson.call(this, data);
      }
    };
    
    next();
  };
  
  /**
   * Recursively mask data
   * @param {any} data - Data to mask
   * @param {string} path - Current path
   * @param {Set} visited - Visited objects
   * @returns {any} Masked data
   */
  function maskDataRecursively(data, path = '', visited = new Set()) {
    if (data == null) {
      return data;
    }
    
    if (typeof data !== 'object') {
      // Mask content patterns in strings
      if (maskContent && typeof data === 'string') {
        return maskContentPatterns(data);
      }
      return data;
    }
    
    // Prevent circular references
    if (visited.has(data)) {
      return '[Circular]';
    }
    visited.add(data);
    
    // Handle arrays
    if (Array.isArray(data)) {
      return data.map((item, index) => 
        maskDataRecursively(item, `${path}[${index}]`, visited)
      );
    }
    
    // Handle objects
    const result = {};
    
    for (const [key, value] of Object.entries(data)) {
      const currentPath = path ? `${path}.${key}` : key;
      const dataType = determineDataType(key);
      
      if (dataType) {
        // Apply masking based on data type
        result[key] = maskValue(value, dataType, key);
      } else {
        // Recursively process nested data
        result[key] = maskDataRecursively(value, currentPath, visited);
      }
    }
    
    return result;
  }
}

/**
 * Get masking statistics
 * @returns {Object} Masking statistics
 */
function getMaskingStats() {
  return {
    tokenStoreSize: tokenStore.size,
    supportedDataTypes: Object.keys(DATA_TYPE_CONFIGS).length,
    contentPatterns: CONTENT_PATTERNS.length,
    fieldPatterns: FIELD_PATTERNS.length
  };
}

/**
 * Clear token store
 */
function clearTokenStore() {
  tokenStore.clear();
}

module.exports = {
  enhancedDataMasking,
  getMaskingStats,
  clearTokenStore,
  maskValue,
  maskContentPatterns,
  MASKING_STRATEGIES,
  DATA_TYPE_CONFIGS
};
