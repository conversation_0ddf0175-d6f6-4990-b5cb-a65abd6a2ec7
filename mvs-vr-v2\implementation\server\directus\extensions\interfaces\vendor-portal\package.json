{"name": "directus-extension-vendor-portal", "version": "1.0.0", "description": "Custom interface for vendor portal", "main": "index.js", "keywords": ["directus", "directus-extension", "directus-custom-interface", "vendor-portal"], "directus:extension": {"type": "interface", "path": "index.js", "source": "src/index.js", "host": "^10.0.0"}, "scripts": {"build": "directus-extension build", "dev": "directus-extension build --watch", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:core": "vitest run src/services/tests/GuidedSetupService.vitest.js src/components/GuidedSetupWizard/tests/WizardStep.vitest.js", "test:core:watch": "vitest src/services/tests/GuidedSetupService.vitest.js src/components/GuidedSetupWizard/tests/WizardStep.vitest.js", "test:all": "vitest run --reporter=verbose", "test:all:watch": "vitest --reporter=verbose", "test:all:coverage": "vitest run --coverage --reporter=verbose", "test:services": "vitest run src/services/tests/", "test:components": "vitest run src/components/", "test:integration": "vitest run src/tests/", "test:spec": "vitest run tests/", "test:migration": "node migrate-jest-to-vitest.js", "test:migration:dry": "node -e \"const script = require('./migrate-jest-to-vitest.js'); script.config.dryRun = true; script.main();\""}, "dependencies": {"axios": "^1.3.4", "chart.js": "^4.2.1", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.0", "vue-chartjs": "^4.1.2", "vue-router": "^3.6.5", "y-indexeddb": "^9.0.12", "y-websocket": "^1.5.4", "yjs": "^13.6.27"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@directus/extensions-sdk": "^10.0.0", "@vitest/coverage-v8": "^1.6.1", "@vitest/ui": "^1.2.1", "@vue/test-utils": "^1.3.6", "jsdom": "^22.1.0", "vite": "^4.5.2", "vite-plugin-vue2": "^2.0.3", "vitest": "^1.6.1", "vue": "^2.7.16", "vue-template-compiler": "^2.7.16"}}