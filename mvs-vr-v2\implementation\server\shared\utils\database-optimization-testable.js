/**
 * Testable Database Optimization Module
 *
 * This is a refactored version of the database optimization module that uses
 * dependency injection to improve testability. This serves as an example
 * of how to make modules more testable.
 */

import { createTestableModule } from './dependency-injection.js';

/**
 * Create database optimization module with injected dependencies
 * @param {Object} supabaseClient - Supabase client instance
 * @param {Object} redisClient - Redis client instance
 * @param {Object} logger - Logger instance
 * @returns {Object} Database optimization functions
 */
function createDatabaseOptimization(supabaseClient, redisClient, logger) {
  /**
   * Generate cache key for query
   * @param {string} table - Table name
   * @param {Object} options - Query options
   * @returns {string} Cache key
   */
  function generateCacheKey(table, options = {}) {
    const keyParts = [
      'db_query',
      table,
      JSON.stringify(options.filters || {}),
      JSON.stringify(options.order || {}),
      options.limit || 'all',
      options.offset || 0,
    ];
    return keyParts.join(':');
  }

  /**
   * Optimized query builder
   * @param {string} table - Table name
   * @returns {Object} Query builder
   */
  function optimizedQuery(table) {
    let query = supabaseClient.from(table);

    return {
      /**
       * Select data with optimization
       * @param {string} columns - Columns to select
       * @param {Object} options - Query options
       * @returns {Promise} Query result
       */
      async select(columns = '*', options = {}) {
        const startTime = Date.now();

        // Build query
        query = query.select(columns);

        // Apply filters
        if (options.filters) {
          for (const [key, value] of Object.entries(options.filters)) {
            if (typeof value === 'object' && value !== null) {
              // Handle complex filters
              for (const [operator, operatorValue] of Object.entries(value)) {
                switch (operator) {
                  case 'eq':
                    query = query.eq(key, operatorValue);
                    break;
                  case 'gt':
                    query = query.gt(key, operatorValue);
                    break;
                  case 'lt':
                    query = query.lt(key, operatorValue);
                    break;
                  case 'gte':
                    query = query.gte(key, operatorValue);
                    break;
                  case 'lte':
                    query = query.lte(key, operatorValue);
                    break;
                  case 'like':
                    query = query.like(key, operatorValue);
                    break;
                  case 'ilike':
                    query = query.ilike(key, operatorValue);
                    break;
                  case 'in':
                    query = query.in(key, operatorValue);
                    break;
                  case 'is':
                    query = query.is(key, operatorValue);
                    break;
                }
              }
            } else {
              // Simple equality filter
              query = query.eq(key, value);
            }
          }
        }

        // Apply ordering
        if (options.order) {
          query = query.order(options.order.column, {
            ascending: options.order.ascending !== false,
          });
        }

        // Apply pagination
        if (options.limit || options.offset) {
          const start = options.offset || 0;
          const end = start + (options.limit || 1000) - 1;
          query = query.range(start, end);
        }

        // Check cache if enabled
        if (options.cache) {
          const cacheKey = generateCacheKey(table, options);
          try {
            const cached = await redisClient.get(cacheKey);
            if (cached) {
              logger.debug(`Cache hit for query: ${cacheKey}`);
              return JSON.parse(cached);
            }
          } catch (error) {
            logger.warn('Cache read error:', error);
          }
        }

        // Execute query
        const { data, error } = await query;
        const duration = Date.now() - startTime;

        // Log slow queries
        if (duration > 500) {
          logger.warn('Slow query detected', {
            table,
            duration,
            columns,
            filters: options.filters,
            order: options.order,
            limit: options.limit,
            offset: options.offset,
          });
        }

        // Cache result if enabled and successful
        if (options.cache && !error && data) {
          const cacheKey = generateCacheKey(table, options);
          const ttl = options.ttl || 300; // 5 minutes default
          try {
            await redisClient.set(cacheKey, JSON.stringify({ data, error }), 'EX', ttl);
            logger.debug(`Cached query result: ${cacheKey}`);
          } catch (cacheError) {
            logger.warn('Cache write error:', cacheError);
          }
        }

        return { data, error };
      },
    };
  }

  /**
   * Invalidate table cache
   * @param {string} table - Table name
   * @returns {Promise}
   */
  async function invalidateTableCache(table) {
    try {
      const pattern = `db_query:${table}:*`;
      const keys = await redisClient.keys(pattern);

      if (keys.length > 0) {
        await redisClient.del(...keys);
        logger.debug(`Invalidated ${keys.length} cache entries for table: ${table}`);
      }
    } catch (error) {
      logger.error('Error invalidating table cache:', error);
    }
  }

  return {
    optimizedQuery,
    invalidateTableCache,
    generateCacheKey,
  };
}

// Export testable module
export default createTestableModule(createDatabaseOptimization, [
  'supabaseClient',
  'redisClient',
  'logger',
]);
