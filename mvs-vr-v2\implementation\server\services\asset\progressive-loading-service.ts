/**
 * Progressive Loading Service
 *
 * This service provides progressive loading capabilities for assets,
 * including priority-based loading, streaming, and adaptive quality.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { logger } from '../../shared/utils/logger';
import { CdnIntegrationService } from './cdn-integration-service';
import { AssetBundleOptimizer } from './asset-bundle-optimizer';
import { createReadStream, createWriteStream, promises as fs } from 'fs';
import * as path from 'path';
import * as os from 'os';

/**
 * Asset loading priority levels
 */
export enum LoadPriority {
  CRITICAL = 0,
  HIGH = 1,
  MEDIUM = 2,
  LOW = 3,
  BACKGROUND = 4,
}

/**
 * Asset loading request
 */
export interface AssetLoadRequest {
  assetId: string;
  priority: LoadPriority;
  callback?: (error: Error | null, url?: string) => void;
  quality?: string;
  region?: string;
}

/**
 * Asset loading status
 */
export interface AssetLoadStatus {
  assetId: string;
  status: 'pending' | 'loading' | 'complete' | 'error';
  progress: number;
  url?: string;
  error?: string;
  startTime: number;
  endTime?: number;
}

/**
 * Network quality levels
 */
export enum NetworkQuality {
  POOR = 0,
  MEDIUM = 1,
  GOOD = 2,
  EXCELLENT = 3,
}

/**
 * Progressive loading options
 */
export interface ProgressiveLoadingOptions {
  maxConcurrentLoads?: number;
  chunkSize?: number;
  cacheDir?: string;
  adaptiveQuality?: boolean;
  preloadCriticalAssets?: boolean;
  prioritizeVisibleAssets?: boolean;
}

/**
 * Progressive Loading Service
 */
export class ProgressiveLoadingService {
  private supabase: SupabaseClient;
  private cdnService: CdnIntegrationService;
  private bundleOptimizer: AssetBundleOptimizer;
  private maxConcurrentLoads: number;
  private chunkSize: number;
  private cacheDir: string;
  private adaptiveQuality: boolean;
  private preloadCriticalAssets: boolean;
  private prioritizeVisibleAssets: boolean;
  private loadQueues: Map<LoadPriority, AssetLoadRequest[]>;
  private activeLoads: Map<string, NodeJS.Timeout>;
  private loadStatus: Map<string, AssetLoadStatus>;
  private networkQuality: NetworkQuality;
  private isProcessing: boolean;

  /**
   * Constructor
   *
   * @param supabase - Supabase client
   * @param cdnService - CDN integration service
   * @param bundleOptimizer - Asset bundle optimizer
   * @param options - Progressive loading options
   */
  constructor(
    supabase: SupabaseClient,
    cdnService: CdnIntegrationService,
    bundleOptimizer: AssetBundleOptimizer,
    options: ProgressiveLoadingOptions = {},
  ) {
    this.supabase = supabase;
    this.cdnService = cdnService;
    this.bundleOptimizer = bundleOptimizer;
    this.maxConcurrentLoads = options.maxConcurrentLoads || 4;
    this.chunkSize = options.chunkSize || 1024 * 1024; // 1MB
    this.cacheDir = options.cacheDir || path.join(os.tmpdir(), 'mvs-vr-asset-cache');
    this.adaptiveQuality = options.adaptiveQuality !== false;
    this.preloadCriticalAssets = options.preloadCriticalAssets !== false;
    this.prioritizeVisibleAssets = options.prioritizeVisibleAssets !== false;
    this.loadQueues = new Map();
    this.activeLoads = new Map();
    this.loadStatus = new Map();
    this.networkQuality = NetworkQuality.GOOD;
    this.isProcessing = false;

    // Initialize load queues
    Object.values(LoadPriority)
      .filter(v => !isNaN(Number(v)))
      .forEach(priority => {
        this.loadQueues.set(Number(priority), []);
      });

    // Create cache directory if it doesn't exist
    this.ensureCacheDir();
  }

  /**
   * Ensure cache directory exists
   */
  private async ensureCacheDir(): Promise<void> {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
      logger.info(`Cache directory created: ${this.cacheDir}`);
    } catch (error) {
      logger.error('Error creating cache directory', { error });
    }
  }

  /**
   * Load an asset
   *
   * @param request - Asset load request
   * @returns Promise that resolves when the asset is loaded
   */
  public async loadAsset(request: AssetLoadRequest): Promise<string> {
    return new Promise((resolve, reject) => {
      // Check if asset is already loaded or loading
      const status = this.loadStatus.get(request.assetId);
      if (status) {
        if (status.status === 'complete') {
          // Asset already loaded
          resolve(status.url!);
          return;
        } else if (status.status === 'error') {
          // Asset failed to load
          reject(new Error(status.error));
          return;
        }
        // Asset is loading, update priority if higher
        if (request.priority < status.status) {
          // Find and update the request in the queue
          const queue = this.loadQueues.get(status.status);
          if (queue) {
            const index = queue.findIndex(r => r.assetId === request.assetId);
            if (index !== -1) {
              queue.splice(index, 1);
              this.loadQueues.get(request.priority)?.push({
                ...request,
                callback: (error, url) => {
                  if (error) {
                    reject(error);
                  } else {
                    resolve(url!);
                  }
                },
              });
            }
          }
        }
        return;
      }

      // Add to load queue
      this.loadQueues.get(request.priority)?.push({
        ...request,
        callback: (error, url) => {
          if (error) {
            reject(error);
          } else {
            resolve(url!);
          }
        },
      });

      // Update load status
      this.loadStatus.set(request.assetId, {
        assetId: request.assetId,
        status: 'pending',
        progress: 0,
        startTime: Date.now(),
      });

      // Start processing if not already
      if (!this.isProcessing) {
        this.processQueue();
      }
    });
  }

  /**
   * Process the load queue
   */
  private processQueue(): void {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    // Process queue until empty or max concurrent loads reached
    const processNext = () => {
      // Check if we've reached max concurrent loads
      if (this.activeLoads.size >= this.maxConcurrentLoads) {
        return;
      }

      // Find the highest priority request
      let nextRequest: AssetLoadRequest | undefined;
      for (const priority of Array.from(this.loadQueues.keys()).sort()) {
        const queue = this.loadQueues.get(priority);
        if (queue && queue.length > 0) {
          nextRequest = queue.shift();
          break;
        }
      }

      if (!nextRequest) {
        // No more requests
        if (this.activeLoads.size === 0) {
          this.isProcessing = false;
        }
        return;
      }

      // Start loading the asset
      this.startLoading(nextRequest);

      // Process next request
      processNext();
    };

    processNext();
  }

  /**
   * Start loading an asset
   *
   * @param request - Asset load request
   */
  private startLoading(request: AssetLoadRequest): void {
    // Update status
    this.loadStatus.set(request.assetId, {
      ...this.loadStatus.get(request.assetId)!,
      status: 'loading',
      progress: 0,
    });

    // Determine quality based on network quality if adaptive quality is enabled
    let quality = request.quality;
    if (this.adaptiveQuality && !quality) {
      quality = this.getQualityForNetworkQuality(this.networkQuality);
    }

    // Get CDN URL with adaptive compression parameters
    let cdnUrl: string;
    if (request.region) {
      cdnUrl = this.cdnService.getRegionalCdnUrl(request.assetId, request.region);
    } else {
      cdnUrl = this.cdnService.getCdnUrl(request.assetId);
    }

    // Add adaptive compression parameters
    const urlParams = new URLSearchParams();

    if (quality) {
      urlParams.set('quality', quality);
    }

    // Add client capability hints for server-side optimization
    urlParams.set('device_type', this.getDeviceTypeFromNetworkQuality());
    urlParams.set('network_quality', this.networkQuality.toString());
    urlParams.set('adaptive_compression', 'true');

    // Add progressive loading parameters for large assets
    if (this.shouldUseProgressiveLoading(request)) {
      urlParams.set('progressive', 'true');
      urlParams.set('chunk_size', this.getOptimalChunkSize().toString());
    }

    if (urlParams.toString()) {
      cdnUrl += `?${urlParams.toString()}`;
    }

    // Simulate progressive loading for large assets
    if (this.shouldUseProgressiveLoading(request)) {
      this.startProgressiveLoading(request, cdnUrl);
    } else {
      this.startStandardLoading(request, cdnUrl);
    }
  }

  /**
   * Check if progressive loading should be used for this request
   */
  private shouldUseProgressiveLoading(request: AssetLoadRequest): boolean {
    // Use progressive loading for high priority assets or when network quality is poor
    return request.priority <= LoadPriority.HIGH || this.networkQuality <= NetworkQuality.MEDIUM;
  }

  /**
   * Get optimal chunk size based on network quality and device capabilities
   */
  private getOptimalChunkSize(): number {
    switch (this.networkQuality) {
      case NetworkQuality.POOR:
        return 256 * 1024; // 256KB
      case NetworkQuality.MEDIUM:
        return 512 * 1024; // 512KB
      case NetworkQuality.GOOD:
        return 1024 * 1024; // 1MB
      case NetworkQuality.EXCELLENT:
        return 2 * 1024 * 1024; // 2MB
      default:
        return this.chunkSize;
    }
  }

  /**
   * Get device type hint from network quality
   */
  private getDeviceTypeFromNetworkQuality(): string {
    if (this.networkQuality <= NetworkQuality.POOR) {
      return 'mobile';
    } else if (this.networkQuality >= NetworkQuality.EXCELLENT) {
      return 'desktop';
    }
    return 'tablet';
  }

  /**
   * Start progressive loading for an asset
   */
  private startProgressiveLoading(request: AssetLoadRequest, cdnUrl: string): void {
    const chunkSize = this.getOptimalChunkSize();
    const totalChunks = Math.ceil((1024 * 1024) / chunkSize); // Assume 1MB asset for simulation
    let loadedChunks = 0;

    const loadNextChunk = () => {
      if (loadedChunks >= totalChunks) {
        // Loading complete
        this.loadStatus.set(request.assetId, {
          ...this.loadStatus.get(request.assetId)!,
          status: 'complete',
          progress: 100,
          url: cdnUrl,
          endTime: Date.now(),
        });

        this.activeLoads.delete(request.assetId);
        request.callback?.(null, cdnUrl);
        this.processQueue();
        return;
      }

      // Update progress
      const progress = Math.round((loadedChunks / totalChunks) * 100);
      this.loadStatus.set(request.assetId, {
        ...this.loadStatus.get(request.assetId)!,
        progress,
      });

      loadedChunks++;

      // Schedule next chunk based on network quality
      const delay = this.getChunkLoadDelay();
      setTimeout(loadNextChunk, delay);
    };

    // Start loading first chunk
    loadNextChunk();
  }

  /**
   * Start standard loading for an asset
   */
  private startStandardLoading(request: AssetLoadRequest, cdnUrl: string): void {
    // Simulate loading time based on network quality
    const loadTime = this.getLoadTimeForNetworkQuality();

    const timeout = setTimeout(() => {
      this.loadStatus.set(request.assetId, {
        ...this.loadStatus.get(request.assetId)!,
        status: 'complete',
        progress: 100,
        url: cdnUrl,
        endTime: Date.now(),
      });

      this.activeLoads.delete(request.assetId);
      request.callback?.(null, cdnUrl);
      this.processQueue();
    }, loadTime);

    this.activeLoads.set(request.assetId, timeout);
  }

  /**
   * Get chunk load delay based on network quality
   */
  private getChunkLoadDelay(): number {
    switch (this.networkQuality) {
      case NetworkQuality.POOR:
        return 500; // 500ms between chunks
      case NetworkQuality.MEDIUM:
        return 200; // 200ms between chunks
      case NetworkQuality.GOOD:
        return 100; // 100ms between chunks
      case NetworkQuality.EXCELLENT:
        return 50; // 50ms between chunks
      default:
        return 200;
    }
  }

  /**
   * Get load time based on network quality
   */
  private getLoadTimeForNetworkQuality(): number {
    switch (this.networkQuality) {
      case NetworkQuality.POOR:
        return 3000; // 3 seconds
      case NetworkQuality.MEDIUM:
        return 1500; // 1.5 seconds
      case NetworkQuality.GOOD:
        return 800; // 0.8 seconds
      case NetworkQuality.EXCELLENT:
        return 400; // 0.4 seconds
      default:
        return 1000;
    }
  }

  /**
   * Get quality for network quality
   *
   * @param networkQuality - Network quality
   * @returns Quality string
   */
  private getQualityForNetworkQuality(networkQuality: NetworkQuality): string {
    switch (networkQuality) {
      case NetworkQuality.POOR:
        return 'low';
      case NetworkQuality.MEDIUM:
        return 'medium';
      case NetworkQuality.GOOD:
        return 'high';
      case NetworkQuality.EXCELLENT:
        return 'ultra';
      default:
        return 'medium';
    }
  }

  /**
   * Set network quality
   *
   * @param quality - Network quality
   */
  public setNetworkQuality(quality: NetworkQuality): void {
    this.networkQuality = quality;
  }

  /**
   * Get load status for an asset
   *
   * @param assetId - Asset ID
   * @returns Load status
   */
  public getLoadStatus(assetId: string): AssetLoadStatus | undefined {
    return this.loadStatus.get(assetId);
  }

  /**
   * Get all load statuses
   *
   * @returns Map of asset IDs to load statuses
   */
  public getAllLoadStatuses(): Map<string, AssetLoadStatus> {
    return new Map(this.loadStatus);
  }

  /**
   * Cancel loading an asset
   *
   * @param assetId - Asset ID
   */
  public cancelLoading(assetId: string): void {
    // Remove from queues
    for (const queue of this.loadQueues.values()) {
      const index = queue.findIndex(r => r.assetId === assetId);
      if (index !== -1) {
        queue.splice(index, 1);
      }
    }

    // Clear timeout if active
    const timeout = this.activeLoads.get(assetId);
    if (timeout) {
      clearTimeout(timeout);
      this.activeLoads.delete(assetId);
    }

    // Update status
    const status = this.loadStatus.get(assetId);
    if (status) {
      this.loadStatus.set(assetId, {
        ...status,
        status: 'error',
        error: 'Loading cancelled',
        endTime: Date.now(),
      });
    }
  }
}
