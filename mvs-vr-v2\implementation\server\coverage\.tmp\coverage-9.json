{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1889", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/vendor-portal-auth-flow.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44244, "count": 1}], "isBlockCoverage": true}, {"functionName": "VendorAuthService", "ranges": [{"startOffset": 872, "endOffset": 1613, "count": 11}], "isBlockCoverage": true}, {"functionName": "login", "ranges": [{"startOffset": 1616, "endOffset": 2751, "count": 9}, {"startOffset": 1708, "endOffset": 1761, "count": 1}, {"startOffset": 1761, "endOffset": 1817, "count": 8}, {"startOffset": 1817, "endOffset": 1872, "count": 3}, {"startOffset": 1846, "endOffset": 1872, "count": 2}, {"startOffset": 1900, "endOffset": 1953, "count": 1}, {"startOffset": 1953, "endOffset": 2750, "count": 7}], "isBlockCoverage": true}, {"functionName": "refreshToken", "ranges": [{"startOffset": 2754, "endOffset": 3484, "count": 2}, {"startOffset": 2867, "endOffset": 3025, "count": 1}, {"startOffset": 3025, "endOffset": 3075, "count": 0}, {"startOffset": 3075, "endOffset": 3483, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2981, "endOffset": 3005, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateToken", "ranges": [{"startOffset": 3487, "endOffset": 3975, "count": 3}, {"startOffset": 3675, "endOffset": 3897, "count": 1}, {"startOffset": 3897, "endOffset": 3971, "count": 2}], "isBlockCoverage": true}, {"functionName": "logout", "ranges": [{"startOffset": 3978, "endOffset": 4066, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4149, "endOffset": 14538, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4240, "endOffset": 7484, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4323, "endOffset": 7271, "count": 20}, {"startOffset": 4591, "endOffset": 4663, "count": 4}, {"startOffset": 4663, "endOffset": 4762, "count": 16}, {"startOffset": 4762, "endOffset": 4795, "count": 12}, {"startOffset": 4797, "endOffset": 5362, "count": 9}, {"startOffset": 5362, "endOffset": 7265, "count": 7}, {"startOffset": 5394, "endOffset": 5429, "count": 3}, {"startOffset": 5431, "endOffset": 5997, "count": 2}, {"startOffset": 5997, "endOffset": 7265, "count": 5}, {"startOffset": 6028, "endOffset": 6064, "count": 4}, {"startOffset": 6066, "endOffset": 6758, "count": 4}, {"startOffset": 6154, "endOffset": 6163, "count": 3}, {"startOffset": 6200, "endOffset": 6366, "count": 1}, {"startOffset": 6366, "endOffset": 6758, "count": 3}, {"startOffset": 6758, "endOffset": 7265, "count": 1}, {"startOffset": 7125, "endOffset": 7265, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4845, "endOffset": 4869, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4894, "endOffset": 5352, "count": 9}, {"startOffset": 5050, "endOffset": 5180, "count": 7}, {"startOffset": 5180, "endOffset": 5342, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5479, "endOffset": 5503, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5528, "endOffset": 5987, "count": 2}, {"startOffset": 5685, "endOffset": 5977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6413, "endOffset": 6748, "count": 3}, {"startOffset": 6455, "endOffset": 6585, "count": 1}, {"startOffset": 6585, "endOffset": 6738, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6979, "endOffset": 7115, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7296, "endOffset": 7478, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7334, "endOffset": 7470, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7521, "endOffset": 7642, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7577, "endOffset": 7630, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7699, "endOffset": 9320, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7789, "endOffset": 8393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8462, "endOffset": 8852, "count": 1}, {"startOffset": 8650, "endOffset": 8659, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8921, "endOffset": 9314, "count": 1}, {"startOffset": 9112, "endOffset": 9121, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9376, "endOffset": 9878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9465, "endOffset": 9872, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9934, "endOffset": 11598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10009, "endOffset": 10764, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10834, "endOffset": 11220, "count": 1}, {"startOffset": 11032, "endOffset": 11041, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11290, "endOffset": 11592, "count": 1}, {"startOffset": 11392, "endOffset": 11401, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11656, "endOffset": 12834, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11749, "endOffset": 12390, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12461, "endOffset": 12828, "count": 1}, {"startOffset": 12624, "endOffset": 12633, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12885, "endOffset": 13836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12962, "endOffset": 13830, "count": 1}, {"startOffset": 13721, "endOffset": 13730, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13901, "endOffset": 14534, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14000, "endOffset": 14528, "count": 1}], "isBlockCoverage": true}]}]}