{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/tests/CompanyProfileStep.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38988, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38988, "count": 1}, {"startOffset": 769, "endOffset": 38987, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 349, "endOffset": 525, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 390, "endOffset": 447, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 838, "endOffset": 7886, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/CompanyProfileStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23020, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23020, "count": 1}, {"startOffset": 5697, "endOffset": 5740, "count": 0}, {"startOffset": 5741, "endOffset": 23019, "count": 0}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 2072, "endOffset": 2082, "count": 0}], "isBlockCoverage": false}, {"functionName": "data", "ranges": [{"startOffset": 2099, "endOffset": 3758, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateStepData", "ranges": [{"startOffset": 3780, "endOffset": 3906, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateField", "ranges": [{"startOffset": 3917, "endOffset": 4049, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateStep", "ranges": [{"startOffset": 4060, "endOffset": 4128, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLogoUpload", "ranges": [{"startOffset": 4139, "endOffset": 4961, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeLogo", "ranges": [{"startOffset": 4972, "endOffset": 5088, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 5369, "endOffset": 5475, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5931, "endOffset": 6020, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6153, "endOffset": 6236, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6285, "endOffset": 6329, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/CompanyProfileStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 54167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 54167, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 8777, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8928, "endOffset": 8950, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9057, "endOffset": 9088, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/CompanyProfileStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 0}], "isBlockCoverage": false}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 0}], "isBlockCoverage": false}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}