#!/usr/bin/env node

/**
 * Test Runner for MVS-VR Server Components
 *
 * This script runs all test suites and provides comprehensive reporting
 */

import { runPredictiveMonitoringTests } from './tests/predictive-monitoring-test-framework.js';
import { runBusinessMetricsTests } from './tests/business-metrics-test-framework.js';
import process from 'node:process';

// Note: business-continuity-test-framework.js needs to be converted to ES modules
// import { runBusinessContinuityTests } from './tests/business-continuity-test-framework.js';

// Test configuration
const TEST_CONFIG = {
  timeout: 120000, // 2 minutes per test suite
  verbose: true,
  exitOnFailure: false,
};

/**
 * Run all test suites
 */
async function runAllTests() {
  console.log('🚀 Starting MVS-VR Server Test Suite');
  console.log('=====================================\n');

  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    suites: [],
  };

  const testSuites = [
    {
      name: 'Predictive Monitoring',
      runner: runPredictiveMonitoringTests,
    },
    {
      name: 'Business Metrics Collection',
      runner: runBusinessMetricsTests,
    },
    // Note: Business Continuity Integration tests need ES module conversion
    // {
    //   name: 'Business Continuity Integration',
    //   runner: runBusinessContinuityTests,
    // },
  ];

  for (const suite of testSuites) {
    console.log(`\n📊 Running ${suite.name} Tests...`);
    console.log('-'.repeat(50));

    try {
      const startTime = Date.now();
      const suiteResult = await Promise.race([
        suite.runner(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Test timeout')), TEST_CONFIG.timeout),
        ),
      ]);

      const duration = Date.now() - startTime;

      results.total += suiteResult.total;
      results.passed += suiteResult.passed;
      results.failed += suiteResult.failed;

      results.suites.push({
        name: suite.name,
        ...suiteResult,
        duration,
      });

      console.log(
        `✅ ${suite.name}: ${suiteResult.passed}/${suiteResult.total} passed (${duration}ms)`,
      );

      if (suiteResult.failed > 0) {
        console.log(`❌ Failed tests in ${suite.name}:`);
        suiteResult.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`   - ${test.name}: ${test.error}`);
          });
      }
    } catch (error) {
      console.error(`❌ ${suite.name} test suite failed:`, error.message);

      results.suites.push({
        name: suite.name,
        total: 0,
        passed: 0,
        failed: 1,
        error: error.message,
        duration: 0,
      });

      results.failed++;

      if (TEST_CONFIG.exitOnFailure) {
        process.exit(1);
      }
    }
  }

  // Print final results
  console.log('\n🎯 Test Results Summary');
  console.log('=======================');
  console.log(`Total Tests: ${results.total}`);
  console.log(`Passed: ${results.passed} ✅`);
  console.log(`Failed: ${results.failed} ❌`);
  console.log(
    `Success Rate: ${results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0}%`,
  );

  // Detailed suite results
  console.log('\n📋 Suite Details:');
  results.suites.forEach(suite => {
    const status = suite.failed === 0 ? '✅' : '❌';
    console.log(`${status} ${suite.name}: ${suite.passed}/${suite.total} (${suite.duration}ms)`);
  });

  // Exit with appropriate code
  const exitCode = results.failed === 0 ? 0 : 1;
  console.log(`\n🏁 Tests completed with exit code: ${exitCode}`);

  return results;
}

/**
 * Run individual test suite
 * @param {string} suiteName - Name of test suite to run
 */
async function runSingleTest(suiteName) {
  console.log(`🎯 Running ${suiteName} test suite only\n`);

  let runner;
  switch (suiteName.toLowerCase()) {
    case 'predictive':
    case 'monitoring':
    case 'predictive-monitoring':
      runner = runPredictiveMonitoringTests;
      break;
    case 'metrics':
    case 'business-metrics':
      runner = runBusinessMetricsTests;
      break;
    // case 'continuity':
    // case 'business-continuity':
    //   runner = runBusinessContinuityTests;
    //   break;
    default:
      console.error(`❌ Unknown test suite: ${suiteName}`);
      console.log('Available suites: predictive-monitoring, business-metrics');
      process.exit(1);
  }

  try {
    const results = await runner();

    console.log(`\n✅ ${suiteName} Results:`);
    console.log(`Total: ${results.total}`);
    console.log(`Passed: ${results.passed}`);
    console.log(`Failed: ${results.failed}`);
    console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);

    if (results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          console.log(`- ${test.name}: ${test.error}`);
        });
    }

    process.exit(results.failed === 0 ? 0 : 1);
  } catch (error) {
    console.error(`❌ Test suite failed:`, error);
    process.exit(1);
  }
}

/**
 * Main execution
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    // Run all tests
    try {
      const results = await runAllTests();
      process.exit(results.failed === 0 ? 0 : 1);
    } catch (error) {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    }
  } else {
    // Run specific test suite
    await runSingleTest(args[0]);
  }
}

// Handle uncaught errors
process.on('uncaughtException', error => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('❌ Main execution failed:', error);
    process.exit(1);
  });
}

export { runAllTests, runSingleTest };
