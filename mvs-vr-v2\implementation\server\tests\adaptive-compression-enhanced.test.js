/**
 * Enhanced Adaptive Compression Tests
 * 
 * This file contains comprehensive tests for the enhanced adaptive compression service
 * including client capability detection, device-specific optimization, and performance benchmarks.
 */

const { expect } = require('chai');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { 
  compressFile, 
  detectClientCapabilities, 
  determineCompressionSettings 
} = require('../services/assets/adaptive-compression');

const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const unlinkAsync = promisify(fs.unlink);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

describe('Enhanced Adaptive Compression', () => {
  const testDir = path.join(__dirname, 'temp-enhanced');
  const testFiles = {
    small: path.join(testDir, 'small-file.txt'),
    medium: path.join(testDir, 'medium-file.txt'),
    large: path.join(testDir, 'large-file.txt'),
    veryLarge: path.join(testDir, 'very-large-file.txt')
  };
  
  before(async () => {
    // Create test directory if it doesn't exist
    if (!await existsAsync(testDir)) {
      await mkdirAsync(testDir, { recursive: true });
    }
    
    // Create test files of different sizes
    const smallContent = 'Small test content';
    const mediumContent = Array(1000).fill(0).map(() => Math.random().toString(36).substring(2)).join('');
    const largeContent = Array(10000).fill(0).map(() => Math.random().toString(36).substring(2)).join('');
    const veryLargeContent = Array(100000).fill(0).map(() => Math.random().toString(36).substring(2)).join('');
    
    await writeFileAsync(testFiles.small, smallContent, 'utf8');
    await writeFileAsync(testFiles.medium, mediumContent, 'utf8');
    await writeFileAsync(testFiles.large, largeContent, 'utf8');
    await writeFileAsync(testFiles.veryLarge, veryLargeContent, 'utf8');
  });
  
  after(async () => {
    // Clean up test files
    for (const filePath of Object.values(testFiles)) {
      if (await existsAsync(filePath)) {
        await unlinkAsync(filePath);
      }
      
      const compressedPath = filePath + '.gz';
      if (await existsAsync(compressedPath)) {
        await unlinkAsync(compressedPath);
      }
    }
  });
  
  describe('Enhanced Client Capability Detection', () => {
    it('should detect mobile device capabilities', () => {
      const headers = {
        'accept-encoding': 'gzip, deflate',
        'device-memory': '2',
        'ect': '3g',
        'viewport-width': '375',
        'user-agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
      };
      
      const capabilities = detectClientCapabilities(headers);
      
      expect(capabilities.deviceType).to.equal('mobile');
      expect(capabilities.level).to.equal('low');
      expect(capabilities.memoryLimit).to.equal('low');
      expect(capabilities.networkQuality).to.equal('medium');
      expect(capabilities.maxTextureSize).to.equal(1024);
    });
    
    it('should detect high-end desktop capabilities', () => {
      const headers = {
        'accept-encoding': 'gzip, deflate, br',
        'device-memory': '16',
        'ect': '4g',
        'viewport-width': '1920',
        'dpr': '2',
        'hardware-concurrency': '8',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/91.0.4472.124'
      };
      
      const capabilities = detectClientCapabilities(headers);
      
      expect(capabilities.deviceType).to.equal('desktop');
      expect(capabilities.level).to.equal('high');
      expect(capabilities.memoryLimit).to.equal('high');
      expect(capabilities.networkQuality).to.equal('high');
      expect(capabilities.maxTextureSize).to.equal(8192);
      expect(capabilities.webGL2Support).to.be.true;
      expect(capabilities.hardwareConcurrency).to.equal(8);
    });
    
    it('should detect Save-Data preference', () => {
      const headers = {
        'accept-encoding': 'gzip, deflate, br',
        'save-data': 'on',
        'device-memory': '4',
        'ect': '4g'
      };
      
      const capabilities = detectClientCapabilities(headers);
      
      expect(capabilities.saveData).to.be.true;
      expect(capabilities.level).to.equal('low'); // Should be low due to Save-Data
    });
  });
  
  describe('Enhanced Compression Settings', () => {
    it('should optimize for mobile devices', () => {
      const clientCapabilities = {
        level: 'low',
        deviceType: 'mobile',
        memoryLimit: 'low',
        networkQuality: 'medium',
        supportedAlgorithms: ['gzip', 'deflate'],
        saveData: false,
        webGLSupport: true,
        hardwareConcurrency: 4
      };
      
      const settings = determineCompressionSettings('application/json', clientCapabilities, 10 * 1024 * 1024);
      
      expect(settings.quality).to.equal('low');
      expect(settings.level).to.be.at.most(4);
      expect(settings.enableProgressive).to.be.true; // Large file with WebGL support
      expect(settings.chunkSize).to.equal(512 * 1024); // Small chunks for low memory
    });
    
    it('should optimize for high-end desktop', () => {
      const clientCapabilities = {
        level: 'high',
        deviceType: 'desktop',
        memoryLimit: 'high',
        networkQuality: 'high',
        screenResolution: 'high',
        supportedAlgorithms: ['gzip', 'deflate', 'brotli'],
        saveData: false,
        webGLSupport: true,
        webGL2Support: true,
        hardwareConcurrency: 8,
        cpuPower: 'high'
      };
      
      const settings = determineCompressionSettings('application/json', clientCapabilities, 10 * 1024 * 1024);
      
      expect(settings.quality).to.equal('high');
      expect(settings.level).to.be.at.least(6);
      expect(settings.enableProgressive).to.be.true;
      expect(settings.chunkSize).to.equal(2 * 1024 * 1024); // Large chunks for high memory
    });
    
    it('should handle Save-Data preference', () => {
      const clientCapabilities = {
        level: 'medium',
        deviceType: 'desktop',
        memoryLimit: 'medium',
        networkQuality: 'medium',
        supportedAlgorithms: ['gzip', 'deflate', 'brotli'],
        saveData: true,
        webGLSupport: true,
        hardwareConcurrency: 4
      };
      
      const settings = determineCompressionSettings('application/json', clientCapabilities, 5 * 1024 * 1024);
      
      expect(settings.quality).to.equal('low');
      expect(settings.level).to.be.at.least(6); // Higher compression for data savings
      expect(settings.enableProgressive).to.be.true;
      expect(settings.chunkSize).to.equal(256 * 1024); // Small chunks for data savings
    });
    
    it('should enable streaming for very large files', () => {
      const clientCapabilities = {
        level: 'high',
        deviceType: 'desktop',
        memoryLimit: 'high',
        networkQuality: 'high',
        supportedAlgorithms: ['gzip', 'deflate', 'brotli'],
        saveData: false,
        webGLSupport: true,
        hardwareConcurrency: 8
      };
      
      const settings = determineCompressionSettings('application/json', clientCapabilities, 100 * 1024 * 1024);
      
      expect(settings.enableStreaming).to.be.true;
      expect(settings.chunkSize).to.be.at.least(2 * 1024 * 1024);
    });
  });
  
  describe('Performance Benchmarks', () => {
    it('should compress files efficiently for different device types', async function() {
      this.timeout(10000); // Increase timeout for performance tests
      
      const deviceTypes = [
        {
          name: 'mobile',
          capabilities: {
            level: 'low',
            deviceType: 'mobile',
            memoryLimit: 'low',
            networkQuality: 'medium',
            supportedAlgorithms: ['gzip', 'deflate'],
            saveData: false,
            webGLSupport: true,
            hardwareConcurrency: 2,
            cpuPower: 'low'
          }
        },
        {
          name: 'desktop',
          capabilities: {
            level: 'high',
            deviceType: 'desktop',
            memoryLimit: 'high',
            networkQuality: 'high',
            supportedAlgorithms: ['gzip', 'deflate', 'brotli'],
            saveData: false,
            webGLSupport: true,
            webGL2Support: true,
            hardwareConcurrency: 8,
            cpuPower: 'high'
          }
        }
      ];
      
      for (const device of deviceTypes) {
        const result = await compressFile(testFiles.large, testFiles.large + '.gz', {
          mimeType: 'text/plain',
          clientCapabilities: device.capabilities
        });
        
        expect(result.success).to.be.true;
        expect(result.compressionRatio).to.be.greaterThan(1);
        expect(result.duration).to.be.a('number');
        
        // Mobile should use lower compression levels (faster)
        if (device.name === 'mobile') {
          expect(result.duration).to.be.lessThan(5); // Should be fast
        }
        
        console.log(`${device.name} compression: ${result.compressionRatio.toFixed(2)}x in ${result.duration.toFixed(3)}s`);
      }
    });
  });
});
