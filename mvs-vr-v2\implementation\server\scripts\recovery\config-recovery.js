/**
 * Configuration Recovery Script
 * 
 * This script handles recovery of configuration files and settings.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { execSync } = require('child_process');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const existsAsync = promisify(fs.exists);
const mkdirAsync = promisify(fs.mkdir);

const logger = require('../../utils/logger').getLogger('config-recovery');

// Configuration
const config = {
  backupPath: process.env.CONFIG_BACKUP_PATH || '/backups/config',
  configPath: process.env.CONFIG_PATH || path.join(__dirname, '../../config'),
  tempPath: path.join(__dirname, '../../temp/config-recovery')
};

/**
 * Recover configuration from backup
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverConfiguration(options = {}) {
  const {
    source = null,
    target = config.configPath,
    test = false
  } = options;
  
  logger.info('Starting configuration recovery');
  
  try {
    // Ensure target directory exists
    if (!await existsAsync(target)) {
      await mkdirAsync(target, { recursive: true });
    }
    
    // Ensure temp directory exists
    if (!await existsAsync(config.tempPath)) {
      await mkdirAsync(config.tempPath, { recursive: true });
    }
    
    let backupSource = source;
    
    // If no source specified, find latest backup
    if (!backupSource) {
      backupSource = await findLatestBackup();
    }
    
    if (!backupSource) {
      throw new Error('No configuration backup found');
    }
    
    logger.info(`Recovering configuration from: ${backupSource}`);
    
    // Extract backup if it's an archive
    let extractedPath = backupSource;
    if (backupSource.endsWith('.tar.gz') || backupSource.endsWith('.zip')) {
      extractedPath = await extractBackup(backupSource);
    }
    
    // Copy configuration files
    const recoveredFiles = await copyConfigurationFiles(extractedPath, target, test);
    
    // Validate recovered configuration
    const validation = await validateConfiguration(target);
    
    logger.info(`Configuration recovery completed. Recovered ${recoveredFiles.length} files`);
    
    return {
      success: true,
      source: backupSource,
      target,
      recoveredFiles,
      validation
    };
    
  } catch (error) {
    logger.error(`Configuration recovery failed: ${error.message}`, { error });
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Find latest configuration backup
 * @returns {Promise<string|null>} Latest backup path
 */
async function findLatestBackup() {
  try {
    if (!await existsAsync(config.backupPath)) {
      return null;
    }
    
    // List backup files
    const files = await fs.promises.readdir(config.backupPath);
    const configBackups = files
      .filter(file => file.startsWith('config-') && (file.endsWith('.tar.gz') || file.endsWith('.zip')))
      .map(file => ({
        name: file,
        path: path.join(config.backupPath, file),
        mtime: fs.statSync(path.join(config.backupPath, file)).mtime
      }))
      .sort((a, b) => b.mtime - a.mtime);
    
    return configBackups.length > 0 ? configBackups[0].path : null;
  } catch (error) {
    logger.error(`Failed to find latest backup: ${error.message}`);
    return null;
  }
}

/**
 * Extract backup archive
 * @param {string} backupPath - Backup file path
 * @returns {Promise<string>} Extracted directory path
 */
async function extractBackup(backupPath) {
  const extractDir = path.join(config.tempPath, `extract-${Date.now()}`);
  
  try {
    await mkdirAsync(extractDir, { recursive: true });
    
    if (backupPath.endsWith('.tar.gz')) {
      execSync(`tar -xzf "${backupPath}" -C "${extractDir}"`, { stdio: 'inherit' });
    } else if (backupPath.endsWith('.zip')) {
      execSync(`unzip -q "${backupPath}" -d "${extractDir}"`, { stdio: 'inherit' });
    }
    
    logger.info(`Extracted backup to: ${extractDir}`);
    return extractDir;
  } catch (error) {
    logger.error(`Failed to extract backup: ${error.message}`);
    throw error;
  }
}

/**
 * Copy configuration files from source to target
 * @param {string} source - Source directory
 * @param {string} target - Target directory
 * @param {boolean} test - Test mode
 * @returns {Promise<Array>} List of copied files
 */
async function copyConfigurationFiles(source, target, test = false) {
  const copiedFiles = [];
  
  try {
    // Find all configuration files in source
    const configFiles = await findConfigurationFiles(source);
    
    for (const file of configFiles) {
      const relativePath = path.relative(source, file);
      const targetPath = path.join(target, relativePath);
      const targetDir = path.dirname(targetPath);
      
      // Ensure target directory exists
      if (!await existsAsync(targetDir)) {
        await mkdirAsync(targetDir, { recursive: true });
      }
      
      if (test) {
        logger.info(`[TEST] Would copy: ${file} -> ${targetPath}`);
      } else {
        // Copy file
        await fs.promises.copyFile(file, targetPath);
        logger.debug(`Copied: ${relativePath}`);
      }
      
      copiedFiles.push(relativePath);
    }
    
    return copiedFiles;
  } catch (error) {
    logger.error(`Failed to copy configuration files: ${error.message}`);
    throw error;
  }
}

/**
 * Find all configuration files in directory
 * @param {string} directory - Directory to search
 * @returns {Promise<Array>} List of configuration files
 */
async function findConfigurationFiles(directory) {
  const configFiles = [];
  
  async function searchDirectory(dir) {
    const items = await fs.promises.readdir(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = await fs.promises.stat(itemPath);
      
      if (stats.isDirectory()) {
        await searchDirectory(itemPath);
      } else if (stats.isFile()) {
        // Check if it's a configuration file
        const ext = path.extname(item).toLowerCase();
        const name = path.basename(item, ext).toLowerCase();
        
        if (ext === '.json' || ext === '.yaml' || ext === '.yml' || ext === '.conf' || ext === '.config' ||
            name.includes('config') || name.includes('settings') || name.includes('env')) {
          configFiles.push(itemPath);
        }
      }
    }
  }
  
  await searchDirectory(directory);
  return configFiles;
}

/**
 * Validate recovered configuration
 * @param {string} configPath - Configuration directory path
 * @returns {Promise<Object>} Validation result
 */
async function validateConfiguration(configPath) {
  const validation = {
    valid: true,
    errors: [],
    warnings: [],
    files: []
  };
  
  try {
    const configFiles = await findConfigurationFiles(configPath);
    
    for (const file of configFiles) {
      const fileValidation = await validateConfigurationFile(file);
      validation.files.push(fileValidation);
      
      if (!fileValidation.valid) {
        validation.valid = false;
        validation.errors.push(...fileValidation.errors);
      }
      
      validation.warnings.push(...fileValidation.warnings);
    }
    
    return validation;
  } catch (error) {
    validation.valid = false;
    validation.errors.push(`Validation failed: ${error.message}`);
    return validation;
  }
}

/**
 * Validate a single configuration file
 * @param {string} filePath - Configuration file path
 * @returns {Promise<Object>} File validation result
 */
async function validateConfigurationFile(filePath) {
  const validation = {
    file: filePath,
    valid: true,
    errors: [],
    warnings: []
  };
  
  try {
    const content = await readFileAsync(filePath, 'utf8');
    const ext = path.extname(filePath).toLowerCase();
    
    if (ext === '.json') {
      try {
        JSON.parse(content);
      } catch (parseError) {
        validation.valid = false;
        validation.errors.push(`Invalid JSON: ${parseError.message}`);
      }
    } else if (ext === '.yaml' || ext === '.yml') {
      // YAML validation would require a YAML parser
      // For now, just check if file is readable
      if (content.trim().length === 0) {
        validation.warnings.push('YAML file is empty');
      }
    }
    
    // Check file permissions
    const stats = await fs.promises.stat(filePath);
    if (stats.mode & 0o077) {
      validation.warnings.push('Configuration file has overly permissive permissions');
    }
    
  } catch (error) {
    validation.valid = false;
    validation.errors.push(`Failed to validate file: ${error.message}`);
  }
  
  return validation;
}

// If script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    source: args.find(arg => arg.startsWith('--source='))?.split('=')[1],
    target: args.find(arg => arg.startsWith('--target='))?.split('=')[1]
  };
  
  recoverConfiguration(options)
    .then(result => {
      if (result.success) {
        console.log('Configuration recovery completed successfully');
        console.log(`Recovered ${result.recoveredFiles.length} files`);
        process.exit(0);
      } else {
        console.error('Configuration recovery failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  recoverConfiguration,
  findLatestBackup,
  validateConfiguration
};
