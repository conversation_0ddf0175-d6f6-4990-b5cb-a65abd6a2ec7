/**
 * Business Metrics Collector
 *
 * This service collects, aggregates, and analyzes business metrics from various sources
 * to provide comprehensive business intelligence and performance insights.
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { EventEmitter } from 'events';
import { fileURLToPath } from 'url';

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple logger for services
const logger = {
  info: msg => console.log(`[INFO] ${msg}`),
  error: msg => console.error(`[ERROR] ${msg}`),
  debug: msg => console.log(`[DEBUG] ${msg}`),
  warn: msg => console.warn(`[WARN] ${msg}`),
};

// Metric types
const METRIC_TYPES = {
  COUNTER: 'counter',
  GAUGE: 'gauge',
  HISTOGRAM: 'histogram',
  SUMMARY: 'summary',
  RATE: 'rate',
};

// Metric categories
const METRIC_CATEGORIES = {
  REVENUE: 'revenue',
  CUSTOMER: 'customer',
  OPERATIONAL: 'operational',
  PERFORMANCE: 'performance',
  QUALITY: 'quality',
  ENGAGEMENT: 'engagement',
};

// Aggregation methods
const AGGREGATION_METHODS = {
  SUM: 'sum',
  AVERAGE: 'average',
  MIN: 'min',
  MAX: 'max',
  COUNT: 'count',
  PERCENTILE: 'percentile',
};

class BusinessMetricsCollector extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      configPath: options.configPath || path.join(__dirname, '../../config/business-metrics.json'),
      outputPath: options.outputPath || path.join(__dirname, '../../data/business-metrics'),
      collectionInterval: options.collectionInterval || 300000, // 5 minutes
      retentionDays: options.retentionDays || 90,
      aggregationIntervals: options.aggregationIntervals || ['1h', '1d', '1w', '1M'],
      ...options,
    };

    // Metrics storage
    this.metrics = new Map();
    this.metricDefinitions = new Map();
    this.aggregatedMetrics = new Map();
    this.metricHistory = [];

    // Data sources
    this.dataSources = new Map();

    // Collection state
    this.collectionInterval = null;
    this.isCollecting = false;

    // Statistics
    this.stats = {
      totalMetrics: 0,
      metricsCollected: 0,
      lastCollection: null,
      collectionErrors: 0,
      startTime: Date.now(),
    };

    this.initialize();
  }

  /**
   * Initialize the metrics collector
   */
  async initialize() {
    try {
      // Ensure output directory exists
      if (!(await existsAsync(this.options.outputPath))) {
        await mkdirAsync(this.options.outputPath, { recursive: true });
      }

      // Load metric definitions
      await this.loadMetricDefinitions();

      // Initialize data sources
      this.initializeDataSources();

      logger.info('Business metrics collector initialized');
    } catch (error) {
      logger.error('Failed to initialize business metrics collector:', error);
      throw error;
    }
  }

  /**
   * Load metric definitions from configuration
   */
  async loadMetricDefinitions() {
    try {
      const configData = await readFileAsync(this.options.configPath, 'utf8');
      const config = JSON.parse(configData);

      // Load metric definitions
      for (const metricConfig of config.metrics || []) {
        const metric = {
          id: metricConfig.id,
          name: metricConfig.name,
          description: metricConfig.description,
          type: metricConfig.type || METRIC_TYPES.GAUGE,
          category: metricConfig.category || METRIC_CATEGORIES.OPERATIONAL,
          unit: metricConfig.unit || 'count',
          source: metricConfig.source,
          query: metricConfig.query,
          aggregation: metricConfig.aggregation || AGGREGATION_METHODS.AVERAGE,
          tags: metricConfig.tags || [],
          thresholds: metricConfig.thresholds || {},
          businessImpact: metricConfig.businessImpact || 'medium',
        };

        this.metricDefinitions.set(metric.id, metric);
      }

      this.stats.totalMetrics = this.metricDefinitions.size;
      logger.info(`Loaded ${this.metricDefinitions.size} metric definitions`);
    } catch (error) {
      logger.error('Failed to load metric definitions:', error);
      throw error;
    }
  }

  /**
   * Initialize data sources
   */
  initializeDataSources() {
    // Database metrics source
    this.dataSources.set('database', {
      type: 'database',
      connection: null, // Will be set when database is available
      queries: new Map(),
    });

    // API metrics source
    this.dataSources.set('api', {
      type: 'api',
      endpoints: new Map(),
      cache: new Map(),
    });

    // System metrics source
    this.dataSources.set('system', {
      type: 'system',
      collectors: new Map(),
    });

    // External metrics source
    this.dataSources.set('external', {
      type: 'external',
      apis: new Map(),
    });

    logger.debug('Initialized data sources');
  }

  /**
   * Start metrics collection
   */
  startCollection() {
    if (this.isCollecting) {
      logger.warn('Metrics collection is already running');
      return;
    }

    this.isCollecting = true;

    // Initial collection
    this.collectMetrics();

    // Schedule regular collection
    this.collectionInterval = setInterval(() => {
      this.collectMetrics();
    }, this.options.collectionInterval);

    logger.info('Started business metrics collection');
  }

  /**
   * Stop metrics collection
   */
  stopCollection() {
    if (!this.isCollecting) {
      return;
    }

    this.isCollecting = false;

    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
    }

    logger.info('Stopped business metrics collection');
  }

  /**
   * Collect all metrics
   */
  async collectMetrics() {
    const startTime = Date.now();
    const collectedMetrics = [];

    try {
      // Collect metrics from each definition
      for (const [metricId, definition] of this.metricDefinitions.entries()) {
        try {
          const value = await this.collectMetric(definition);

          const metric = {
            id: metricId,
            name: definition.name,
            value,
            timestamp: new Date().toISOString(),
            type: definition.type,
            category: definition.category,
            unit: definition.unit,
            tags: definition.tags,
            businessImpact: definition.businessImpact,
          };

          // Store metric
          this.metrics.set(metricId, metric);
          collectedMetrics.push(metric);

          // Add to history
          this.metricHistory.push(metric);

          // Emit metric collected event
          this.emit('metricCollected', metric);
        } catch (error) {
          logger.error(`Error collecting metric ${metricId}:`, error);
          this.stats.collectionErrors++;
        }
      }

      // Aggregate metrics
      await this.aggregateMetrics(collectedMetrics);

      // Clean up old data
      this.cleanupOldData();

      // Update statistics
      this.stats.metricsCollected += collectedMetrics.length;
      this.stats.lastCollection = new Date().toISOString();

      const collectionTime = Date.now() - startTime;
      logger.debug(`Collected ${collectedMetrics.length} metrics in ${collectionTime}ms`);

      // Emit collection complete event
      this.emit('collectionComplete', {
        metricsCount: collectedMetrics.length,
        collectionTime,
        timestamp: this.stats.lastCollection,
      });
    } catch (error) {
      logger.error('Error during metrics collection:', error);
      this.stats.collectionErrors++;
    }
  }

  /**
   * Collect a specific metric
   * @param {Object} definition - Metric definition
   * @returns {Promise<number>} Metric value
   */
  async collectMetric(definition) {
    switch (definition.source) {
      case 'database':
        return this.collectDatabaseMetric(definition);
      case 'api':
        return this.collectAPIMetric(definition);
      case 'system':
        return this.collectSystemMetric(definition);
      case 'external':
        return this.collectExternalMetric(definition);
      case 'calculated':
        return this.collectCalculatedMetric(definition);
      default:
        throw new Error(`Unknown metric source: ${definition.source}`);
    }
  }

  /**
   * Collect database metric
   * @param {Object} definition - Metric definition
   * @returns {Promise<number>} Metric value
   */
  async collectDatabaseMetric(definition) {
    // Placeholder implementation - would connect to actual database
    // For now, return simulated values based on metric type

    switch (definition.id) {
      case 'total_users':
        return Math.floor(Math.random() * 1000) + 5000;
      case 'active_sessions':
        return Math.floor(Math.random() * 500) + 100;
      case 'database_connections':
        return Math.floor(Math.random() * 50) + 10;
      case 'query_response_time':
        return Math.random() * 100 + 50; // 50-150ms
      default:
        return Math.random() * 100;
    }
  }

  /**
   * Collect API metric
   * @param {Object} definition - Metric definition
   * @returns {Promise<number>} Metric value
   */
  async collectAPIMetric(definition) {
    // Placeholder implementation - would collect from API monitoring

    switch (definition.id) {
      case 'api_requests_per_minute':
        return Math.floor(Math.random() * 1000) + 500;
      case 'api_response_time':
        return Math.random() * 500 + 100; // 100-600ms
      case 'api_error_rate':
        return Math.random() * 5; // 0-5%
      case 'api_success_rate':
        return 95 + Math.random() * 5; // 95-100%
      default:
        return Math.random() * 100;
    }
  }

  /**
   * Collect system metric
   * @param {Object} definition - Metric definition
   * @returns {Promise<number>} Metric value
   */
  async collectSystemMetric(definition) {
    // Placeholder implementation - would collect from system monitoring

    switch (definition.id) {
      case 'cpu_usage':
        return Math.random() * 80 + 10; // 10-90%
      case 'memory_usage':
        return Math.random() * 70 + 20; // 20-90%
      case 'disk_usage':
        return Math.random() * 60 + 30; // 30-90%
      case 'network_throughput':
        return Math.random() * 1000 + 100; // 100-1100 Mbps
      default:
        return Math.random() * 100;
    }
  }

  /**
   * Collect external metric
   * @param {Object} definition - Metric definition
   * @returns {Promise<number>} Metric value
   */
  async collectExternalMetric(definition) {
    // Placeholder implementation - would call external APIs

    switch (definition.id) {
      case 'external_service_uptime':
        return 95 + Math.random() * 5; // 95-100%
      case 'third_party_api_calls':
        return Math.floor(Math.random() * 10000) + 1000;
      default:
        return Math.random() * 100;
    }
  }

  /**
   * Collect calculated metric
   * @param {Object} definition - Metric definition
   * @returns {Promise<number>} Metric value
   */
  async collectCalculatedMetric(definition) {
    // Calculate metrics based on other metrics

    switch (definition.id) {
      case 'user_engagement_score':
        const activeSessions = this.metrics.get('active_sessions')?.value || 100;
        const totalUsers = this.metrics.get('total_users')?.value || 5000;
        return (activeSessions / totalUsers) * 100;

      case 'system_health_score':
        const cpuUsage = this.metrics.get('cpu_usage')?.value || 50;
        const memoryUsage = this.metrics.get('memory_usage')?.value || 50;
        const diskUsage = this.metrics.get('disk_usage')?.value || 50;
        return 100 - (cpuUsage + memoryUsage + diskUsage) / 3;

      case 'business_performance_index':
        const apiSuccessRate = this.metrics.get('api_success_rate')?.value || 95;
        const systemHealth = this.metrics.get('system_health_score')?.value || 70;
        const userEngagement = this.metrics.get('user_engagement_score')?.value || 10;
        return apiSuccessRate * 0.4 + systemHealth * 0.4 + userEngagement * 0.2;

      default:
        return Math.random() * 100;
    }
  }

  /**
   * Aggregate metrics over different time intervals
   * @param {Array<Object>} metrics - Collected metrics
   */
  async aggregateMetrics(metrics) {
    const timestamp = new Date().toISOString();

    for (const interval of this.options.aggregationIntervals) {
      const aggregationKey = `${interval}-${this.getIntervalKey(interval)}`;

      if (!this.aggregatedMetrics.has(aggregationKey)) {
        this.aggregatedMetrics.set(aggregationKey, {
          interval,
          timestamp,
          metrics: {},
        });
      }

      const aggregation = this.aggregatedMetrics.get(aggregationKey);

      // Aggregate each metric
      for (const metric of metrics) {
        if (!aggregation.metrics[metric.id]) {
          aggregation.metrics[metric.id] = {
            values: [],
            sum: 0,
            count: 0,
            min: Infinity,
            max: -Infinity,
          };
        }

        const metricAgg = aggregation.metrics[metric.id];
        metricAgg.values.push(metric.value);
        metricAgg.sum += metric.value;
        metricAgg.count++;
        metricAgg.min = Math.min(metricAgg.min, metric.value);
        metricAgg.max = Math.max(metricAgg.max, metric.value);
        metricAgg.average = metricAgg.sum / metricAgg.count;
      }
    }
  }

  /**
   * Get interval key for aggregation
   * @param {string} interval - Interval string (e.g., '1h', '1d')
   * @returns {string} Interval key
   */
  getIntervalKey(interval) {
    const now = new Date();

    switch (interval) {
      case '1h':
        return `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}`;
      case '1d':
        return `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;
      case '1w':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
        return `${weekStart.getFullYear()}-W${Math.ceil(weekStart.getDate() / 7)}`;
      case '1M':
        return `${now.getFullYear()}-${now.getMonth()}`;
      default:
        return now.toISOString().split('T')[0];
    }
  }

  /**
   * Clean up old data based on retention policy
   */
  cleanupOldData() {
    const cutoffTime = Date.now() - this.options.retentionDays * 24 * 60 * 60 * 1000;

    // Clean up metric history
    this.metricHistory = this.metricHistory.filter(metric => {
      return new Date(metric.timestamp).getTime() > cutoffTime;
    });

    // Clean up aggregated metrics
    for (const [key, aggregation] of this.aggregatedMetrics.entries()) {
      if (new Date(aggregation.timestamp).getTime() < cutoffTime) {
        this.aggregatedMetrics.delete(key);
      }
    }
  }

  /**
   * Add custom metric
   * @param {string} metricId - Metric ID
   * @param {number} value - Metric value
   * @param {Object} metadata - Additional metadata
   */
  addCustomMetric(metricId, value, metadata = {}) {
    const metric = {
      id: metricId,
      name: metadata.name || metricId,
      value,
      timestamp: new Date().toISOString(),
      type: metadata.type || METRIC_TYPES.GAUGE,
      category: metadata.category || METRIC_CATEGORIES.OPERATIONAL,
      unit: metadata.unit || 'count',
      tags: metadata.tags || [],
      businessImpact: metadata.businessImpact || 'medium',
      custom: true,
    };

    this.metrics.set(metricId, metric);
    this.metricHistory.push(metric);

    this.emit('customMetricAdded', metric);

    logger.debug(`Added custom metric: ${metricId} = ${value}`);
  }

  /**
   * Get current metrics
   * @param {string} category - Filter by category (optional)
   * @returns {Array<Object>} Current metrics
   */
  getCurrentMetrics(category = null) {
    const metrics = Array.from(this.metrics.values());

    if (category) {
      return metrics.filter(metric => metric.category === category);
    }

    return metrics;
  }

  /**
   * Get metric history
   * @param {string} metricId - Metric ID (optional)
   * @param {number} limit - Maximum number of records
   * @returns {Array<Object>} Metric history
   */
  getMetricHistory(metricId = null, limit = 100) {
    let history = this.metricHistory;

    if (metricId) {
      history = history.filter(metric => metric.id === metricId);
    }

    return history.slice(-limit).sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  /**
   * Get aggregated metrics
   * @param {string} interval - Aggregation interval
   * @returns {Object} Aggregated metrics
   */
  getAggregatedMetrics(interval) {
    const aggregations = {};

    for (const [key, aggregation] of this.aggregatedMetrics.entries()) {
      if (aggregation.interval === interval) {
        aggregations[key] = aggregation;
      }
    }

    return aggregations;
  }

  /**
   * Get business insights
   * @returns {Object} Business insights
   */
  getBusinessInsights() {
    const insights = {
      performance: {},
      trends: {},
      alerts: [],
      recommendations: [],
    };

    // Calculate performance indicators
    const currentMetrics = this.getCurrentMetrics();

    for (const metric of currentMetrics) {
      const definition = this.metricDefinitions.get(metric.id);

      if (definition && definition.thresholds) {
        // Check thresholds
        if (definition.thresholds.critical && metric.value >= definition.thresholds.critical) {
          insights.alerts.push({
            metric: metric.id,
            level: 'critical',
            value: metric.value,
            threshold: definition.thresholds.critical,
            message: `${metric.name} is at critical level`,
          });
        } else if (definition.thresholds.warning && metric.value >= definition.thresholds.warning) {
          insights.alerts.push({
            metric: metric.id,
            level: 'warning',
            value: metric.value,
            threshold: definition.thresholds.warning,
            message: `${metric.name} is above warning threshold`,
          });
        }
      }

      insights.performance[metric.id] = {
        current: metric.value,
        category: metric.category,
        businessImpact: metric.businessImpact,
      };
    }

    // Generate recommendations
    if (insights.alerts.length > 0) {
      insights.recommendations.push('Review critical and warning alerts');
    }

    if (insights.performance.system_health_score?.current < 70) {
      insights.recommendations.push('System health is below optimal levels');
    }

    if (insights.performance.user_engagement_score?.current < 5) {
      insights.recommendations.push('User engagement is low, consider engagement initiatives');
    }

    return insights;
  }

  /**
   * Export metrics data
   * @param {Object} options - Export options
   * @returns {Promise<string>} Export file path
   */
  async exportMetrics(options = {}) {
    const { format = 'json', startDate = null, endDate = null, metrics = null } = options;

    let data = this.metricHistory;

    // Filter by date range
    if (startDate) {
      data = data.filter(metric => new Date(metric.timestamp) >= new Date(startDate));
    }

    if (endDate) {
      data = data.filter(metric => new Date(metric.timestamp) <= new Date(endDate));
    }

    // Filter by specific metrics
    if (metrics && Array.isArray(metrics)) {
      data = data.filter(metric => metrics.includes(metric.id));
    }

    // Format data
    let exportData;
    let fileName;

    switch (format) {
      case 'csv':
        exportData = this.formatAsCSV(data);
        fileName = `metrics-export-${Date.now()}.csv`;
        break;
      case 'json':
      default:
        exportData = JSON.stringify(data, null, 2);
        fileName = `metrics-export-${Date.now()}.json`;
    }

    // Save to file
    const filePath = path.join(this.options.outputPath, fileName);
    await writeFileAsync(filePath, exportData);

    logger.info(`Exported ${data.length} metrics to ${fileName}`);

    return filePath;
  }

  /**
   * Format data as CSV
   * @param {Array<Object>} data - Metrics data
   * @returns {string} CSV formatted data
   */
  formatAsCSV(data) {
    if (data.length === 0) {
      return '';
    }

    const headers = [
      'id',
      'name',
      'value',
      'timestamp',
      'type',
      'category',
      'unit',
      'businessImpact',
    ];
    const csvRows = [headers.join(',')];

    for (const metric of data) {
      const row = headers.map(header => {
        const value = metric[header] || '';
        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
      });
      csvRows.push(row.join(','));
    }

    return csvRows.join('\n');
  }

  /**
   * Get statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      currentMetricsCount: this.metrics.size,
      historySize: this.metricHistory.length,
      aggregationsCount: this.aggregatedMetrics.size,
      isCollecting: this.isCollecting,
    };
  }

  /**
   * Shutdown the collector
   */
  shutdown() {
    this.stopCollection();
    logger.info('Business metrics collector shutdown');
  }
}

export { BusinessMetricsCollector, METRIC_TYPES, METRIC_CATEGORIES, AGGREGATION_METHODS };
