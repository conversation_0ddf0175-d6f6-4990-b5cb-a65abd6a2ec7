#!/usr/bin/env node

/**
 * Simple Module Test
 * 
 * This script tests if all modules can be imported and basic functionality works
 */

import process from 'node:process';

async function testModules() {
  console.log('🧪 Testing Module Imports...\n');
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    tests: []
  };
  
  // Test 1: Predictive Monitoring Services
  try {
    console.log('📊 Testing Predictive Monitoring Services...');
    
    const { EnhancedAnomalyDetectionService, ALGORITHMS } = await import('./services/monitoring/enhanced-anomaly-detection.js');
    const { PredictiveAlertManager } = await import('./services/monitoring/predictive-alert-manager.js');
    const { PredictiveMonitoringService } = await import('./services/monitoring/predictive-monitoring-service.js');
    
    // Test basic instantiation
    const anomalyService = new EnhancedAnomalyDetectionService();
    const alertManager = new PredictiveAlertManager();
    const monitoringService = new PredictiveMonitoringService();
    
    console.log('✅ Predictive Monitoring Services imported and instantiated successfully');
    console.log(`   - Available algorithms: ${Object.keys(ALGORITHMS).length}`);
    console.log(`   - Anomaly service initialized: ${anomalyService !== null}`);
    console.log(`   - Alert manager initialized: ${alertManager !== null}`);
    console.log(`   - Monitoring service initialized: ${monitoringService !== null}`);
    
    results.passed++;
    results.tests.push({ name: 'Predictive Monitoring Services', status: 'PASSED' });
    
  } catch (error) {
    console.error('❌ Predictive Monitoring Services failed:', error.message);
    results.failed++;
    results.tests.push({ name: 'Predictive Monitoring Services', status: 'FAILED', error: error.message });
  }
  
  results.total++;
  
  // Test 2: Business Metrics Services
  try {
    console.log('\n📈 Testing Business Metrics Services...');
    
    const { BusinessMetricsCollector, METRIC_TYPES } = await import('./services/metrics/business-metrics-collector.js');
    const { BusinessMetricsAnalytics, TREND_DIRECTIONS } = await import('./services/metrics/business-metrics-analytics.js');
    
    // Test basic instantiation
    const metricsCollector = new BusinessMetricsCollector({
      configPath: './config/business-metrics.json',
      outputPath: './temp/test-metrics',
      collectionInterval: 60000
    });
    
    const metricsAnalytics = new BusinessMetricsAnalytics({
      analysisInterval: 120000
    });
    
    // Set up service references
    metricsAnalytics.setMetricsCollector(metricsCollector);
    
    console.log('✅ Business Metrics Services imported and instantiated successfully');
    console.log(`   - Available metric types: ${Object.keys(METRIC_TYPES).length}`);
    console.log(`   - Available trend directions: ${Object.keys(TREND_DIRECTIONS).length}`);
    console.log(`   - Metrics collector initialized: ${metricsCollector !== null}`);
    console.log(`   - Analytics service initialized: ${metricsAnalytics !== null}`);
    
    results.passed++;
    results.tests.push({ name: 'Business Metrics Services', status: 'PASSED' });
    
  } catch (error) {
    console.error('❌ Business Metrics Services failed:', error.message);
    results.failed++;
    results.tests.push({ name: 'Business Metrics Services', status: 'FAILED', error: error.message });
  }
  
  results.total++;
  
  // Test 3: Test Framework Imports
  try {
    console.log('\n🧪 Testing Test Framework Imports...');
    
    const { runPredictiveMonitoringTests } = await import('./tests/predictive-monitoring-test-framework.js');
    const { runBusinessMetricsTests } = await import('./tests/business-metrics-test-framework.js');
    
    console.log('✅ Test Frameworks imported successfully');
    console.log(`   - Predictive monitoring test function: ${typeof runPredictiveMonitoringTests}`);
    console.log(`   - Business metrics test function: ${typeof runBusinessMetricsTests}`);
    
    results.passed++;
    results.tests.push({ name: 'Test Framework Imports', status: 'PASSED' });
    
  } catch (error) {
    console.error('❌ Test Framework Imports failed:', error.message);
    results.failed++;
    results.tests.push({ name: 'Test Framework Imports', status: 'FAILED', error: error.message });
  }
  
  results.total++;
  
  // Test 4: Basic Functionality Test
  try {
    console.log('\n⚙️ Testing Basic Functionality...');
    
    const { BusinessMetricsCollector } = await import('./services/metrics/business-metrics-collector.js');
    
    // Create a simple test collector
    const testCollector = new BusinessMetricsCollector({
      configPath: './config/business-metrics.json',
      outputPath: './temp/test-basic',
      collectionInterval: 60000
    });
    
    // Test adding a custom metric
    testCollector.addCustomMetric('test_metric', 42.5, {
      name: 'Test Metric',
      category: 'performance',
      unit: 'count'
    });
    
    // Test getting current metrics
    const currentMetrics = testCollector.getCurrentMetrics();
    const testMetric = currentMetrics.find(m => m.id === 'test_metric');
    
    if (testMetric && testMetric.value === 42.5) {
      console.log('✅ Basic functionality test passed');
      console.log(`   - Custom metric added successfully: ${testMetric.name} = ${testMetric.value}`);
      console.log(`   - Total metrics: ${currentMetrics.length}`);
      
      results.passed++;
      results.tests.push({ name: 'Basic Functionality Test', status: 'PASSED' });
    } else {
      throw new Error('Custom metric not found or incorrect value');
    }
    
  } catch (error) {
    console.error('❌ Basic Functionality Test failed:', error.message);
    results.failed++;
    results.tests.push({ name: 'Basic Functionality Test', status: 'FAILED', error: error.message });
  }
  
  results.total++;
  
  // Print results
  console.log('\n🎯 Module Test Results');
  console.log('======================');
  console.log(`Total Tests: ${results.total}`);
  console.log(`Passed: ${results.passed} ✅`);
  console.log(`Failed: ${results.failed} ❌`);
  console.log(`Success Rate: ${results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ Failed Tests:');
    results.tests
      .filter(test => test.status === 'FAILED')
      .forEach(test => {
        console.log(`   - ${test.name}: ${test.error}`);
      });
  }
  
  console.log('\n📋 Test Details:');
  results.tests.forEach(test => {
    const status = test.status === 'PASSED' ? '✅' : '❌';
    console.log(`${status} ${test.name}`);
  });
  
  return results;
}

// Run tests
testModules()
  .then(results => {
    const exitCode = results.failed === 0 ? 0 : 1;
    console.log(`\n🏁 Module tests completed with exit code: ${exitCode}`);
    process.exit(exitCode);
  })
  .catch(error => {
    console.error('❌ Module test runner failed:', error);
    process.exit(1);
  });
