{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1449", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/%00plugin-vue:export-helper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_exports__.default", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}, {"startOffset": 308, "endOffset": 336, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "1457", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/MaterialTextureEditor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 55596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 55596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2428, "endOffset": 18818, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2486, "endOffset": 3432, "count": 41}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2575, "endOffset": 3023, "count": 42}, {"startOffset": 2631, "endOffset": 2710, "count": 0}, {"startOffset": 2831, "endOffset": 3022, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3469, "endOffset": 3530, "count": 41}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3589, "endOffset": 5496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3657, "endOffset": 3833, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3916, "endOffset": 4429, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4499, "endOffset": 4778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4857, "endOffset": 5056, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5129, "endOffset": 5230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5297, "endOffset": 5490, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5552, "endOffset": 6237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5621, "endOffset": 5836, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5908, "endOffset": 6231, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6289, "endOffset": 7435, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6360, "endOffset": 6568, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6661, "endOffset": 6880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6974, "endOffset": 7429, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7507, "endOffset": 8651, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7587, "endOffset": 7909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7980, "endOffset": 8307, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8197, "endOffset": 8225, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8378, "endOffset": 8645, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8710, "endOffset": 10576, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8778, "endOffset": 9022, "count": 1}, {"startOffset": 8924, "endOffset": 9021, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9085, "endOffset": 9267, "count": 1}, {"startOffset": 9187, "endOffset": 9266, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9335, "endOffset": 9499, "count": 1}, {"startOffset": 9405, "endOffset": 9498, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9566, "endOffset": 9728, "count": 1}, {"startOffset": 9635, "endOffset": 9727, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9795, "endOffset": 9957, "count": 1}, {"startOffset": 9864, "endOffset": 9956, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10024, "endOffset": 10186, "count": 1}, {"startOffset": 10093, "endOffset": 10185, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10266, "endOffset": 10570, "count": 1}, {"startOffset": 10336, "endOffset": 10569, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10634, "endOffset": 12564, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10701, "endOffset": 11317, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11384, "endOffset": 11608, "count": 1}, {"startOffset": 11517, "endOffset": 11607, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11677, "endOffset": 11909, "count": 1}, {"startOffset": 11847, "endOffset": 11908, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11978, "endOffset": 12240, "count": 1}, {"startOffset": 12178, "endOffset": 12239, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12309, "endOffset": 12558, "count": 1}, {"startOffset": 12497, "endOffset": 12557, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12620, "endOffset": 13702, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12705, "endOffset": 12946, "count": 1}, {"startOffset": 12867, "endOffset": 12945, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13024, "endOffset": 13257, "count": 1}, {"startOffset": 13178, "endOffset": 13256, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13323, "endOffset": 13470, "count": 1}, {"startOffset": 13387, "endOffset": 13469, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13539, "endOffset": 13696, "count": 1}, {"startOffset": 13608, "endOffset": 13695, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13758, "endOffset": 14842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13827, "endOffset": 14401, "count": 1}, {"startOffset": 14200, "endOffset": 14400, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14467, "endOffset": 14836, "count": 1}, {"startOffset": 14585, "endOffset": 14835, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14910, "endOffset": 16545, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14999, "endOffset": 15358, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15438, "endOffset": 15766, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15841, "endOffset": 16197, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16271, "endOffset": 16539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16600, "endOffset": 17421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16673, "endOffset": 17068, "count": 1}, {"startOffset": 16858, "endOffset": 17067, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17144, "endOffset": 17415, "count": 1}, {"startOffset": 17212, "endOffset": 17414, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17475, "endOffset": 18814, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17552, "endOffset": 17889, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17659, "endOffset": 17674, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17960, "endOffset": 18400, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18067, "endOffset": 18082, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18472, "endOffset": 18808, "count": 1}, {"startOffset": 18703, "endOffset": 18807, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18579, "endOffset": 18594, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1458", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 94781, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 94781, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1065, "endOffset": 2859, "count": 42}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2880, "endOffset": 3025, "count": 93}, {"startOffset": 2930, "endOffset": 3018, "count": 51}], "isBlockCoverage": true}, {"functionName": "previewStyle", "ranges": [{"startOffset": 3036, "endOffset": 3554, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 3563, "endOffset": 3599, "count": 42}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 3619, "endOffset": 4201, "count": 42}, {"startOffset": 3844, "endOffset": 3849, "count": 0}, {"startOffset": 3994, "endOffset": 4110, "count": 41}, {"startOffset": 4119, "endOffset": 4195, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterMaterials", "ranges": [{"startOffset": 4208, "endOffset": 4907, "count": 5}, {"startOffset": 4350, "endOffset": 4532, "count": 0}, {"startOffset": 4600, "endOffset": 4853, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4387, "endOffset": 4513, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4691, "endOffset": 4834, "count": 2}, {"startOffset": 4758, "endOffset": 4834, "count": 1}, {"startOffset": 4787, "endOffset": 4833, "count": 0}], "isBlockCoverage": true}, {"functionName": "selectCategory", "ranges": [{"startOffset": 4914, "endOffset": 5022, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectMaterialById", "ranges": [{"startOffset": 5029, "endOffset": 5206, "count": 41}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5105, "endOffset": 5129, "count": 41}], "isBlockCoverage": true}, {"functionName": "selectMaterial", "ranges": [{"startOffset": 5213, "endOffset": 5501, "count": 42}], "isBlockCoverage": true}, {"functionName": "createNewMaterial", "ranges": [{"startOffset": 5508, "endOffset": 6072, "count": 0}], "isBlockCoverage": false}, {"functionName": "openTextureUpload", "ranges": [{"startOffset": 6079, "endOffset": 6202, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTextureSelected", "ranges": [{"startOffset": 6209, "endOffset": 6395, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTextureDrop", "ranges": [{"startOffset": 6402, "endOffset": 6601, "count": 0}], "isBlockCoverage": false}, {"functionName": "uploadTexture", "ranges": [{"startOffset": 6608, "endOffset": 7200, "count": 2}, {"startOffset": 6867, "endOffset": 6932, "count": 1}, {"startOffset": 6934, "endOffset": 7104, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeTexture", "ranges": [{"startOffset": 7207, "endOffset": 7357, "count": 1}], "isBlockCoverage": true}, {"functionName": "changePreviewShape", "ranges": [{"startOffset": 7364, "endOffset": 7430, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveMaterial", "ranges": [{"startOffset": 7437, "endOffset": 8547, "count": 4}, {"startOffset": 7533, "endOffset": 7718, "count": 3}, {"startOffset": 7708, "endOffset": 7718, "count": 2}, {"startOffset": 7718, "endOffset": 7897, "count": 1}, {"startOffset": 7897, "endOffset": 8453, "count": 3}, {"startOffset": 8220, "endOffset": 8284, "count": 2}, {"startOffset": 8284, "endOffset": 8351, "count": 1}, {"startOffset": 8462, "endOffset": 8541, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8159, "endOffset": 8189, "count": 4}], "isBlockCoverage": true}, {"functionName": "resetMaterial", "ranges": [{"startOffset": 8554, "endOffset": 8695, "count": 1}], "isBlockCoverage": true}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 11354, "endOffset": 35182, "count": 95}, {"startOffset": 11614, "endOffset": 11988, "count": 42}, {"startOffset": 12182, "endOffset": 12269, "count": 42}, {"startOffset": 12334, "endOffset": 12566, "count": 42}, {"startOffset": 12720, "endOffset": 12809, "count": 42}, {"startOffset": 12874, "endOffset": 13110, "count": 42}, {"startOffset": 13381, "endOffset": 13511, "count": 42}, {"startOffset": 13811, "endOffset": 13868, "count": 42}, {"startOffset": 13903, "endOffset": 13996, "count": 42}, {"startOffset": 14166, "endOffset": 14286, "count": 42}, {"startOffset": 17143, "endOffset": 17240, "count": 42}, {"startOffset": 17265, "endOffset": 17517, "count": 42}, {"startOffset": 17653, "endOffset": 18050, "count": 43}, {"startOffset": 17763, "endOffset": 18048, "count": 42}, {"startOffset": 18061, "endOffset": 35158, "count": 52}, {"startOffset": 18347, "endOffset": 18482, "count": 41}, {"startOffset": 18681, "endOffset": 18800, "count": 41}, {"startOffset": 19053, "endOffset": 19112, "count": 41}, {"startOffset": 19474, "endOffset": 19593, "count": 41}, {"startOffset": 19809, "endOffset": 19868, "count": 41}, {"startOffset": 19905, "endOffset": 20521, "count": 41}, {"startOffset": 20897, "endOffset": 21024, "count": 41}, {"startOffset": 21244, "endOffset": 21309, "count": 41}, {"startOffset": 22246, "endOffset": 22365, "count": 41}, {"startOffset": 22618, "endOffset": 22677, "count": 41}, {"startOffset": 23082, "endOffset": 23220, "count": 41}, {"startOffset": 23419, "endOffset": 23545, "count": 41}, {"startOffset": 23893, "endOffset": 23968, "count": 41}, {"startOffset": 24366, "endOffset": 24442, "count": 41}, {"startOffset": 24894, "endOffset": 25021, "count": 41}, {"startOffset": 25373, "endOffset": 25448, "count": 41}, {"startOffset": 26363, "endOffset": 26492, "count": 41}, {"startOffset": 26845, "endOffset": 26921, "count": 41}, {"startOffset": 27736, "endOffset": 27877, "count": 41}, {"startOffset": 28236, "endOffset": 28317, "count": 41}, {"startOffset": 29174, "endOffset": 29301, "count": 0}, {"startOffset": 32782, "endOffset": 32880, "count": 0}, {"startOffset": 33087, "endOffset": 33217, "count": 0}, {"startOffset": 33902, "endOffset": 33969, "count": 0}, {"startOffset": 34004, "endOffset": 34181, "count": 0}, {"startOffset": 34354, "endOffset": 34419, "count": 0}, {"startOffset": 34454, "endOffset": 34625, "count": 0}, {"startOffset": 34798, "endOffset": 34867, "count": 0}, {"startOffset": 34902, "endOffset": 35084, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 12198, "endOffset": 12268, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 12736, "endOffset": 12808, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 13827, "endOffset": 13867, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onInput._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 13919, "endOffset": 13995, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14564, "endOffset": 15061, "count": 665}], "isBlockCoverage": true}, {"functionName": "onClick", "ranges": [{"startOffset": 14885, "endOffset": 14933, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15367, "endOffset": 16963, "count": 116}, {"startOffset": 15886, "endOffset": 16157, "count": 0}, {"startOffset": 16293, "endOffset": 16464, "count": 42}, {"startOffset": 16847, "endOffset": 16860, "count": 11}], "isBlockCoverage": true}, {"functionName": "onClick", "ranges": [{"startOffset": 15683, "endOffset": 15728, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 17159, "endOffset": 17239, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 19069, "endOffset": 19111, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 19825, "endOffset": 19867, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 21260, "endOffset": 21308, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21539, "endOffset": 21913, "count": 364}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 22634, "endOffset": 22676, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 23909, "endOffset": 23967, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 24383, "endOffset": 24441, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 25390, "endOffset": 25447, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 26862, "endOffset": 26920, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 28253, "endOffset": 28316, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 29576, "endOffset": 32457, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onChange._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 32799, "endOffset": 32879, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 33919, "endOffset": 33968, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 34371, "endOffset": 34418, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 34815, "endOffset": 34866, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1459", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 574, "count": 1}], "isBlockCoverage": true}]}]}