{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/tests/AnimationService.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45044, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45044, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 813, "endOffset": 9658, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 856, "endOffset": 932, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 986, "endOffset": 2370, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1664, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1729, "endOffset": 2017, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2074, "endOffset": 2364, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2423, "endOffset": 3669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2497, "endOffset": 2977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3041, "endOffset": 3323, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3380, "endOffset": 3663, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3725, "endOffset": 4715, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3795, "endOffset": 4366, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4423, "endOffset": 4709, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4771, "endOffset": 5795, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4847, "endOffset": 5440, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5497, "endOffset": 5789, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5851, "endOffset": 6632, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5918, "endOffset": 6280, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6337, "endOffset": 6626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6687, "endOffset": 9654, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6775, "endOffset": 7558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7628, "endOffset": 8430, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8507, "endOffset": 9279, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9336, "endOffset": 9648, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/AnimationService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30126, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAnimations", "ranges": [{"startOffset": 596, "endOffset": 857, "count": 3}, {"startOffset": 722, "endOffset": 753, "count": 2}, {"startOffset": 753, "endOffset": 758, "count": 0}, {"startOffset": 764, "endOffset": 855, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 960, "endOffset": 989, "count": 3}], "isBlockCoverage": true}, {"functionName": "getAnimation", "ranges": [{"startOffset": 1230, "endOffset": 1490, "count": 3}, {"startOffset": 1339, "endOffset": 1370, "count": 2}, {"startOffset": 1370, "endOffset": 1377, "count": 1}, {"startOffset": 1383, "endOffset": 1488, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1592, "endOffset": 1620, "count": 3}], "isBlockCoverage": true}, {"functionName": "createAnimation", "ranges": [{"startOffset": 1869, "endOffset": 2110, "count": 5}, {"startOffset": 1973, "endOffset": 2004, "count": 3}, {"startOffset": 2004, "endOffset": 2011, "count": 0}, {"startOffset": 2017, "endOffset": 2108, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2246, "count": 2}], "isBlockCoverage": true}, {"functionName": "updateAnimation", "ranges": [{"startOffset": 2549, "endOffset": 2834, "count": 3}, {"startOffset": 2682, "endOffset": 2713, "count": 2}, {"startOffset": 2713, "endOffset": 2720, "count": 0}, {"startOffset": 2726, "endOffset": 2832, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2939, "endOffset": 2970, "count": 2}], "isBlockCoverage": true}, {"functionName": "deleteAnimation", "ranges": [{"startOffset": 3217, "endOffset": 3442, "count": 2}, {"startOffset": 3312, "endOffset": 3440, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3547, "endOffset": 3578, "count": 2}], "isBlockCoverage": true}, {"functionName": "saveAnimations", "ranges": [{"startOffset": 3897, "endOffset": 4898, "count": 4}, {"startOffset": 4114, "endOffset": 4154, "count": 2}, {"startOffset": 4156, "endOffset": 4407, "count": 1}, {"startOffset": 4407, "endOffset": 4766, "count": 3}, {"startOffset": 4431, "endOffset": 4608, "count": 1}, {"startOffset": 4608, "endOffset": 4766, "count": 2}, {"startOffset": 4719, "endOffset": 4766, "count": 1}, {"startOffset": 4772, "endOffset": 4806, "count": 3}, {"startOffset": 4806, "endOffset": 4896, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5002, "endOffset": 5032, "count": 4}], "isBlockCoverage": true}, {"functionName": "createBlendedAnimation", "ranges": [{"startOffset": 5516, "endOffset": 6194, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6306, "endOffset": 6344, "count": 0}], "isBlockCoverage": false}]}]}