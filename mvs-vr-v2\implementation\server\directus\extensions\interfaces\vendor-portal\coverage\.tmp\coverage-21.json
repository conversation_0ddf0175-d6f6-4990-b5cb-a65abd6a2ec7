{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/TeamMemberManagement.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 681, "endOffset": 8159, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 740, "endOffset": 860, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 916, "endOffset": 1085, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1143, "endOffset": 1415, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1474, "endOffset": 1748, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1805, "endOffset": 2077, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2143, "endOffset": 2515, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2604, "endOffset": 3091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3180, "endOffset": 3603, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3673, "endOffset": 4209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4272, "endOffset": 4806, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4871, "endOffset": 5414, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5467, "endOffset": 5977, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6029, "endOffset": 6262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6322, "endOffset": 6982, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7059, "endOffset": 8155, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7855, "endOffset": 7890, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/TeamMemberManagement.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 57980, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 57980, "count": 1}], "isBlockCoverage": true}, {"functionName": "setup", "ranges": [{"startOffset": 2382, "endOffset": 11564, "count": 14}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4243, "endOffset": 5687, "count": 19}, {"startOffset": 4353, "endOffset": 4577, "count": 1}, {"startOffset": 4634, "endOffset": 4719, "count": 1}, {"startOffset": 4780, "endOffset": 4869, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4441, "endOffset": 4558, "count": 4}, {"startOffset": 4503, "endOffset": 4558, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4667, "endOffset": 4709, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4813, "endOffset": 4859, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4912, "endOffset": 5657, "count": 54}, {"startOffset": 5083, "endOffset": 5490, "count": 0}, {"startOffset": 5535, "endOffset": 5589, "count": 51}, {"startOffset": 5570, "endOffset": 5573, "count": 3}, {"startOffset": 5574, "endOffset": 5578, "count": 48}, {"startOffset": 5589, "endOffset": 5649, "count": 3}, {"startOffset": 5634, "endOffset": 5638, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5763, "endOffset": 5847, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5911, "endOffset": 6082, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6145, "endOffset": 6258, "count": 20}, {"startOffset": 6201, "endOffset": 6204, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6319, "endOffset": 6421, "count": 20}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6481, "endOffset": 7185, "count": 20}, {"startOffset": 6718, "endOffset": 7158, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7273, "endOffset": 7467, "count": 10}, {"startOffset": 7321, "endOffset": 7362, "count": 6}, {"startOffset": 7363, "endOffset": 7394, "count": 6}, {"startOffset": 7395, "endOffset": 7425, "count": 4}, {"startOffset": 7426, "endOffset": 7452, "count": 2}], "isBlockCoverage": true}, {"functionName": "getInitials", "ranges": [{"startOffset": 7510, "endOffset": 7669, "count": 119}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7573, "endOffset": 7595, "count": 238}], "isBlockCoverage": true}, {"functionName": "getAvatarColor", "ranges": [{"startOffset": 7699, "endOffset": 7975, "count": 119}, {"startOffset": 7833, "endOffset": 7900, "count": 1269}], "isBlockCoverage": true}, {"functionName": "getRoleName", "ranges": [{"startOffset": 8002, "endOffset": 8197, "count": 119}, {"startOffset": 8044, "endOffset": 8073, "count": 31}, {"startOffset": 8082, "endOffset": 8113, "count": 58}, {"startOffset": 8122, "endOffset": 8153, "count": 30}, {"startOffset": 8162, "endOffset": 8183, "count": 0}], "isBlockCoverage": true}, {"functionName": "getStatusName", "ranges": [{"startOffset": 8226, "endOffset": 8435, "count": 119}, {"startOffset": 8272, "endOffset": 8303, "count": 90}, {"startOffset": 8312, "endOffset": 8345, "count": 29}, {"startOffset": 8354, "endOffset": 8389, "count": 0}, {"startOffset": 8398, "endOffset": 8421, "count": 0}], "isBlockCoverage": true}, {"functionName": "formatLastActive", "ranges": [{"startOffset": 8467, "endOffset": 9326, "count": 119}, {"startOffset": 8496, "endOffset": 8511, "count": 29}, {"startOffset": 8511, "endOffset": 8631, "count": 90}, {"startOffset": 8631, "endOffset": 8667, "count": 0}, {"startOffset": 8667, "endOffset": 8729, "count": 90}, {"startOffset": 8729, "endOffset": 8861, "count": 31}, {"startOffset": 8842, "endOffset": 8846, "count": 0}, {"startOffset": 8861, "endOffset": 8926, "count": 59}, {"startOffset": 8926, "endOffset": 9055, "count": 29}, {"startOffset": 9036, "endOffset": 9040, "count": 0}, {"startOffset": 9055, "endOffset": 9255, "count": 30}, {"startOffset": 9236, "endOffset": 9240, "count": 0}, {"startOffset": 9255, "endOffset": 9325, "count": 0}], "isBlockCoverage": true}, {"functionName": "isValidEmail", "ranges": [{"startOffset": 9354, "endOffset": 9448, "count": 6}], "isBlockCoverage": true}, {"functionName": "editMember", "ranges": [{"startOffset": 9474, "endOffset": 9601, "count": 0}], "isBlockCoverage": false}, {"functionName": "resendInvitation", "ranges": [{"startOffset": 9633, "endOffset": 9772, "count": 0}], "isBlockCoverage": false}, {"functionName": "showDeleteConfirmation", "ranges": [{"startOffset": 9810, "endOffset": 9902, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteMember", "ranges": [{"startOffset": 9930, "endOffset": 10311, "count": 0}], "isBlockCoverage": false}, {"functionName": "sendInvitation", "ranges": [{"startOffset": 10341, "endOffset": 10934, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 11841, "endOffset": 11947, "count": 14}, {"startOffset": 11910, "endOffset": 11945, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12381, "endOffset": 12470, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12581, "endOffset": 12664, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12713, "endOffset": 12757, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/TeamMemberManagement.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 116126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 116126, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 17447, "count": 31}, {"startOffset": 346, "endOffset": 350, "count": 0}, {"startOffset": 1528, "endOffset": 1749, "count": 1}, {"startOffset": 1750, "endOffset": 1760, "count": 30}, {"startOffset": 5112, "endOffset": 5130, "count": 30}, {"startOffset": 5131, "endOffset": 5150, "count": 1}, {"startOffset": 5389, "endOffset": 5405, "count": 30}, {"startOffset": 5406, "endOffset": 5424, "count": 1}, {"startOffset": 5601, "endOffset": 5624, "count": 0}, {"startOffset": 10772, "endOffset": 15744, "count": 10}, {"startOffset": 14363, "endOffset": 14428, "count": 0}, {"startOffset": 14518, "endOffset": 14611, "count": 0}, {"startOffset": 15745, "endOffset": 15755, "count": 21}, {"startOffset": 15777, "endOffset": 17431, "count": 0}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 618, "endOffset": 681, "count": 4}], "isBlockCoverage": true}, {"functionName": "input", "ranges": [{"startOffset": 1380, "endOffset": 1499, "count": 1}, {"startOffset": 1437, "endOffset": 1444, "count": 0}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 1610, "endOffset": 1667, "count": 0}], "isBlockCoverage": false}, {"functionName": "change", "ranges": [{"startOffset": 2173, "endOffset": 2527, "count": 1}, {"startOffset": 2484, "endOffset": 2499, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2272, "endOffset": 2325, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2331, "endOffset": 2433, "count": 1}, {"startOffset": 2380, "endOffset": 2390, "count": 0}], "isBlockCoverage": true}, {"functionName": "change", "ranges": [{"startOffset": 3240, "endOffset": 3596, "count": 1}, {"startOffset": 3553, "endOffset": 3568, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3339, "endOffset": 3392, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3398, "endOffset": 3500, "count": 1}, {"startOffset": 3447, "endOffset": 3457, "count": 0}], "isBlockCoverage": true}, {"functionName": "change", "ranges": [{"startOffset": 4297, "endOffset": 4647, "count": 1}, {"startOffset": 4604, "endOffset": 4619, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4396, "endOffset": 4449, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4455, "endOffset": 4557, "count": 1}, {"startOffset": 4504, "endOffset": 4514, "count": 0}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 5183, "endOffset": 5284, "count": 1}, {"startOffset": 5268, "endOffset": 5275, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5666, "endOffset": 7795, "count": 119}, {"startOffset": 7271, "endOffset": 7518, "count": 29}, {"startOffset": 7519, "endOffset": 7529, "count": 90}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 7082, "endOffset": 7152, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 7353, "endOffset": 7429, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 7611, "endOffset": 7693, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 8263, "endOffset": 8319, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 8546, "endOffset": 8600, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8769, "endOffset": 9075, "count": 31}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 8956, "endOffset": 9019, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 9233, "endOffset": 9287, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 9530, "endOffset": 9599, "count": 0}], "isBlockCoverage": false}, {"functionName": "change", "ranges": [{"startOffset": 10039, "endOffset": 10391, "count": 1}, {"startOffset": 10348, "endOffset": 10363, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10138, "endOffset": 10191, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10197, "endOffset": 10299, "count": 1}, {"startOffset": 10246, "endOffset": 10256, "count": 0}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 11043, "endOffset": 11107, "count": 1}], "isBlockCoverage": true}, {"functionName": "input", "ranges": [{"startOffset": 11744, "endOffset": 11880, "count": 2}, {"startOffset": 11801, "endOffset": 11808, "count": 0}], "isBlockCoverage": true}, {"functionName": "input", "ranges": [{"startOffset": 12461, "endOffset": 12601, "count": 2}, {"startOffset": 12518, "endOffset": 12525, "count": 0}], "isBlockCoverage": true}, {"functionName": "input", "ranges": [{"startOffset": 13129, "endOffset": 13268, "count": 2}, {"startOffset": 13186, "endOffset": 13193, "count": 0}], "isBlockCoverage": true}, {"functionName": "change", "ranges": [{"startOffset": 13648, "endOffset": 14019, "count": 2}, {"startOffset": 13975, "endOffset": 13990, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13747, "endOffset": 13800, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13806, "endOffset": 13908, "count": 2}, {"startOffset": 13855, "endOffset": 13865, "count": 0}], "isBlockCoverage": true}, {"functionName": "input", "ranges": [{"startOffset": 15167, "endOffset": 15305, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 15446, "endOffset": 15510, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 16061, "endOffset": 16125, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 17198, "endOffset": 17262, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 17472, "endOffset": 17954, "count": 14}, {"startOffset": 17552, "endOffset": 17556, "count": 0}], "isBlockCoverage": true}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 17956, "endOffset": 18317, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 18443, "endOffset": 18465, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 18572, "endOffset": 18603, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/TeamMemberManagement.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 14}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}