import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/vitest.setup.ts'],
    include: [
      'tests/**/*.{test,spec}.{js,ts}',
      'src/**/*.{test,spec}.{js,ts}',
      'services/**/*.{test,spec}.{js,ts}',
    ],
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/.deno/**',
      '**/coverage/**',
      '**/directus/extensions/**/node_modules/**',
      '**/directus/extensions/**/tests/**/*.spec.{js,ts}',
      '**/directus/extensions/**/tests/**/*.vitest.{js,ts}',
      '**/.migration-backup/**',
      '**/deploy-optimizations/**',
      '**/deploy-verify/**',
      // Exclude problematic tests temporarily
      '**/tests/e2e/**',
      '**/tests/**/complete-user-journey.test.js',
      '**/tests/**/websocket-integration.test.js',
      '**/tests/**/ml-integration.test.js',
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      include: ['src/**/*', 'services/**/*', 'shared/**/*'],
      exclude: [
        'node_modules/**',
        'dist/**',
        '.deno/**',
        'coverage/**',
        '**/*.d.ts',
        '**/*.test.*',
        '**/*.spec.*',
        '**/*.config.*',
      ],
    },
    browser: {
      enabled: false,
      headless: true,
      name: 'chrome',
    },
    poolOptions: {
      threads: {
        singleThread: true,
      },
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@directus': resolve(__dirname, './directus/extensions'),
      '@shared': resolve(__dirname, './shared'),
      '@services': resolve(__dirname, './services'),
      '@tests': resolve(__dirname, './tests'),
      '@setup': resolve(__dirname, './tests/setup'),
    },
  },
  deps: {
    interopDefault: true,
  },
  esbuild: {
    target: 'node18',
  },
});
