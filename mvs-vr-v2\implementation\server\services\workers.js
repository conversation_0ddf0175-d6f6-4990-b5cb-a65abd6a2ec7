/**
 * Workers Service
 * 
 * This service manages background workers for the MVS-VR platform.
 * It provides worker status monitoring, job queue management, and health checks.
 */

const { EventEmitter } = require('events');
const configService = require('./config');

class WorkerManager extends EventEmitter {
  constructor() {
    super();
    this.workers = new Map();
    this.jobs = new Map();
    this.isInitialized = false;
    this.stats = {
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      activeJobs: 0
    };
  }

  /**
   * Initialize worker manager
   */
  async initialize() {
    try {
      const config = await configService.getSystemConfig();
      
      // Initialize default workers
      this.registerWorker('asset-processor', {
        name: 'Asset Processor',
        description: 'Processes uploaded assets',
        maxConcurrency: 3,
        timeout: 300000 // 5 minutes
      });
      
      this.registerWorker('email-sender', {
        name: 'Email Sender',
        description: 'Sends email notifications',
        maxConcurrency: 5,
        timeout: 30000 // 30 seconds
      });
      
      this.registerWorker('backup-manager', {
        name: 'Backup Manager',
        description: 'Manages backup operations',
        maxConcurrency: 1,
        timeout: 3600000 // 1 hour
      });
      
      this.isInitialized = true;
      console.log('Worker manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize worker manager:', error);
      throw error;
    }
  }

  /**
   * Register a worker type
   * @param {string} type - Worker type
   * @param {Object} config - Worker configuration
   */
  registerWorker(type, config) {
    this.workers.set(type, {
      ...config,
      type,
      activeJobs: 0,
      totalJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      lastActivity: null,
      status: 'idle'
    });
  }

  /**
   * Add a job to the queue
   * @param {string} type - Worker type
   * @param {Object} data - Job data
   * @param {Object} options - Job options
   * @returns {string} Job ID
   */
  async addJob(type, data, options = {}) {
    const jobId = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const job = {
      id: jobId,
      type,
      data,
      options: {
        priority: 0,
        delay: 0,
        attempts: 3,
        ...options
      },
      status: 'pending',
      createdAt: new Date(),
      startedAt: null,
      completedAt: null,
      error: null,
      result: null,
      attempts: 0
    };
    
    this.jobs.set(jobId, job);
    this.stats.totalJobs++;
    
    // Emit job added event
    this.emit('job:added', job);
    
    // Process job immediately (in a real implementation, this would be queued)
    setImmediate(() => this.processJob(jobId));
    
    return jobId;
  }

  /**
   * Process a job
   * @param {string} jobId - Job ID
   */
  async processJob(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      return;
    }

    const worker = this.workers.get(job.type);
    if (!worker) {
      job.status = 'failed';
      job.error = `Unknown worker type: ${job.type}`;
      this.stats.failedJobs++;
      this.emit('job:failed', job);
      return;
    }

    // Check concurrency limit
    if (worker.activeJobs >= worker.maxConcurrency) {
      // Job will be retried later
      return;
    }

    try {
      // Update job status
      job.status = 'running';
      job.startedAt = new Date();
      job.attempts++;
      
      // Update worker stats
      worker.activeJobs++;
      worker.totalJobs++;
      worker.lastActivity = new Date();
      worker.status = 'busy';
      this.stats.activeJobs++;
      
      this.emit('job:started', job);
      
      // Simulate job processing (in a real implementation, this would call actual worker functions)
      const result = await this.simulateJobProcessing(job);
      
      // Job completed successfully
      job.status = 'completed';
      job.completedAt = new Date();
      job.result = result;
      
      worker.activeJobs--;
      worker.completedJobs++;
      this.stats.activeJobs--;
      this.stats.completedJobs++;
      
      if (worker.activeJobs === 0) {
        worker.status = 'idle';
      }
      
      this.emit('job:completed', job);
      
    } catch (error) {
      // Job failed
      job.status = 'failed';
      job.completedAt = new Date();
      job.error = error.message;
      
      worker.activeJobs--;
      worker.failedJobs++;
      this.stats.activeJobs--;
      this.stats.failedJobs++;
      
      if (worker.activeJobs === 0) {
        worker.status = 'idle';
      }
      
      this.emit('job:failed', job);
      
      // Retry if attempts remaining
      if (job.attempts < job.options.attempts) {
        setTimeout(() => {
          job.status = 'pending';
          this.processJob(jobId);
        }, 5000); // Retry after 5 seconds
      }
    }
  }

  /**
   * Simulate job processing
   * @param {Object} job - Job object
   * @returns {Promise<any>} Job result
   */
  async simulateJobProcessing(job) {
    // Simulate processing time
    const processingTime = Math.random() * 2000 + 1000; // 1-3 seconds
    await new Promise(resolve => setTimeout(resolve, processingTime));
    
    // Simulate occasional failures
    if (Math.random() < 0.1) { // 10% failure rate
      throw new Error('Simulated job failure');
    }
    
    return {
      processed: true,
      processingTime,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get job status
   * @param {string} jobId - Job ID
   * @returns {Object|null} Job status
   */
  getJobStatus(jobId) {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Get worker status
   * @param {string} type - Worker type
   * @returns {Object|null} Worker status
   */
  getWorkerStatus(type) {
    return this.workers.get(type) || null;
  }

  /**
   * Get all workers status
   * @returns {Array} Workers status
   */
  getAllWorkersStatus() {
    return Array.from(this.workers.values());
  }

  /**
   * Check if workers are healthy
   * @returns {boolean} Health status
   */
  async checkStatus() {
    if (!this.isInitialized) {
      return false;
    }
    
    // Check if any workers are stuck
    const now = Date.now();
    for (const worker of this.workers.values()) {
      if (worker.status === 'busy' && worker.lastActivity) {
        const timeSinceActivity = now - worker.lastActivity.getTime();
        if (timeSinceActivity > worker.timeout) {
          return false; // Worker appears to be stuck
        }
      }
    }
    
    return true;
  }

  /**
   * Get health status
   * @returns {Promise<Object>} Health status
   */
  async getHealthStatus() {
    try {
      const isHealthy = await this.checkStatus();
      
      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        initialized: this.isInitialized,
        timestamp: new Date().toISOString(),
        workers: this.getAllWorkersStatus(),
        stats: { ...this.stats }
      };
    } catch (error) {
      return {
        status: 'error',
        initialized: this.isInitialized,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Get statistics
   * @returns {Object} Worker statistics
   */
  getStatistics() {
    const workerStats = {};
    for (const [type, worker] of this.workers.entries()) {
      workerStats[type] = {
        activeJobs: worker.activeJobs,
        totalJobs: worker.totalJobs,
        completedJobs: worker.completedJobs,
        failedJobs: worker.failedJobs,
        status: worker.status,
        lastActivity: worker.lastActivity
      };
    }
    
    return {
      timestamp: new Date().toISOString(),
      overall: { ...this.stats },
      workers: workerStats,
      totalWorkers: this.workers.size,
      activeWorkers: Array.from(this.workers.values()).filter(w => w.status === 'busy').length
    };
  }

  /**
   * Stop all workers
   */
  async stop() {
    // In a real implementation, this would gracefully stop all workers
    this.workers.clear();
    this.jobs.clear();
    this.isInitialized = false;
    console.log('Worker manager stopped');
  }
}

// Create singleton instance
const workerManager = new WorkerManager();

module.exports = {
  initialize: () => workerManager.initialize(),
  addJob: (type, data, options) => workerManager.addJob(type, data, options),
  getJobStatus: (jobId) => workerManager.getJobStatus(jobId),
  getWorkerStatus: (type) => workerManager.getWorkerStatus(type),
  getAllWorkersStatus: () => workerManager.getAllWorkersStatus(),
  checkStatus: () => workerManager.checkStatus(),
  getHealthStatus: () => workerManager.getHealthStatus(),
  getStatistics: () => workerManager.getStatistics(),
  stop: () => workerManager.stop(),
  on: (event, listener) => workerManager.on(event, listener),
  off: (event, listener) => workerManager.off(event, listener)
};
