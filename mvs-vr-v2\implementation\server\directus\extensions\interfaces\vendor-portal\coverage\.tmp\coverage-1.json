{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 329, "endOffset": 450, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/tests/GuidedSetupService.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 145887, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 145887, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 821, "endOffset": 29253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 905, "endOffset": 1546, "count": 23}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2099, "endOffset": 4275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2186, "endOffset": 2985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3078, "endOffset": 3661, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3743, "endOffset": 4269, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4336, "endOffset": 10005, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4417, "endOffset": 5800, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5887, "endOffset": 7172, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7259, "endOffset": 8028, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8111, "endOffset": 9109, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9192, "endOffset": 9999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10064, "endOffset": 15046, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10161, "endOffset": 11773, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11877, "endOffset": 13272, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13355, "endOffset": 14018, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14097, "endOffset": 15040, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15104, "endOffset": 17995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15203, "endOffset": 16354, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16433, "endOffset": 17099, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17198, "endOffset": 17989, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18052, "endOffset": 25457, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18148, "endOffset": 21443, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19245, "endOffset": 19555, "count": 2}, {"startOffset": 19314, "endOffset": 19480, "count": 1}, {"startOffset": 19480, "endOffset": 19554, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19606, "endOffset": 19838, "count": 1}, {"startOffset": 19764, "endOffset": 19837, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21528, "endOffset": 22326, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22412, "endOffset": 24119, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 23161, "endOffset": 23444, "count": 2}, {"startOffset": 23230, "endOffset": 23369, "count": 1}, {"startOffset": 23369, "endOffset": 23443, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 24207, "endOffset": 25451, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25510, "endOffset": 27438, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25580, "endOffset": 26846, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26929, "endOffset": 27432, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27499, "endOffset": 29249, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 27566, "endOffset": 28454, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 28541, "endOffset": 29243, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/GuidedSetupService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42253, "count": 1}], "isBlockCoverage": true}, {"functionName": "GuidedSetupService", "ranges": [{"startOffset": 580, "endOffset": 927, "count": 23}], "isBlockCoverage": true}, {"functionName": "getOnboardingStatus", "ranges": [{"startOffset": 1089, "endOffset": 1451, "count": 3}, {"startOffset": 1261, "endOffset": 1305, "count": 2}, {"startOffset": 1305, "endOffset": 1328, "count": 1}, {"startOffset": 1329, "endOffset": 1335, "count": 1}, {"startOffset": 1343, "endOffset": 1447, "count": 1}], "isBlockCoverage": true}, {"functionName": "saveOnboardingStatus", "ranges": [{"startOffset": 1746, "endOffset": 2750, "count": 5}, {"startOffset": 1892, "endOffset": 1920, "count": 4}, {"startOffset": 1920, "endOffset": 2249, "count": 2}, {"startOffset": 2206, "endOffset": 2249, "count": 1}, {"startOffset": 2249, "endOffset": 2636, "count": 2}, {"startOffset": 2593, "endOffset": 2636, "count": 1}, {"startOffset": 2643, "endOffset": 2746, "count": 3}], "isBlockCoverage": true}, {"functionName": "saveCompanyProfile", "ranges": [{"startOffset": 2963, "endOffset": 3750, "count": 4}, {"startOffset": 3417, "endOffset": 3498, "count": 3}, {"startOffset": 3498, "endOffset": 3522, "count": 2}, {"startOffset": 3524, "endOffset": 3604, "count": 2}, {"startOffset": 3596, "endOffset": 3604, "count": 1}, {"startOffset": 3604, "endOffset": 3746, "count": 2}], "isBlockCoverage": true}, {"functionName": "uploadCompanyLogo", "ranges": [{"startOffset": 3938, "endOffset": 4597, "count": 3}, {"startOffset": 4259, "endOffset": 4447, "count": 2}, {"startOffset": 4447, "endOffset": 4492, "count": 1}, {"startOffset": 4492, "endOffset": 4593, "count": 2}], "isBlockCoverage": true}, {"functionName": "saveUserAccounts", "ranges": [{"startOffset": 4806, "endOffset": 6275, "count": 4}, {"startOffset": 5192, "endOffset": 6018, "count": 3}, {"startOffset": 6018, "endOffset": 6172, "count": 1}, {"startOffset": 6172, "endOffset": 6271, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5300, "endOffset": 5942, "count": 4}, {"startOffset": 5341, "endOffset": 5934, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6124, "endOffset": 6154, "count": 2}], "isBlockCoverage": true}, {"functionName": "saveBranding", "ranges": [{"startOffset": 6473, "endOffset": 7179, "count": 2}, {"startOffset": 7035, "endOffset": 7175, "count": 1}], "isBlockCoverage": true}, {"functionName": "trackWizardAnalytics", "ranges": [{"startOffset": 7384, "endOffset": 7935, "count": 2}, {"startOffset": 7711, "endOffset": 7931, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/directus.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28293, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDirectusUrl", "ranges": [{"startOffset": 322, "endOffset": 735, "count": 23}, {"startOffset": 488, "endOffset": 732, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 838, "endOffset": 868, "count": 23}], "isBlockCoverage": true}, {"functionName": "getDirectusToken", "ranges": [{"startOffset": 1010, "endOffset": 1736, "count": 23}, {"startOffset": 1311, "endOffset": 1490, "count": 0}, {"startOffset": 1492, "endOffset": 1539, "count": 0}, {"startOffset": 1546, "endOffset": 1641, "count": 0}, {"startOffset": 1645, "endOffset": 1735, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1841, "endOffset": 1873, "count": 23}], "isBlockCoverage": true}, {"functionName": "getCurrentVendorId", "ranges": [{"startOffset": 1987, "endOffset": 2746, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2853, "endOffset": 2887, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentUserRole", "ranges": [{"startOffset": 2991, "endOffset": 3730, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3837, "endOffset": 3871, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasPermission", "ranges": [{"startOffset": 4132, "endOffset": 4665, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4767, "endOffset": 4796, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDate", "ranges": [{"startOffset": 4989, "endOffset": 5397, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5496, "endOffset": 5522, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileUrl", "ranges": [{"startOffset": 5642, "endOffset": 5778, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5877, "endOffset": 5903, "count": 0}], "isBlockCoverage": false}]}]}