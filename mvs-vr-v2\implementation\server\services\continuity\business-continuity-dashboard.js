/**
 * Business Continuity Dashboard Service
 * 
 * This service provides a comprehensive dashboard for business continuity monitoring,
 * including real-time status, metrics visualization, and executive reporting.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const EventEmitter = require('events');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

const logger = require('../../utils/logger').getLogger('business-continuity-dashboard');

// Dashboard widget types
const WIDGET_TYPES = {
  STATUS_OVERVIEW: 'status_overview',
  SERVICE_HEALTH: 'service_health',
  BUSINESS_METRICS: 'business_metrics',
  RECOVERY_TIMELINE: 'recovery_timeline',
  DEPENDENCY_MAP: 'dependency_map',
  ALERT_SUMMARY: 'alert_summary',
  EXECUTIVE_SUMMARY: 'executive_summary'
};

// Report types
const REPORT_TYPES = {
  OPERATIONAL: 'operational',
  EXECUTIVE: 'executive',
  TECHNICAL: 'technical',
  COMPLIANCE: 'compliance'
};

class BusinessContinuityDashboard extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      outputPath: options.outputPath || path.join(__dirname, '../../data/dashboard'),
      reportPath: options.reportPath || path.join(__dirname, '../../reports'),
      refreshInterval: options.refreshInterval || 30000, // 30 seconds
      retentionDays: options.retentionDays || 30,
      ...options
    };
    
    // Dashboard data
    this.widgets = new Map();
    this.dashboardData = new Map();
    this.reports = new Map();
    this.kpis = new Map();
    
    // External services
    this.serviceMonitor = null;
    this.recoveryPrioritizer = null;
    this.alertManager = null;
    
    // Statistics
    this.stats = {
      totalWidgets: 0,
      reportsGenerated: 0,
      lastUpdate: null,
      startTime: Date.now()
    };
    
    this.initialize();
  }
  
  /**
   * Initialize the dashboard
   */
  async initialize() {
    try {
      // Ensure output directories exist
      for (const dir of [this.options.outputPath, this.options.reportPath]) {
        if (!await existsAsync(dir)) {
          await mkdirAsync(dir, { recursive: true });
        }
      }
      
      // Initialize widgets
      this.initializeWidgets();
      
      // Start refresh interval
      this.startRefreshInterval();
      
      logger.info('Business continuity dashboard initialized');
    } catch (error) {
      logger.error('Failed to initialize business continuity dashboard:', error);
      throw error;
    }
  }
  
  /**
   * Set external service references
   * @param {Object} services - External services
   */
  setServices(services) {
    this.serviceMonitor = services.serviceMonitor;
    this.recoveryPrioritizer = services.recoveryPrioritizer;
    this.alertManager = services.alertManager;
    
    logger.debug('External services configured for dashboard');
  }
  
  /**
   * Initialize dashboard widgets
   */
  initializeWidgets() {
    // Status Overview Widget
    this.widgets.set(WIDGET_TYPES.STATUS_OVERVIEW, {
      type: WIDGET_TYPES.STATUS_OVERVIEW,
      title: 'System Status Overview',
      description: 'Overall system health and availability',
      refreshInterval: 30000,
      dataSource: 'getStatusOverview'
    });
    
    // Service Health Widget
    this.widgets.set(WIDGET_TYPES.SERVICE_HEALTH, {
      type: WIDGET_TYPES.SERVICE_HEALTH,
      title: 'Service Health Matrix',
      description: 'Individual service health status',
      refreshInterval: 60000,
      dataSource: 'getServiceHealthData'
    });
    
    // Business Metrics Widget
    this.widgets.set(WIDGET_TYPES.BUSINESS_METRICS, {
      type: WIDGET_TYPES.BUSINESS_METRICS,
      title: 'Business Impact Metrics',
      description: 'Key business continuity metrics',
      refreshInterval: 300000, // 5 minutes
      dataSource: 'getBusinessMetricsData'
    });
    
    // Recovery Timeline Widget
    this.widgets.set(WIDGET_TYPES.RECOVERY_TIMELINE, {
      type: WIDGET_TYPES.RECOVERY_TIMELINE,
      title: 'Recovery Timeline',
      description: 'Current and planned recovery activities',
      refreshInterval: 120000, // 2 minutes
      dataSource: 'getRecoveryTimelineData'
    });
    
    // Dependency Map Widget
    this.widgets.set(WIDGET_TYPES.DEPENDENCY_MAP, {
      type: WIDGET_TYPES.DEPENDENCY_MAP,
      title: 'Service Dependencies',
      description: 'Service dependency visualization',
      refreshInterval: 600000, // 10 minutes
      dataSource: 'getDependencyMapData'
    });
    
    // Alert Summary Widget
    this.widgets.set(WIDGET_TYPES.ALERT_SUMMARY, {
      type: WIDGET_TYPES.ALERT_SUMMARY,
      title: 'Active Alerts',
      description: 'Current system alerts and notifications',
      refreshInterval: 30000,
      dataSource: 'getAlertSummaryData'
    });
    
    // Executive Summary Widget
    this.widgets.set(WIDGET_TYPES.EXECUTIVE_SUMMARY, {
      type: WIDGET_TYPES.EXECUTIVE_SUMMARY,
      title: 'Executive Summary',
      description: 'High-level business continuity status',
      refreshInterval: 900000, // 15 minutes
      dataSource: 'getExecutiveSummaryData'
    });
    
    this.stats.totalWidgets = this.widgets.size;
    logger.debug(`Initialized ${this.widgets.size} dashboard widgets`);
  }
  
  /**
   * Start refresh interval
   */
  startRefreshInterval() {
    setInterval(() => {
      this.refreshDashboard();
    }, this.options.refreshInterval);
    
    // Initial refresh
    this.refreshDashboard();
  }
  
  /**
   * Refresh dashboard data
   */
  async refreshDashboard() {
    try {
      const startTime = Date.now();
      
      // Refresh each widget
      for (const [widgetType, widget] of this.widgets.entries()) {
        try {
          const data = await this[widget.dataSource]();
          this.dashboardData.set(widgetType, {
            ...data,
            lastUpdate: new Date().toISOString(),
            widget: widget.title
          });
        } catch (error) {
          logger.error(`Error refreshing widget ${widgetType}:`, error);
          this.dashboardData.set(widgetType, {
            error: error.message,
            lastUpdate: new Date().toISOString(),
            widget: widget.title
          });
        }
      }
      
      // Update KPIs
      this.updateKPIs();
      
      // Save dashboard data
      await this.saveDashboardData();
      
      const refreshTime = Date.now() - startTime;
      this.stats.lastUpdate = new Date().toISOString();
      
      logger.debug(`Dashboard refreshed in ${refreshTime}ms`);
      
      // Emit refresh event
      this.emit('dashboardRefreshed', {
        timestamp: this.stats.lastUpdate,
        refreshTime,
        widgets: this.widgets.size
      });
      
    } catch (error) {
      logger.error('Error refreshing dashboard:', error);
    }
  }
  
  /**
   * Get status overview data
   * @returns {Object} Status overview
   */
  async getStatusOverview() {
    const data = {
      systemStatus: 'unknown',
      overallHealth: 0,
      servicesTotal: 0,
      servicesHealthy: 0,
      servicesDegraded: 0,
      servicesUnhealthy: 0,
      activeIncidents: 0,
      lastIncident: null
    };
    
    if (this.serviceMonitor) {
      const stats = this.serviceMonitor.getStatistics();
      const healthData = this.serviceMonitor.getServiceHealth();
      
      data.servicesTotal = stats.totalServices;
      data.servicesHealthy = stats.healthyServices;
      data.servicesDegraded = stats.degradedServices;
      data.servicesUnhealthy = stats.unhealthyServices + stats.criticalServices;
      
      // Calculate overall health percentage
      data.overallHealth = Math.round(
        ((data.servicesHealthy * 100) + (data.servicesDegraded * 75)) / data.servicesTotal
      );
      
      // Determine system status
      if (data.overallHealth >= 95) {
        data.systemStatus = 'operational';
      } else if (data.overallHealth >= 80) {
        data.systemStatus = 'degraded';
      } else {
        data.systemStatus = 'outage';
      }
    }
    
    return data;
  }
  
  /**
   * Get service health data
   * @returns {Object} Service health data
   */
  async getServiceHealthData() {
    const data = {
      services: [],
      healthDistribution: {
        healthy: 0,
        degraded: 0,
        unhealthy: 0,
        critical: 0,
        unknown: 0
      }
    };
    
    if (this.serviceMonitor) {
      const healthData = this.serviceMonitor.getServiceHealth();
      
      for (const [serviceId, serviceData] of Object.entries(healthData)) {
        const service = {
          id: serviceId,
          name: serviceData.service.name,
          type: serviceData.service.type,
          health: serviceData.health.state,
          uptime: serviceData.health.uptime,
          responseTime: serviceData.health.responseTime,
          lastCheck: serviceData.health.lastCheck
        };
        
        data.services.push(service);
        data.healthDistribution[service.health]++;
      }
    }
    
    return data;
  }
  
  /**
   * Get business metrics data
   * @returns {Object} Business metrics
   */
  async getBusinessMetricsData() {
    const data = {
      metrics: {},
      trends: {},
      kpis: {}
    };
    
    if (this.serviceMonitor) {
      data.metrics = this.serviceMonitor.getBusinessMetrics();
    }
    
    // Add KPIs
    data.kpis = Object.fromEntries(this.kpis);
    
    return data;
  }
  
  /**
   * Get recovery timeline data
   * @returns {Object} Recovery timeline
   */
  async getRecoveryTimelineData() {
    const data = {
      activeRecoveries: [],
      plannedRecoveries: [],
      completedRecoveries: [],
      estimatedCompletionTime: null
    };
    
    if (this.recoveryPrioritizer) {
      const plans = this.recoveryPrioritizer.getAllRecoveryPlans();
      
      // Process recent plans
      const recentPlans = plans.filter(plan => {
        const planTime = new Date(plan.timestamp);
        const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return planTime > dayAgo;
      });
      
      data.activeRecoveries = recentPlans.slice(0, 5);
      data.estimatedCompletionTime = recentPlans.length > 0 
        ? recentPlans[0].totalEstimatedTime 
        : null;
    }
    
    return data;
  }
  
  /**
   * Get dependency map data
   * @returns {Object} Dependency map
   */
  async getDependencyMapData() {
    const data = {
      nodes: [],
      edges: [],
      clusters: []
    };
    
    if (this.serviceMonitor) {
      data = this.serviceMonitor.getDependencyGraph();
    }
    
    return data;
  }
  
  /**
   * Get alert summary data
   * @returns {Object} Alert summary
   */
  async getAlertSummaryData() {
    const data = {
      activeAlerts: [],
      alertsByPriority: {
        critical: 0,
        high: 0,
        medium: 0,
        low: 0
      },
      recentAlerts: [],
      alertTrends: {}
    };
    
    if (this.alertManager) {
      const alerts = this.alertManager.getActiveAlerts();
      data.activeAlerts = alerts.slice(0, 10); // Top 10 alerts
      
      // Count by priority
      for (const alert of alerts) {
        if (data.alertsByPriority[alert.severity] !== undefined) {
          data.alertsByPriority[alert.severity]++;
        }
      }
      
      // Get recent alerts
      data.recentAlerts = this.alertManager.getAlertHistory(20);
    }
    
    return data;
  }
  
  /**
   * Get executive summary data
   * @returns {Object} Executive summary
   */
  async getExecutiveSummaryData() {
    const data = {
      businessContinuityScore: 0,
      riskLevel: 'low',
      keyMetrics: {},
      recommendations: [],
      incidents: {
        total: 0,
        resolved: 0,
        mttr: 0 // Mean Time To Recovery
      }
    };
    
    // Calculate business continuity score
    const statusOverview = await this.getStatusOverview();
    data.businessContinuityScore = statusOverview.overallHealth;
    
    // Determine risk level
    if (data.businessContinuityScore >= 95) {
      data.riskLevel = 'low';
    } else if (data.businessContinuityScore >= 80) {
      data.riskLevel = 'medium';
    } else if (data.businessContinuityScore >= 60) {
      data.riskLevel = 'high';
    } else {
      data.riskLevel = 'critical';
    }
    
    // Key metrics
    data.keyMetrics = {
      systemAvailability: `${data.businessContinuityScore}%`,
      servicesMonitored: statusOverview.servicesTotal,
      activeIncidents: statusOverview.activeIncidents,
      lastUpdate: new Date().toISOString()
    };
    
    // Generate recommendations
    data.recommendations = this.generateRecommendations(data);
    
    return data;
  }
  
  /**
   * Generate recommendations based on current state
   * @param {Object} summaryData - Executive summary data
   * @returns {Array<string>} Recommendations
   */
  generateRecommendations(summaryData) {
    const recommendations = [];
    
    if (summaryData.businessContinuityScore < 80) {
      recommendations.push('Immediate attention required for degraded services');
    }
    
    if (summaryData.riskLevel === 'high' || summaryData.riskLevel === 'critical') {
      recommendations.push('Activate business continuity procedures');
      recommendations.push('Consider escalating to senior management');
    }
    
    if (summaryData.keyMetrics.activeIncidents > 0) {
      recommendations.push('Review and prioritize incident resolution');
    }
    
    // Add default recommendations if none generated
    if (recommendations.length === 0) {
      recommendations.push('Continue monitoring system health');
      recommendations.push('Review recovery procedures regularly');
    }
    
    return recommendations;
  }
  
  /**
   * Update KPIs
   */
  updateKPIs() {
    const timestamp = new Date().toISOString();
    
    // System availability KPI
    this.kpis.set('system_availability', {
      value: this.dashboardData.get(WIDGET_TYPES.STATUS_OVERVIEW)?.overallHealth || 0,
      target: 99.9,
      unit: 'percentage',
      timestamp
    });
    
    // Mean Time To Recovery (MTTR) KPI
    this.kpis.set('mttr', {
      value: this.calculateMTTR(),
      target: 60, // 60 minutes
      unit: 'minutes',
      timestamp
    });
    
    // Service Health Score KPI
    this.kpis.set('service_health_score', {
      value: this.calculateServiceHealthScore(),
      target: 95,
      unit: 'percentage',
      timestamp
    });
  }
  
  /**
   * Calculate Mean Time To Recovery
   * @returns {number} MTTR in minutes
   */
  calculateMTTR() {
    // Simplified MTTR calculation
    // In a real implementation, this would analyze historical incident data
    return 45; // Default 45 minutes
  }
  
  /**
   * Calculate service health score
   * @returns {number} Health score percentage
   */
  calculateServiceHealthScore() {
    const statusData = this.dashboardData.get(WIDGET_TYPES.STATUS_OVERVIEW);
    return statusData?.overallHealth || 0;
  }
  
  /**
   * Save dashboard data
   */
  async saveDashboardData() {
    try {
      const dashboardSnapshot = {
        timestamp: new Date().toISOString(),
        widgets: Object.fromEntries(this.dashboardData),
        kpis: Object.fromEntries(this.kpis),
        statistics: this.stats
      };
      
      const fileName = `dashboard-${new Date().toISOString().split('T')[0]}.json`;
      const filePath = path.join(this.options.outputPath, fileName);
      
      await writeFileAsync(filePath, JSON.stringify(dashboardSnapshot, null, 2));
      
    } catch (error) {
      logger.error('Failed to save dashboard data:', error);
    }
  }
  
  /**
   * Generate report
   * @param {string} reportType - Type of report
   * @param {Object} options - Report options
   * @returns {Object} Generated report
   */
  async generateReport(reportType, options = {}) {
    const report = {
      id: `report-${reportType}-${Date.now()}`,
      type: reportType,
      timestamp: new Date().toISOString(),
      period: options.period || '24h',
      data: {},
      summary: {},
      recommendations: []
    };
    
    switch (reportType) {
      case REPORT_TYPES.EXECUTIVE:
        report.data = await this.getExecutiveSummaryData();
        report.summary = this.generateExecutiveSummary(report.data);
        break;
        
      case REPORT_TYPES.OPERATIONAL:
        report.data = {
          statusOverview: await this.getStatusOverview(),
          serviceHealth: await this.getServiceHealthData(),
          alerts: await this.getAlertSummaryData()
        };
        report.summary = this.generateOperationalSummary(report.data);
        break;
        
      case REPORT_TYPES.TECHNICAL:
        report.data = {
          serviceHealth: await this.getServiceHealthData(),
          dependencies: await this.getDependencyMapData(),
          recovery: await this.getRecoveryTimelineData()
        };
        report.summary = this.generateTechnicalSummary(report.data);
        break;
        
      default:
        throw new Error(`Unknown report type: ${reportType}`);
    }
    
    // Store report
    this.reports.set(report.id, report);
    this.stats.reportsGenerated++;
    
    // Save report to file
    await this.saveReport(report);
    
    logger.info(`Generated ${reportType} report: ${report.id}`);
    
    return report;
  }
  
  /**
   * Generate executive summary
   * @param {Object} data - Executive data
   * @returns {Object} Summary
   */
  generateExecutiveSummary(data) {
    return {
      overallStatus: data.riskLevel,
      businessContinuityScore: data.businessContinuityScore,
      keyFindings: [
        `System availability: ${data.keyMetrics.systemAvailability}`,
        `Risk level: ${data.riskLevel}`,
        `Services monitored: ${data.keyMetrics.servicesMonitored}`
      ],
      actionItems: data.recommendations
    };
  }
  
  /**
   * Generate operational summary
   * @param {Object} data - Operational data
   * @returns {Object} Summary
   */
  generateOperationalSummary(data) {
    return {
      systemHealth: data.statusOverview.overallHealth,
      serviceStatus: {
        total: data.serviceHealth.services.length,
        healthy: data.serviceHealth.healthDistribution.healthy,
        issues: data.serviceHealth.healthDistribution.degraded + 
                data.serviceHealth.healthDistribution.unhealthy
      },
      alertStatus: {
        active: data.alerts.activeAlerts.length,
        critical: data.alerts.alertsByPriority.critical,
        high: data.alerts.alertsByPriority.high
      }
    };
  }
  
  /**
   * Generate technical summary
   * @param {Object} data - Technical data
   * @returns {Object} Summary
   */
  generateTechnicalSummary(data) {
    return {
      serviceMetrics: {
        totalServices: data.serviceHealth.services.length,
        healthyServices: data.serviceHealth.healthDistribution.healthy,
        degradedServices: data.serviceHealth.healthDistribution.degraded
      },
      dependencyAnalysis: {
        totalNodes: data.dependencies.nodes.length,
        totalEdges: data.dependencies.edges.length
      },
      recoveryStatus: {
        activeRecoveries: data.recovery.activeRecoveries.length,
        estimatedCompletion: data.recovery.estimatedCompletionTime
      }
    };
  }
  
  /**
   * Save report to file
   * @param {Object} report - Report to save
   */
  async saveReport(report) {
    try {
      const fileName = `${report.id}.json`;
      const filePath = path.join(this.options.reportPath, fileName);
      
      await writeFileAsync(filePath, JSON.stringify(report, null, 2));
      
    } catch (error) {
      logger.error('Failed to save report:', error);
    }
  }
  
  /**
   * Get dashboard data
   * @param {string} widgetType - Widget type (optional)
   * @returns {Object} Dashboard data
   */
  getDashboardData(widgetType = null) {
    if (widgetType) {
      return this.dashboardData.get(widgetType);
    }
    
    return Object.fromEntries(this.dashboardData);
  }
  
  /**
   * Get KPIs
   * @returns {Object} KPIs
   */
  getKPIs() {
    return Object.fromEntries(this.kpis);
  }
  
  /**
   * Get reports
   * @param {string} reportType - Report type filter (optional)
   * @returns {Array<Object>} Reports
   */
  getReports(reportType = null) {
    const allReports = Array.from(this.reports.values());
    
    if (reportType) {
      return allReports.filter(report => report.type === reportType);
    }
    
    return allReports;
  }
  
  /**
   * Get statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      dashboardDataSize: this.dashboardData.size,
      reportsCount: this.reports.size,
      kpisCount: this.kpis.size
    };
  }
}

module.exports = {
  BusinessContinuityDashboard,
  WIDGET_TYPES,
  REPORT_TYPES
};
