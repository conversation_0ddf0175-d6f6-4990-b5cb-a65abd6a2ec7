/**
 * Cache Recovery Script
 * 
 * This script handles recovery of cache systems.
 */

const logger = require('../../utils/logger').getLogger('cache-recovery');
const cacheService = require('../../services/cache');

/**
 * Recover cache system
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverCache(options = {}) {
  const {
    test = false,
    clearExisting = true
  } = options;
  
  logger.info('Starting cache recovery');
  
  try {
    if (test) {
      logger.info('[TEST] Would perform cache recovery');
      return {
        success: true,
        test: true,
        message: 'Cache recovery test completed'
      };
    }
    
    // Initialize cache service
    await cacheService.initialize();
    
    // Clear existing cache if requested
    if (clearExisting) {
      await cacheService.clear();
      logger.info('Cleared existing cache');
    }
    
    // Test cache functionality
    const testKey = '__cache_recovery_test__';
    const testValue = { timestamp: Date.now(), test: true };
    
    await cacheService.set(testKey, testValue, 60);
    const retrieved = await cacheService.get(testKey);
    await cacheService.del(testKey);
    
    if (JSON.stringify(retrieved) !== JSON.stringify(testValue)) {
      throw new Error('Cache functionality test failed');
    }
    
    logger.info('Cache recovery completed successfully');
    
    return {
      success: true,
      message: 'Cache system recovered and tested successfully'
    };
    
  } catch (error) {
    logger.error(`Cache recovery failed: ${error.message}`, { error });
    return {
      success: false,
      error: error.message
    };
  }
}

// If script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    clearExisting: !args.includes('--no-clear')
  };
  
  recoverCache(options)
    .then(result => {
      if (result.success) {
        console.log('Cache recovery completed successfully');
        process.exit(0);
      } else {
        console.error('Cache recovery failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  recoverCache
};
