/**
 * Vendor Portal Authentication Flow Integration Tests
 * 
 * Tests the complete authentication workflow for vendor portal access
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { createServer } from 'http';
import { AddressInfo } from 'net';
import axios from 'axios';
import jwt from 'jsonwebtoken';

// Mock authentication service
class VendorAuthService {
  constructor() {
    this.vendors = new Map([
      ['<EMAIL>', {
        id: 'vendor-1',
        email: '<EMAIL>',
        password: '$2b$10$hashedpassword', // bcrypt hash of 'password123'
        name: 'Test Vendor',
        status: 'active',
        company: 'Test Company',
        role: 'vendor'
      }],
      ['<EMAIL>', {
        id: 'admin-1',
        email: '<EMAIL>',
        password: '$2b$10$hashedadminpass', // bcrypt hash of 'admin123'
        name: 'Admin User',
        status: 'active',
        company: 'MVS-VR',
        role: 'admin'
      }]
    ]);
    this.sessions = new Map();
    this.refreshTokens = new Map();
  }

  async login(email, password) {
    const vendor = this.vendors.get(email);
    if (!vendor) {
      throw new Error('Invalid credentials');
    }

    // In real implementation, use bcrypt.compare
    const isValidPassword = password === 'password123' || (email === '<EMAIL>' && password === 'admin123');
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Generate tokens
    const accessToken = jwt.sign(
      { 
        id: vendor.id, 
        email: vendor.email, 
        role: vendor.role 
      },
      'secret-key',
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      { id: vendor.id },
      'refresh-secret',
      { expiresIn: '7d' }
    );

    // Store session
    this.sessions.set(accessToken, {
      vendorId: vendor.id,
      email: vendor.email,
      role: vendor.role,
      createdAt: new Date()
    });

    this.refreshTokens.set(refreshToken, vendor.id);

    return {
      accessToken,
      refreshToken,
      vendor: {
        id: vendor.id,
        email: vendor.email,
        name: vendor.name,
        company: vendor.company,
        role: vendor.role
      }
    };
  }

  async refreshToken(refreshToken) {
    const vendorId = this.refreshTokens.get(refreshToken);
    if (!vendorId) {
      throw new Error('Invalid refresh token');
    }

    const vendor = Array.from(this.vendors.values()).find(v => v.id === vendorId);
    if (!vendor) {
      throw new Error('Vendor not found');
    }

    // Generate new access token
    const accessToken = jwt.sign(
      { 
        id: vendor.id, 
        email: vendor.email, 
        role: vendor.role 
      },
      'secret-key',
      { expiresIn: '15m' }
    );

    this.sessions.set(accessToken, {
      vendorId: vendor.id,
      email: vendor.email,
      role: vendor.role,
      createdAt: new Date()
    });

    return { accessToken };
  }

  async validateToken(token) {
    try {
      const decoded = jwt.verify(token, 'secret-key');
      const session = this.sessions.get(token);
      
      if (!session) {
        throw new Error('Session not found');
      }

      return {
        valid: true,
        vendor: {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role
        }
      };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  async logout(token) {
    this.sessions.delete(token);
    return { success: true };
  }
}

describe('Vendor Portal Authentication Flow Integration', () => {
  let server;
  let baseURL;
  let authService;

  beforeEach(async () => {
    authService = new VendorAuthService();

    // Create mock authentication server
    server = createServer((req, res) => {
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      const url = new URL(req.url, `http://${req.headers.host}`);
      
      if (req.method === 'POST' && url.pathname === '/auth/login') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
          try {
            const { email, password } = JSON.parse(body);
            const result = await authService.login(email, password);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(result));
          } catch (error) {
            res.writeHead(401, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: error.message }));
          }
        });
      } else if (req.method === 'POST' && url.pathname === '/auth/refresh') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
          try {
            const { refreshToken } = JSON.parse(body);
            const result = await authService.refreshToken(refreshToken);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(result));
          } catch (error) {
            res.writeHead(401, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: error.message }));
          }
        });
      } else if (req.method === 'GET' && url.pathname === '/auth/validate') {
        const authHeader = req.headers.authorization;
        const token = authHeader?.replace('Bearer ', '');
        
        if (!token) {
          res.writeHead(401, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({ error: 'No token provided' }));
          return;
        }

        authService.validateToken(token).then(result => {
          if (result.valid) {
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(result));
          } else {
            res.writeHead(401, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: result.error }));
          }
        });
      } else if (req.method === 'POST' && url.pathname === '/auth/logout') {
        const authHeader = req.headers.authorization;
        const token = authHeader?.replace('Bearer ', '');
        
        authService.logout(token).then(result => {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify(result));
        });
      } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Not found' }));
      }
    });

    await new Promise((resolve) => {
      server.listen(0, () => {
        const address = server.address() as AddressInfo;
        baseURL = `http://localhost:${address.port}`;
        resolve(undefined);
      });
    });
  });

  afterEach(async () => {
    if (server) {
      await new Promise((resolve) => {
        server.close(resolve);
      });
    }
  });

  describe('Vendor Login Flow', () => {
    it('should authenticate vendor with valid credentials', async () => {
      const response = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(response.status).toBe(200);
      expect(response.data).toMatchObject({
        accessToken: expect.any(String),
        refreshToken: expect.any(String),
        vendor: {
          id: 'vendor-1',
          email: '<EMAIL>',
          name: 'Test Vendor',
          role: 'vendor'
        }
      });
    });

    it('should reject invalid credentials', async () => {
      try {
        await axios.post(`${baseURL}/auth/login`, {
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Invalid credentials');
      }
    });

    it('should reject non-existent vendor', async () => {
      try {
        await axios.post(`${baseURL}/auth/login`, {
          email: '<EMAIL>',
          password: 'password123'
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Invalid credentials');
      }
    });
  });

  describe('Admin Login Flow', () => {
    it('should authenticate admin with valid credentials', async () => {
      const response = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });

      expect(response.status).toBe(200);
      expect(response.data.vendor.role).toBe('admin');
      expect(response.data.vendor.email).toBe('<EMAIL>');
    });
  });

  describe('Token Validation', () => {
    it('should validate valid access token', async () => {
      // First login to get token
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      const { accessToken } = loginResponse.data;

      // Validate token
      const validateResponse = await axios.get(`${baseURL}/auth/validate`, {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      });

      expect(validateResponse.status).toBe(200);
      expect(validateResponse.data.valid).toBe(true);
      expect(validateResponse.data.vendor).toMatchObject({
        id: 'vendor-1',
        email: '<EMAIL>',
        role: 'vendor'
      });
    });

    it('should reject invalid access token', async () => {
      try {
        await axios.get(`${baseURL}/auth/validate`, {
          headers: {
            Authorization: 'Bearer invalid-token'
          }
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBeDefined();
      }
    });

    it('should reject missing access token', async () => {
      try {
        await axios.get(`${baseURL}/auth/validate`);
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('No token provided');
      }
    });
  });

  describe('Token Refresh Flow', () => {
    it('should refresh access token with valid refresh token', async () => {
      // First login to get tokens
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      const { refreshToken } = loginResponse.data;

      // Refresh token
      const refreshResponse = await axios.post(`${baseURL}/auth/refresh`, {
        refreshToken
      });

      expect(refreshResponse.status).toBe(200);
      expect(refreshResponse.data.accessToken).toBeDefined();
      expect(refreshResponse.data.accessToken).not.toBe(loginResponse.data.accessToken);
    });

    it('should reject invalid refresh token', async () => {
      try {
        await axios.post(`${baseURL}/auth/refresh`, {
          refreshToken: 'invalid-refresh-token'
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data.error).toBe('Invalid refresh token');
      }
    });
  });

  describe('Logout Flow', () => {
    it('should logout and invalidate session', async () => {
      // First login
      const loginResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      const { accessToken } = loginResponse.data;

      // Logout
      const logoutResponse = await axios.post(`${baseURL}/auth/logout`, {}, {
        headers: {
          Authorization: `Bearer ${accessToken}`
        }
      });

      expect(logoutResponse.status).toBe(200);
      expect(logoutResponse.data.success).toBe(true);

      // Try to validate the token after logout
      try {
        await axios.get(`${baseURL}/auth/validate`, {
          headers: {
            Authorization: `Bearer ${accessToken}`
          }
        });
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });
  });

  describe('Role-Based Access Control', () => {
    it('should provide different access levels for vendor vs admin', async () => {
      // Login as vendor
      const vendorResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });

      // Login as admin
      const adminResponse = await axios.post(`${baseURL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });

      expect(vendorResponse.data.vendor.role).toBe('vendor');
      expect(adminResponse.data.vendor.role).toBe('admin');
    });
  });
});
