/**
 * Event Dispatcher Service
 * Implements pub/sub pattern with event routing, filtering, and persistence
 */

import { EventEmitter } from 'events';
import Redis from 'ioredis';
import { performance } from 'perf_hooks';

export class EventDispatcher extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      redis: options.redis || null,
      enablePersistence: options.enablePersistence || true,
      enableEventSourcing: options.enableEventSourcing || false,
      maxEventHistory: options.maxEventHistory || 1000,
      eventTTL: options.eventTTL || 86400, // 24 hours in seconds
      enableFiltering: options.enableFiltering || true,
      enableBatching: options.enableBatching || true,
      batchSize: options.batchSize || 100,
      batchTimeout: options.batchTimeout || 1000,
      enableMetrics: options.enableMetrics || true,
      ...options
    };

    // Event management
    this.subscribers = new Map(); // channel -> Set of subscriber functions
    this.filters = new Map(); // channel -> filter functions
    this.eventHistory = new Map(); // channel -> array of events
    this.eventStore = new Map(); // eventId -> event data
    
    // Batching
    this.batchQueues = new Map(); // channel -> array of events
    this.batchTimers = new Map(); // channel -> timer
    
    // Metrics
    this.metrics = {
      eventsPublished: 0,
      eventsDelivered: 0,
      subscribersCount: 0,
      averageDeliveryTime: 0,
      errorCount: 0,
      lastReset: Date.now()
    };

    // Redis for clustering
    this.redis = null;
    this.redisSubscriber = null;
    
    this.initialize();
  }

  async initialize() {
    try {
      if (this.options.redis) {
        this.redis = new Redis(this.options.redis);
        this.redisSubscriber = new Redis(this.options.redis);
        
        await this.redis.ping();
        console.log('✅ Redis connection established for Event Dispatcher');
        
        // Setup Redis pub/sub for clustering
        this.setupRedisSubscription();
      }

      if (this.options.enableMetrics) {
        this.startMetricsCollection();
      }

      console.log('🎯 Event Dispatcher initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize Event Dispatcher:', error);
      this.emit('error', error);
    }
  }

  setupRedisSubscription() {
    this.redisSubscriber.on('message', (channel, message) => {
      try {
        const event = JSON.parse(message);
        this.deliverEventLocally(channel, event, { fromRedis: true });
      } catch (error) {
        console.error('Error processing Redis message:', error);
        this.metrics.errorCount++;
      }
    });

    this.redisSubscriber.on('error', (error) => {
      console.error('Redis subscriber error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Subscribe to events on a channel
   * @param {string} channel - Channel name
   * @param {Function} handler - Event handler function
   * @param {Object} options - Subscription options
   */
  subscribe(channel, handler, options = {}) {
    if (!this.subscribers.has(channel)) {
      this.subscribers.set(channel, new Set());
      
      // Subscribe to Redis channel for clustering
      if (this.redisSubscriber) {
        this.redisSubscriber.subscribe(channel);
      }
    }

    const subscription = {
      handler,
      options: {
        filter: options.filter || null,
        priority: options.priority || 0,
        once: options.once || false,
        metadata: options.metadata || {},
        ...options
      },
      subscribedAt: Date.now(),
      id: this.generateSubscriptionId()
    };

    this.subscribers.get(channel).add(subscription);
    this.metrics.subscribersCount++;

    console.log(`📡 New subscription to ${channel} (ID: ${subscription.id})`);
    
    // Send recent events if requested
    if (options.sendHistory && this.eventHistory.has(channel)) {
      const history = this.eventHistory.get(channel);
      const recentEvents = history.slice(-options.historyLimit || 10);
      
      setImmediate(() => {
        recentEvents.forEach(event => {
          this.deliverEventToSubscription(subscription, channel, event);
        });
      });
    }

    return subscription.id;
  }

  /**
   * Unsubscribe from events on a channel
   * @param {string} channel - Channel name
   * @param {string} subscriptionId - Subscription ID to remove
   */
  unsubscribe(channel, subscriptionId) {
    if (!this.subscribers.has(channel)) {
      return false;
    }

    const subscribers = this.subscribers.get(channel);
    const subscription = Array.from(subscribers).find(sub => sub.id === subscriptionId);
    
    if (subscription) {
      subscribers.delete(subscription);
      this.metrics.subscribersCount--;
      
      // If no more subscribers, unsubscribe from Redis
      if (subscribers.size === 0) {
        this.subscribers.delete(channel);
        if (this.redisSubscriber) {
          this.redisSubscriber.unsubscribe(channel);
        }
      }
      
      console.log(`📡 Unsubscribed from ${channel} (ID: ${subscriptionId})`);
      return true;
    }
    
    return false;
  }

  /**
   * Publish an event to a channel
   * @param {string} channel - Channel name
   * @param {Object} data - Event data
   * @param {Object} options - Publishing options
   */
  async publish(channel, data, options = {}) {
    const startTime = performance.now();
    
    const event = {
      id: this.generateEventId(),
      channel,
      data,
      timestamp: Date.now(),
      source: options.source || 'local',
      metadata: options.metadata || {},
      version: options.version || 1,
      correlationId: options.correlationId || null,
      causationId: options.causationId || null
    };

    try {
      // Store event if persistence is enabled
      if (this.options.enablePersistence) {
        await this.storeEvent(event);
      }

      // Add to event history
      this.addToEventHistory(channel, event);

      // Publish to Redis for clustering
      if (this.redis && !options.localOnly) {
        await this.redis.publish(channel, JSON.stringify(event));
      }

      // Deliver to local subscribers
      await this.deliverEventLocally(channel, event, { fromRedis: false });

      this.metrics.eventsPublished++;
      
      const deliveryTime = performance.now() - startTime;
      this.updateDeliveryTimeMetrics(deliveryTime);

      console.log(`📤 Event published to ${channel}: ${event.id}`);
      this.emit('eventPublished', { channel, event, deliveryTime });

      return event.id;
    } catch (error) {
      console.error(`Failed to publish event to ${channel}:`, error);
      this.metrics.errorCount++;
      this.emit('publishError', { channel, event, error });
      throw error;
    }
  }

  /**
   * Publish multiple events as a batch
   * @param {Array} events - Array of {channel, data, options} objects
   */
  async publishBatch(events) {
    const results = [];
    
    for (const { channel, data, options } of events) {
      try {
        const eventId = await this.publish(channel, data, options);
        results.push({ success: true, eventId, channel });
      } catch (error) {
        results.push({ success: false, error: error.message, channel });
      }
    }

    return results;
  }

  async deliverEventLocally(channel, event, context = {}) {
    if (!this.subscribers.has(channel)) {
      return;
    }

    const subscribers = Array.from(this.subscribers.get(channel));
    
    // Sort by priority (higher priority first)
    subscribers.sort((a, b) => b.options.priority - a.options.priority);

    if (this.options.enableBatching && !context.fromRedis) {
      this.addToBatch(channel, event, subscribers);
    } else {
      await this.deliverEventToSubscribers(subscribers, channel, event);
    }
  }

  async deliverEventToSubscribers(subscribers, channel, event) {
    const deliveryPromises = subscribers.map(subscription => 
      this.deliverEventToSubscription(subscription, channel, event)
    );

    const results = await Promise.allSettled(deliveryPromises);
    
    let successCount = 0;
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successCount++;
        
        // Remove one-time subscriptions
        if (subscribers[index].options.once) {
          this.unsubscribe(channel, subscribers[index].id);
        }
      } else {
        console.error(`Delivery failed for subscription ${subscribers[index].id}:`, result.reason);
        this.metrics.errorCount++;
      }
    });

    this.metrics.eventsDelivered += successCount;
    return successCount;
  }

  async deliverEventToSubscription(subscription, channel, event) {
    try {
      // Apply filters if enabled
      if (this.options.enableFiltering && subscription.options.filter) {
        if (!subscription.options.filter(event)) {
          return false; // Event filtered out
        }
      }

      // Create delivery context
      const context = {
        subscription: {
          id: subscription.id,
          subscribedAt: subscription.subscribedAt,
          metadata: subscription.options.metadata
        },
        channel,
        timestamp: Date.now()
      };

      // Call the handler
      await subscription.handler(event, context);
      return true;
    } catch (error) {
      console.error(`Handler error for subscription ${subscription.id}:`, error);
      this.emit('handlerError', { subscription, channel, event, error });
      throw error;
    }
  }

  addToBatch(channel, event, subscribers) {
    if (!this.batchQueues.has(channel)) {
      this.batchQueues.set(channel, []);
    }

    this.batchQueues.get(channel).push({ event, subscribers });

    // Set timer for batch processing if not already set
    if (!this.batchTimers.has(channel)) {
      const timer = setTimeout(() => {
        this.processBatch(channel);
      }, this.options.batchTimeout);
      
      this.batchTimers.set(channel, timer);
    }

    // Process batch if it reaches the size limit
    if (this.batchQueues.get(channel).length >= this.options.batchSize) {
      this.processBatch(channel);
    }
  }

  async processBatch(channel) {
    const batch = this.batchQueues.get(channel) || [];
    if (batch.length === 0) return;

    // Clear batch and timer
    this.batchQueues.set(channel, []);
    const timer = this.batchTimers.get(channel);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(channel);
    }

    console.log(`📦 Processing batch for ${channel}: ${batch.length} events`);

    // Group events by subscriber to optimize delivery
    const subscriberEvents = new Map();
    
    batch.forEach(({ event, subscribers }) => {
      subscribers.forEach(subscription => {
        if (!subscriberEvents.has(subscription.id)) {
          subscriberEvents.set(subscription.id, { subscription, events: [] });
        }
        subscriberEvents.get(subscription.id).events.push(event);
      });
    });

    // Deliver batched events to each subscriber
    const deliveryPromises = Array.from(subscriberEvents.values()).map(
      ({ subscription, events }) => this.deliverBatchToSubscription(subscription, channel, events)
    );

    await Promise.allSettled(deliveryPromises);
  }

  async deliverBatchToSubscription(subscription, channel, events) {
    try {
      // Filter events if filtering is enabled
      let filteredEvents = events;
      if (this.options.enableFiltering && subscription.options.filter) {
        filteredEvents = events.filter(subscription.options.filter);
      }

      if (filteredEvents.length === 0) return;

      // Create batch delivery context
      const context = {
        subscription: {
          id: subscription.id,
          subscribedAt: subscription.subscribedAt,
          metadata: subscription.options.metadata
        },
        channel,
        timestamp: Date.now(),
        batchSize: filteredEvents.length
      };

      // Check if handler supports batch delivery
      if (subscription.options.supportsBatch) {
        await subscription.handler(filteredEvents, context);
      } else {
        // Deliver events individually
        for (const event of filteredEvents) {
          await subscription.handler(event, context);
        }
      }

      this.metrics.eventsDelivered += filteredEvents.length;
    } catch (error) {
      console.error(`Batch delivery error for subscription ${subscription.id}:`, error);
      this.emit('batchDeliveryError', { subscription, channel, events, error });
    }
  }

  async storeEvent(event) {
    if (this.options.enableEventSourcing) {
      this.eventStore.set(event.id, event);
    }

    if (this.redis) {
      const key = `event:${event.id}`;
      await this.redis.setex(key, this.options.eventTTL, JSON.stringify(event));
    }
  }

  addToEventHistory(channel, event) {
    if (!this.eventHistory.has(channel)) {
      this.eventHistory.set(channel, []);
    }

    const history = this.eventHistory.get(channel);
    history.push(event);

    // Maintain history size limit
    if (history.length > this.options.maxEventHistory) {
      history.shift();
    }
  }

  /**
   * Get event history for a channel
   * @param {string} channel - Channel name
   * @param {Object} options - Query options
   */
  getEventHistory(channel, options = {}) {
    const history = this.eventHistory.get(channel) || [];
    
    let filteredHistory = history;

    // Apply time range filter
    if (options.since) {
      filteredHistory = filteredHistory.filter(event => event.timestamp >= options.since);
    }
    
    if (options.until) {
      filteredHistory = filteredHistory.filter(event => event.timestamp <= options.until);
    }

    // Apply limit
    if (options.limit) {
      filteredHistory = filteredHistory.slice(-options.limit);
    }

    return filteredHistory;
  }

  /**
   * Replay events from history
   * @param {string} channel - Channel name
   * @param {Object} options - Replay options
   */
  async replayEvents(channel, options = {}) {
    const events = this.getEventHistory(channel, options);
    
    console.log(`🔄 Replaying ${events.length} events for ${channel}`);
    
    for (const event of events) {
      await this.deliverEventLocally(channel, event, { fromReplay: true });
      
      // Add delay between events if specified
      if (options.delay) {
        await new Promise(resolve => setTimeout(resolve, options.delay));
      }
    }

    return events.length;
  }

  generateEventId() {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  generateSubscriptionId() {
    return `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  startMetricsCollection() {
    setInterval(() => {
      this.updateMetrics();
    }, 5000);
  }

  updateMetrics() {
    const now = Date.now();
    const timeDiff = now - this.metrics.lastReset;
    
    // Calculate rates
    this.metrics.publishRate = this.metrics.eventsPublished / (timeDiff / 1000);
    this.metrics.deliveryRate = this.metrics.eventsDelivered / (timeDiff / 1000);
    this.metrics.errorRate = this.metrics.errorCount / (timeDiff / 1000);
    
    this.metrics.lastReset = now;
    
    // Reset counters
    this.metrics.eventsPublished = 0;
    this.metrics.eventsDelivered = 0;
    this.metrics.errorCount = 0;

    this.emit('metrics', this.metrics);
  }

  updateDeliveryTimeMetrics(deliveryTime) {
    // Simple moving average
    this.metrics.averageDeliveryTime = 
      (this.metrics.averageDeliveryTime * 0.9) + (deliveryTime * 0.1);
  }

  getMetrics() {
    return {
      ...this.metrics,
      channels: this.subscribers.size,
      totalSubscribers: this.metrics.subscribersCount,
      eventHistorySize: Array.from(this.eventHistory.values())
        .reduce((sum, history) => sum + history.length, 0),
      eventStoreSize: this.eventStore.size,
      batchQueuesSize: Array.from(this.batchQueues.values())
        .reduce((sum, queue) => sum + queue.length, 0)
    };
  }

  getChannelInfo(channel) {
    const subscribers = this.subscribers.get(channel);
    const history = this.eventHistory.get(channel) || [];
    const batchQueue = this.batchQueues.get(channel) || [];

    return {
      channel,
      subscriberCount: subscribers ? subscribers.size : 0,
      subscribers: subscribers ? Array.from(subscribers).map(sub => ({
        id: sub.id,
        subscribedAt: sub.subscribedAt,
        options: sub.options
      })) : [],
      historySize: history.length,
      batchQueueSize: batchQueue.length,
      lastEvent: history[history.length - 1] || null
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down Event Dispatcher...');
    
    // Process remaining batches
    for (const channel of this.batchQueues.keys()) {
      await this.processBatch(channel);
    }

    // Clear all timers
    for (const timer of this.batchTimers.values()) {
      clearTimeout(timer);
    }

    // Close Redis connections
    if (this.redis) {
      await this.redis.quit();
    }
    
    if (this.redisSubscriber) {
      await this.redisSubscriber.quit();
    }

    console.log('✅ Event Dispatcher shutdown complete');
  }
}

export default EventDispatcher;
