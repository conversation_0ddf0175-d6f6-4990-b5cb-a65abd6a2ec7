/**
 * Business Recovery Prioritizer
 * 
 * This service prioritizes recovery operations based on business impact,
 * service dependencies, and recovery objectives to optimize business continuity.
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const EventEmitter = require('events');

const readFileAsync = promisify(fs.readFile);
const writeFileAsync = promisify(fs.writeFile);
const mkdirAsync = promisify(fs.mkdir);
const existsAsync = promisify(fs.exists);

const logger = require('../../utils/logger').getLogger('business-recovery-prioritizer');

// Recovery priority levels
const PRIORITY_LEVELS = {
  CRITICAL: 1,
  HIGH: 2,
  MEDIUM: 3,
  LOW: 4,
  DEFERRED: 5
};

// Recovery strategies
const RECOVERY_STRATEGIES = {
  IMMEDIATE: 'immediate',
  PARALLEL: 'parallel',
  SEQUENTIAL: 'sequential',
  DEFERRED: 'deferred'
};

// Business impact categories
const IMPACT_CATEGORIES = {
  REVENUE: 'revenue',
  CUSTOMER: 'customer',
  OPERATIONAL: 'operational',
  COMPLIANCE: 'compliance',
  REPUTATION: 'reputation'
};

class BusinessRecoveryPrioritizer extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      configPath: options.configPath || path.join(__dirname, '../../config/recovery-priorities.json'),
      outputPath: options.outputPath || path.join(__dirname, '../../data/recovery-plans'),
      businessImpactWeights: options.businessImpactWeights || {
        revenue: 0.3,
        customer: 0.25,
        operational: 0.2,
        compliance: 0.15,
        reputation: 0.1
      },
      ...options
    };
    
    // Recovery data
    this.services = new Map();
    this.recoveryPlans = new Map();
    this.priorityMatrix = new Map();
    this.dependencyGraph = new Map();
    
    // Statistics
    this.stats = {
      totalServices: 0,
      criticalServices: 0,
      highPriorityServices: 0,
      recoveryPlansGenerated: 0,
      lastPrioritization: null,
      startTime: Date.now()
    };
    
    this.initialize();
  }
  
  /**
   * Initialize the recovery prioritizer
   */
  async initialize() {
    try {
      // Ensure output directory exists
      if (!await existsAsync(this.options.outputPath)) {
        await mkdirAsync(this.options.outputPath, { recursive: true });
      }
      
      // Load configuration
      await this.loadConfiguration();
      
      // Build priority matrix
      this.buildPriorityMatrix();
      
      logger.info('Business recovery prioritizer initialized');
    } catch (error) {
      logger.error('Failed to initialize business recovery prioritizer:', error);
      throw error;
    }
  }
  
  /**
   * Load recovery configuration
   */
  async loadConfiguration() {
    try {
      const configData = await readFileAsync(this.options.configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // Load services with recovery information
      for (const serviceConfig of config.services || []) {
        const service = {
          id: serviceConfig.id,
          name: serviceConfig.name,
          type: serviceConfig.type,
          businessCriticality: serviceConfig.businessCriticality || 3,
          rto: serviceConfig.rto || 240, // Recovery Time Objective in minutes
          rpo: serviceConfig.rpo || 60,  // Recovery Point Objective in minutes
          dependencies: serviceConfig.dependencies || [],
          businessImpact: serviceConfig.businessImpact || {},
          recoveryProcedures: serviceConfig.recoveryProcedures || [],
          resources: serviceConfig.resources || {},
          contacts: serviceConfig.contacts || []
        };
        
        this.services.set(service.id, service);
      }
      
      // Build dependency graph
      this.buildDependencyGraph();
      
      this.stats.totalServices = this.services.size;
      logger.info(`Loaded ${this.services.size} services for recovery prioritization`);
      
    } catch (error) {
      logger.error('Failed to load recovery configuration:', error);
      throw error;
    }
  }
  
  /**
   * Build dependency graph
   */
  buildDependencyGraph() {
    this.dependencyGraph.clear();
    
    for (const [serviceId, service] of this.services.entries()) {
      const dependencies = new Set();
      const dependents = new Set();
      
      // Add direct dependencies
      for (const depId of service.dependencies) {
        if (this.services.has(depId)) {
          dependencies.add(depId);
        }
      }
      
      // Find dependents
      for (const [otherId, otherService] of this.services.entries()) {
        if (otherService.dependencies.includes(serviceId)) {
          dependents.add(otherId);
        }
      }
      
      this.dependencyGraph.set(serviceId, {
        dependencies: Array.from(dependencies),
        dependents: Array.from(dependents),
        depth: this.calculateDependencyDepth(serviceId, new Set())
      });
    }
  }
  
  /**
   * Calculate dependency depth for topological sorting
   * @param {string} serviceId - Service ID
   * @param {Set} visited - Visited services to prevent cycles
   * @returns {number} Dependency depth
   */
  calculateDependencyDepth(serviceId, visited) {
    if (visited.has(serviceId)) {
      return 0; // Circular dependency, return 0
    }
    
    visited.add(serviceId);
    const service = this.services.get(serviceId);
    
    if (!service || service.dependencies.length === 0) {
      return 0;
    }
    
    let maxDepth = 0;
    for (const depId of service.dependencies) {
      const depDepth = this.calculateDependencyDepth(depId, new Set(visited));
      maxDepth = Math.max(maxDepth, depDepth + 1);
    }
    
    return maxDepth;
  }
  
  /**
   * Build priority matrix
   */
  buildPriorityMatrix() {
    this.priorityMatrix.clear();
    
    for (const [serviceId, service] of this.services.entries()) {
      const priority = this.calculateRecoveryPriority(service);
      this.priorityMatrix.set(serviceId, priority);
      
      // Update statistics
      if (priority.level === PRIORITY_LEVELS.CRITICAL) {
        this.stats.criticalServices++;
      } else if (priority.level === PRIORITY_LEVELS.HIGH) {
        this.stats.highPriorityServices++;
      }
    }
    
    logger.debug('Built recovery priority matrix');
  }
  
  /**
   * Calculate recovery priority for a service
   * @param {Object} service - Service configuration
   * @returns {Object} Priority assessment
   */
  calculateRecoveryPriority(service) {
    // Business criticality score (1-5)
    const criticalityScore = service.businessCriticality || 3;
    
    // RTO urgency (lower RTO = higher urgency)
    const rtoUrgency = Math.max(1, 6 - Math.ceil(service.rto / 60)); // Convert to hours and invert
    
    // Dependency impact (more dependents = higher priority)
    const dependents = this.dependencyGraph.get(service.id)?.dependents || [];
    const dependencyImpact = Math.min(5, 1 + dependents.length * 0.5);
    
    // Business impact score
    const businessImpact = this.calculateBusinessImpactScore(service);
    
    // Calculate weighted priority score
    const priorityScore = (
      criticalityScore * 0.3 +
      rtoUrgency * 0.25 +
      dependencyImpact * 0.2 +
      businessImpact * 0.25
    );
    
    // Determine priority level
    let priorityLevel;
    if (priorityScore >= 4.5) {
      priorityLevel = PRIORITY_LEVELS.CRITICAL;
    } else if (priorityScore >= 3.5) {
      priorityLevel = PRIORITY_LEVELS.HIGH;
    } else if (priorityScore >= 2.5) {
      priorityLevel = PRIORITY_LEVELS.MEDIUM;
    } else if (priorityScore >= 1.5) {
      priorityLevel = PRIORITY_LEVELS.LOW;
    } else {
      priorityLevel = PRIORITY_LEVELS.DEFERRED;
    }
    
    // Determine recovery strategy
    const recoveryStrategy = this.determineRecoveryStrategy(service, priorityLevel);
    
    return {
      serviceId: service.id,
      level: priorityLevel,
      score: Math.round(priorityScore * 100) / 100,
      strategy: recoveryStrategy,
      estimatedRecoveryTime: this.estimateRecoveryTime(service, recoveryStrategy),
      resourceRequirements: this.calculateResourceRequirements(service),
      businessImpact: {
        score: businessImpact,
        categories: this.categorizeBusinessImpact(service)
      },
      dependencies: {
        count: service.dependencies.length,
        dependents: dependents.length,
        depth: this.dependencyGraph.get(service.id)?.depth || 0
      }
    };
  }
  
  /**
   * Calculate business impact score
   * @param {Object} service - Service configuration
   * @returns {number} Business impact score (1-5)
   */
  calculateBusinessImpactScore(service) {
    const impact = service.businessImpact || {};
    const weights = this.options.businessImpactWeights;
    
    let totalScore = 0;
    let totalWeight = 0;
    
    for (const [category, weight] of Object.entries(weights)) {
      if (impact[category] !== undefined) {
        totalScore += impact[category] * weight;
        totalWeight += weight;
      }
    }
    
    return totalWeight > 0 ? totalScore / totalWeight : 3; // Default to medium impact
  }
  
  /**
   * Categorize business impact
   * @param {Object} service - Service configuration
   * @returns {Object} Impact categories
   */
  categorizeBusinessImpact(service) {
    const impact = service.businessImpact || {};
    const categories = {};
    
    for (const category of Object.values(IMPACT_CATEGORIES)) {
      categories[category] = impact[category] || 0;
    }
    
    return categories;
  }
  
  /**
   * Determine recovery strategy
   * @param {Object} service - Service configuration
   * @param {number} priorityLevel - Priority level
   * @returns {string} Recovery strategy
   */
  determineRecoveryStrategy(service, priorityLevel) {
    const dependencies = service.dependencies || [];
    
    if (priorityLevel === PRIORITY_LEVELS.CRITICAL) {
      return dependencies.length === 0 ? RECOVERY_STRATEGIES.IMMEDIATE : RECOVERY_STRATEGIES.PARALLEL;
    } else if (priorityLevel === PRIORITY_LEVELS.HIGH) {
      return RECOVERY_STRATEGIES.PARALLEL;
    } else if (priorityLevel === PRIORITY_LEVELS.MEDIUM) {
      return RECOVERY_STRATEGIES.SEQUENTIAL;
    } else {
      return RECOVERY_STRATEGIES.DEFERRED;
    }
  }
  
  /**
   * Estimate recovery time
   * @param {Object} service - Service configuration
   * @param {string} strategy - Recovery strategy
   * @returns {number} Estimated recovery time in minutes
   */
  estimateRecoveryTime(service, strategy) {
    const baseRTO = service.rto || 240;
    
    switch (strategy) {
      case RECOVERY_STRATEGIES.IMMEDIATE:
        return Math.min(baseRTO, 30); // Maximum 30 minutes for immediate recovery
      case RECOVERY_STRATEGIES.PARALLEL:
        return Math.min(baseRTO, 60); // Maximum 1 hour for parallel recovery
      case RECOVERY_STRATEGIES.SEQUENTIAL:
        return baseRTO;
      case RECOVERY_STRATEGIES.DEFERRED:
        return baseRTO * 2; // Allow longer recovery time for deferred services
      default:
        return baseRTO;
    }
  }
  
  /**
   * Calculate resource requirements
   * @param {Object} service - Service configuration
   * @returns {Object} Resource requirements
   */
  calculateResourceRequirements(service) {
    const resources = service.resources || {};
    
    return {
      personnel: resources.personnel || 1,
      infrastructure: resources.infrastructure || 'standard',
      budget: resources.budget || 'medium',
      expertise: resources.expertise || 'standard',
      tools: resources.tools || []
    };
  }
  
  /**
   * Generate recovery plan
   * @param {Array<string>} affectedServices - List of affected service IDs
   * @returns {Object} Recovery plan
   */
  generateRecoveryPlan(affectedServices) {
    const plan = {
      id: `recovery-plan-${Date.now()}`,
      timestamp: new Date().toISOString(),
      affectedServices: affectedServices.length,
      phases: [],
      totalEstimatedTime: 0,
      resourceRequirements: {
        personnel: 0,
        budget: 'medium',
        criticalPath: []
      }
    };
    
    // Get priorities for affected services
    const servicePriorities = affectedServices
      .map(serviceId => ({
        serviceId,
        service: this.services.get(serviceId),
        priority: this.priorityMatrix.get(serviceId)
      }))
      .filter(item => item.service && item.priority)
      .sort((a, b) => {
        // Sort by priority level first, then by dependency depth
        if (a.priority.level !== b.priority.level) {
          return a.priority.level - b.priority.level;
        }
        return a.priority.dependencies.depth - b.priority.dependencies.depth;
      });
    
    // Group services by recovery strategy and priority
    const recoveryGroups = this.groupServicesByRecoveryStrategy(servicePriorities);
    
    // Create recovery phases
    let phaseNumber = 1;
    let cumulativeTime = 0;
    
    for (const [strategy, services] of recoveryGroups.entries()) {
      if (services.length === 0) continue;
      
      const phase = {
        phase: phaseNumber++,
        strategy,
        services: services.map(item => ({
          serviceId: item.serviceId,
          serviceName: item.service.name,
          priority: item.priority.level,
          estimatedTime: item.priority.estimatedRecoveryTime,
          dependencies: item.service.dependencies,
          procedures: item.service.recoveryProcedures
        })),
        parallelExecution: strategy === RECOVERY_STRATEGIES.PARALLEL || strategy === RECOVERY_STRATEGIES.IMMEDIATE,
        estimatedDuration: this.calculatePhaseDuration(services, strategy),
        resourceRequirements: this.calculatePhaseResources(services)
      };
      
      plan.phases.push(phase);
      
      // Update cumulative time
      if (phase.parallelExecution) {
        cumulativeTime = Math.max(cumulativeTime, phase.estimatedDuration);
      } else {
        cumulativeTime += phase.estimatedDuration;
      }
      
      // Update resource requirements
      plan.resourceRequirements.personnel += phase.resourceRequirements.personnel;
    }
    
    plan.totalEstimatedTime = cumulativeTime;
    plan.resourceRequirements.criticalPath = this.calculateCriticalPath(servicePriorities);
    
    // Store the plan
    this.recoveryPlans.set(plan.id, plan);
    this.stats.recoveryPlansGenerated++;
    
    logger.info(`Generated recovery plan ${plan.id} for ${affectedServices.length} services`);
    
    return plan;
  }
  
  /**
   * Group services by recovery strategy
   * @param {Array<Object>} servicePriorities - Service priorities
   * @returns {Map} Grouped services
   */
  groupServicesByRecoveryStrategy(servicePriorities) {
    const groups = new Map();
    
    // Initialize groups
    for (const strategy of Object.values(RECOVERY_STRATEGIES)) {
      groups.set(strategy, []);
    }
    
    // Group services
    for (const item of servicePriorities) {
      const strategy = item.priority.strategy;
      groups.get(strategy).push(item);
    }
    
    return groups;
  }
  
  /**
   * Calculate phase duration
   * @param {Array<Object>} services - Services in phase
   * @param {string} strategy - Recovery strategy
   * @returns {number} Phase duration in minutes
   */
  calculatePhaseDuration(services, strategy) {
    if (strategy === RECOVERY_STRATEGIES.PARALLEL || strategy === RECOVERY_STRATEGIES.IMMEDIATE) {
      // For parallel execution, duration is the maximum of all services
      return Math.max(...services.map(item => item.priority.estimatedRecoveryTime));
    } else {
      // For sequential execution, duration is the sum of all services
      return services.reduce((total, item) => total + item.priority.estimatedRecoveryTime, 0);
    }
  }
  
  /**
   * Calculate phase resource requirements
   * @param {Array<Object>} services - Services in phase
   * @returns {Object} Resource requirements
   */
  calculatePhaseResources(services) {
    return {
      personnel: services.reduce((total, item) => total + item.priority.resourceRequirements.personnel, 0),
      expertise: [...new Set(services.map(item => item.priority.resourceRequirements.expertise))],
      tools: [...new Set(services.flatMap(item => item.priority.resourceRequirements.tools))]
    };
  }
  
  /**
   * Calculate critical path
   * @param {Array<Object>} servicePriorities - Service priorities
   * @returns {Array<string>} Critical path service IDs
   */
  calculateCriticalPath(servicePriorities) {
    // Simple critical path calculation based on dependencies and priority
    const criticalServices = servicePriorities
      .filter(item => item.priority.level <= PRIORITY_LEVELS.HIGH)
      .sort((a, b) => a.priority.dependencies.depth - b.priority.dependencies.depth)
      .map(item => item.serviceId);
    
    return criticalServices.slice(0, 5); // Return top 5 critical services
  }
  
  /**
   * Save recovery plan
   * @param {Object} plan - Recovery plan
   */
  async saveRecoveryPlan(plan) {
    try {
      const fileName = `${plan.id}.json`;
      const filePath = path.join(this.options.outputPath, fileName);
      
      await writeFileAsync(filePath, JSON.stringify(plan, null, 2));
      
      logger.info(`Saved recovery plan to ${fileName}`);
    } catch (error) {
      logger.error('Failed to save recovery plan:', error);
    }
  }
  
  /**
   * Get recovery priorities
   * @param {Array<string>} serviceIds - Service IDs (optional)
   * @returns {Array<Object>} Recovery priorities
   */
  getRecoveryPriorities(serviceIds = null) {
    const targetServices = serviceIds || Array.from(this.services.keys());
    
    return targetServices
      .map(serviceId => ({
        serviceId,
        service: this.services.get(serviceId),
        priority: this.priorityMatrix.get(serviceId)
      }))
      .filter(item => item.service && item.priority)
      .sort((a, b) => a.priority.level - b.priority.level);
  }
  
  /**
   * Get recovery plan
   * @param {string} planId - Plan ID
   * @returns {Object} Recovery plan
   */
  getRecoveryPlan(planId) {
    return this.recoveryPlans.get(planId);
  }
  
  /**
   * Get all recovery plans
   * @returns {Array<Object>} All recovery plans
   */
  getAllRecoveryPlans() {
    return Array.from(this.recoveryPlans.values());
  }
  
  /**
   * Get statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
      priorityDistribution: {
        critical: this.stats.criticalServices,
        high: this.stats.highPriorityServices,
        medium: this.stats.totalServices - this.stats.criticalServices - this.stats.highPriorityServices,
        total: this.stats.totalServices
      }
    };
  }
  
  /**
   * Update priorities
   */
  updatePriorities() {
    this.buildPriorityMatrix();
    this.stats.lastPrioritization = new Date().toISOString();
    this.emit('prioritiesUpdated', this.getStatistics());
  }
}

module.exports = {
  BusinessRecoveryPrioritizer,
  PRIORITY_LEVELS,
  RECOVERY_STRATEGIES,
  IMPACT_CATEGORIES
};
