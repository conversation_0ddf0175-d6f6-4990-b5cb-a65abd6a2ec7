{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1422", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/services/scene/scene-validator.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 46312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 46312, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 357, "endOffset": 461, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 613, "endOffset": 631, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 650, "endOffset": 668, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 683, "endOffset": 701, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 716, "endOffset": 734, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 753, "endOffset": 771, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 810, "endOffset": 12522, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 853, "endOffset": 966, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 981, "endOffset": 1016, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1047, "endOffset": 3808, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1095, "endOffset": 2038, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2093, "endOffset": 3244, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2842, "endOffset": 2874, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2941, "endOffset": 3032, "count": 2}, {"startOffset": 2974, "endOffset": 3032, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3115, "endOffset": 3208, "count": 3}, {"startOffset": 3149, "endOffset": 3208, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3287, "endOffset": 3802, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3747, "endOffset": 3782, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3849, "endOffset": 6943, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3900, "endOffset": 5871, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5919, "endOffset": 6937, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6984, "endOffset": 12518, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7059, "endOffset": 10171, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9561, "endOffset": 9679, "count": 3}, {"startOffset": 9609, "endOffset": 9649, "count": 1}, {"startOffset": 9650, "endOffset": 9679, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9772, "endOffset": 9849, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9942, "endOffset": 10019, "count": 2}, {"startOffset": 9988, "endOffset": 10019, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10228, "endOffset": 12512, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1423", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/services/scene/scene-validator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 141766, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 141766, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1211, "endOffset": 41420, "count": 7}], "isBlockCoverage": true}, {"functionName": "SceneValidatorService", "ranges": [{"startOffset": 1249, "endOffset": 1399, "count": 7}], "isBlockCoverage": true}, {"functionName": "validateScene", "ranges": [{"startOffset": 1504, "endOffset": 3344, "count": 3}, {"startOffset": 1691, "endOffset": 2016, "count": 1}, {"startOffset": 2016, "endOffset": 2092, "count": 2}, {"startOffset": 2092, "endOffset": 2437, "count": 1}, {"startOffset": 2268, "endOffset": 2437, "count": 0}, {"startOffset": 2437, "endOffset": 2461, "count": 2}, {"startOffset": 2461, "endOffset": 2604, "count": 0}, {"startOffset": 2604, "endOffset": 3014, "count": 2}, {"startOffset": 3014, "endOffset": 3340, "count": 0}], "isBlockCoverage": true}, {"functionName": "validateSceneData", "ranges": [{"startOffset": 3460, "endOffset": 4480, "count": 2}, {"startOffset": 3560, "endOffset": 3718, "count": 0}, {"startOffset": 3742, "endOffset": 4080, "count": 1}, {"startOffset": 3930, "endOffset": 4080, "count": 0}, {"startOffset": 4105, "endOffset": 4258, "count": 0}, {"startOffset": 4303, "endOffset": 4457, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateBlueprintInstances", "ranges": [{"startOffset": 4615, "endOffset": 7960, "count": 2}, {"startOffset": 4748, "endOffset": 4785, "count": 1}, {"startOffset": 4787, "endOffset": 4981, "count": 1}, {"startOffset": 4981, "endOffset": 7923, "count": 0}, {"startOffset": 7923, "endOffset": 7959, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4887, "endOffset": 4912, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7450, "endOffset": 7651, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7696, "endOffset": 7907, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateSceneFlow", "ranges": [{"startOffset": 8072, "endOffset": 9339, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkCircularReferences", "ranges": [{"startOffset": 9484, "endOffset": 10844, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkSpaceReferences", "ranges": [{"startOffset": 10994, "endOffset": 11591, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkDanglingNodes", "ranges": [{"startOffset": 11733, "endOffset": 12925, "count": 0}], "isBlockCoverage": false}, {"functionName": "analyzeScenePerformance", "ranges": [{"startOffset": 13056, "endOffset": 24701, "count": 2}, {"startOffset": 13253, "endOffset": 13406, "count": 0}, {"startOffset": 13803, "endOffset": 13965, "count": 0}, {"startOffset": 14006, "endOffset": 14010, "count": 1}, {"startOffset": 14105, "endOffset": 14109, "count": 1}, {"startOffset": 14221, "endOffset": 14225, "count": 1}, {"startOffset": 14321, "endOffset": 14325, "count": 1}, {"startOffset": 14383, "endOffset": 14391, "count": 1}, {"startOffset": 14392, "endOffset": 14396, "count": 1}, {"startOffset": 14501, "endOffset": 14506, "count": 0}, {"startOffset": 14722, "endOffset": 14727, "count": 0}, {"startOffset": 15363, "endOffset": 15367, "count": 1}, {"startOffset": 15390, "endOffset": 15398, "count": 0}, {"startOffset": 15439, "endOffset": 15447, "count": 0}, {"startOffset": 15512, "endOffset": 15520, "count": 0}, {"startOffset": 16037, "endOffset": 16071, "count": 0}, {"startOffset": 16167, "endOffset": 16203, "count": 0}, {"startOffset": 16265, "endOffset": 16828, "count": 0}, {"startOffset": 16875, "endOffset": 17495, "count": 0}, {"startOffset": 17529, "endOffset": 18148, "count": 0}, {"startOffset": 18346, "endOffset": 20572, "count": 0}, {"startOffset": 20600, "endOffset": 21271, "count": 0}, {"startOffset": 21427, "endOffset": 23269, "count": 0}, {"startOffset": 23562, "endOffset": 23699, "count": 0}, {"startOffset": 23779, "endOffset": 23822, "count": 0}, {"startOffset": 23862, "endOffset": 23902, "count": 0}, {"startOffset": 24055, "endOffset": 24098, "count": 0}, {"startOffset": 24559, "endOffset": 24697, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13515, "endOffset": 13618, "count": 3}, {"startOffset": 13554, "endOffset": 13608, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14056, "endOffset": 14100, "count": 2}, {"startOffset": 14095, "endOffset": 14099, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14287, "endOffset": 14312, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14454, "endOffset": 14499, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14558, "endOffset": 14713, "count": 2}, {"startOffset": 14604, "endOffset": 14640, "count": 1}, {"startOffset": 14641, "endOffset": 14677, "count": 1}, {"startOffset": 14678, "endOffset": 14713, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14778, "endOffset": 14997, "count": 1}, {"startOffset": 14840, "endOffset": 14847, "count": 0}, {"startOffset": 14895, "endOffset": 14902, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15047, "endOffset": 15304, "count": 1}, {"startOffset": 15125, "endOffset": 15131, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15562, "endOffset": 15583, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18201, "endOffset": 18283, "count": 2}, {"startOffset": 18247, "endOffset": 18283, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18638, "endOffset": 18675, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18784, "endOffset": 18821, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18963, "endOffset": 19000, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19230, "endOffset": 20562, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21328, "endOffset": 21381, "count": 1}, {"startOffset": 21370, "endOffset": 21374, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21729, "endOffset": 21787, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21913, "endOffset": 21971, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22161, "endOffset": 22219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22474, "endOffset": 23259, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23338, "endOffset": 23394, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23649, "endOffset": 23677, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23943, "endOffset": 24045, "count": 0}], "isBlockCoverage": false}, {"functionName": "checkSceneCompatibility", "ranges": [{"startOffset": 24946, "endOffset": 38262, "count": 2}, {"startOffset": 25060, "endOffset": 25071, "count": 0}, {"startOffset": 25219, "endOffset": 25372, "count": 0}, {"startOffset": 26034, "endOffset": 26208, "count": 0}, {"startOffset": 29536, "endOffset": 29540, "count": 0}, {"startOffset": 29601, "endOffset": 30175, "count": 0}, {"startOffset": 30219, "endOffset": 30227, "count": 0}, {"startOffset": 30291, "endOffset": 30854, "count": 0}, {"startOffset": 34426, "endOffset": 34479, "count": 0}, {"startOffset": 34480, "endOffset": 34491, "count": 0}, {"startOffset": 35312, "endOffset": 35993, "count": 1}, {"startOffset": 35487, "endOffset": 35622, "count": 0}, {"startOffset": 35799, "endOffset": 35993, "count": 0}, {"startOffset": 36106, "endOffset": 36312, "count": 1}, {"startOffset": 36383, "endOffset": 36580, "count": 1}, {"startOffset": 36649, "endOffset": 36847, "count": 0}, {"startOffset": 36917, "endOffset": 37089, "count": 0}, {"startOffset": 37162, "endOffset": 37297, "count": 1}, {"startOffset": 38101, "endOffset": 38258, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 25711, "endOffset": 25814, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 26235, "endOffset": 29461, "count": 3}, {"startOffset": 26319, "endOffset": 27096, "count": 0}, {"startOffset": 27148, "endOffset": 27172, "count": 2}, {"startOffset": 27173, "endOffset": 27198, "count": 2}, {"startOffset": 27200, "endOffset": 28410, "count": 2}, {"startOffset": 27364, "endOffset": 28396, "count": 1}, {"startOffset": 28462, "endOffset": 28494, "count": 1}, {"startOffset": 28496, "endOffset": 29449, "count": 1}], "isBlockCoverage": true}, {"functionName": "checkFeature", "ranges": [{"startOffset": 30882, "endOffset": 31567, "count": 10}, {"startOffset": 30995, "endOffset": 31559, "count": 2}, {"startOffset": 31023, "endOffset": 31481, "count": 1}, {"startOffset": 31338, "endOffset": 31386, "count": 0}, {"startOffset": 31481, "endOffset": 31549, "count": 1}], "isBlockCoverage": true}, {"functionName": "checkPartialFeature", "ranges": [{"startOffset": 31603, "endOffset": 32850, "count": 2}, {"startOffset": 31758, "endOffset": 32226, "count": 0}, {"startOffset": 32264, "endOffset": 32764, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35025, "endOffset": 35062, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 35113, "endOffset": 35152, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36058, "endOffset": 36103, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36335, "endOffset": 36380, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36603, "endOffset": 36646, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 36870, "endOffset": 36914, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37112, "endOffset": 37159, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 37682, "endOffset": 37719, "count": 3}], "isBlockCoverage": true}, {"functionName": "getEnvironmentConstraints", "ranges": [{"startOffset": 38398, "endOffset": 41418, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 41530, "endOffset": 41567, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "1424", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/services/blueprint/blueprint-validator.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33308, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33308, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 346, "endOffset": 9722, "count": 7}], "isBlockCoverage": true}, {"functionName": "BlueprintValidatorService", "ranges": [{"startOffset": 362, "endOffset": 419, "count": 7}], "isBlockCoverage": true}, {"functionName": "validateBlueprint", "ranges": [{"startOffset": 536, "endOffset": 2676, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateBlueprintData", "ranges": [{"startOffset": 2800, "endOffset": 5478, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateBlueprintInstance", "ranges": [{"startOffset": 5649, "endOffset": 9720, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9836, "endOffset": 9877, "count": 7}], "isBlockCoverage": true}]}]}