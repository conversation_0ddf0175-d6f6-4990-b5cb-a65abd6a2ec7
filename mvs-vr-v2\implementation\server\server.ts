/**
 * Advanced MVS-VR Server
 *
 * This module provides the main entry point for the MVS-VR server with
 * advanced features including real-time WebSocket, security, monitoring,
 * machine learning, service mesh, and dashboard capabilities.
 */

import dotenv from 'dotenv';
import express from 'express';
import session from 'express-session';
import { ApiGateway } from './api/gateway';
import { Logger, LogLevel } from './services/integration/logger';

// Advanced Features
import CSRFProtection from './middleware/security/csrf-protection.js';
import SecurityHeaders from './middleware/security/security-headers.js';
import { ServiceMesh } from './services/mesh/service-mesh.js';
import { DashboardManager } from './services/dashboard/dashboard-framework.js';
import WebSocketManager from './services/realtime/websocket-manager.js';
import { PerformanceMonitor } from './services/monitoring/performance-monitor.js';

// Load environment variables
dotenv.config();

// Create logger
const logger = new Logger(LogLevel.INFO);

// Get port from environment variables
const port = parseInt(process.env.PORT || '3000', 10);
const wsPort = parseInt(process.env.WS_PORT || '8080', 10);

// Initialize advanced features
const serviceMesh = new ServiceMesh({
  loadBalancingStrategy: 'round-robin',
  healthCheckInterval: 30000,
});

const dashboardManager = new DashboardManager();
const performanceMonitor = new PerformanceMonitor();
const webSocketManager = new WebSocketManager({ port: wsPort });

// Security middleware
const csrfProtection = new CSRFProtection({
  secure: process.env.NODE_ENV === 'production',
});

const securityHeaders = new SecurityHeaders({
  csp: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'", `ws://localhost:${wsPort}`, `wss://localhost:${wsPort}`],
    },
  },
});

// Create enhanced server
export async function createServer() {
  const app = express();

  // Session middleware (required for CSRF)
  app.use(
    session({
      secret: process.env.SESSION_SECRET || 'mvs-vr-session-secret',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      },
    }),
  );

  // Security middleware
  app.use(securityHeaders.middleware());
  app.use(csrfProtection.middleware());

  // Performance monitoring
  app.use(performanceMonitor.middleware());

  // CSRF token endpoint
  app.get('/api/csrf-token', csrfProtection.getTokenEndpoint());

  // Dashboard API endpoints
  app.get('/api/dashboards', (req, res) => {
    res.json(dashboardManager.getAllDashboards());
  });

  app.post('/api/dashboards', express.json(), (req, res) => {
    try {
      const dashboard = dashboardManager.createDashboard(req.body);
      res.status(201).json(dashboard.serialize());
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  });

  // Service mesh status endpoint
  app.get('/api/service-mesh/status', (req, res) => {
    res.json(serviceMesh.getServiceStats());
  });

  // Performance metrics endpoint
  app.get('/api/metrics', (req, res) => {
    res.json(performanceMonitor.getMetrics());
  });

  // Create API Gateway with advanced features
  const apiGateway = new ApiGateway(port, {
    serviceMesh,
    dashboardManager,
    performanceMonitor,
  });

  // Integrate with existing API Gateway
  app.use('/api', apiGateway.getRouter());

  return app;
}

// Start the enhanced server
async function startServer() {
  try {
    // Initialize WebSocket server
    await webSocketManager.start();
    logger.info(`WebSocket server started on port ${wsPort}`);

    // Register core services with service mesh
    serviceMesh.registerService('websocket', {
      host: 'localhost',
      port: wsPort,
      type: 'websocket',
    });

    serviceMesh.registerService('api-gateway', {
      host: 'localhost',
      port: port,
      type: 'http',
    });

    // Create and start HTTP server
    const app = await createServer();
    const server = app.listen(port, () => {
      logger.info(`Advanced MVS-VR server started on port ${port}`);
      logger.info('Advanced features enabled:');
      logger.info('- Real-time WebSocket communication');
      logger.info('- Enhanced security (CSRF, Security Headers)');
      logger.info('- Service mesh with load balancing');
      logger.info('- Performance monitoring');
      logger.info('- Interactive dashboards');
    });

    return server;
  } catch (error) {
    logger.error('Failed to start advanced server', { error });
    throw error;
  }
}

// Start the server if this file is run directly
if (require.main === module) {
  startServer().catch(error => {
    logger.error('Server startup failed', { error });
    process.exit(1);
  });
}

// Handle process termination
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  logger.error('Uncaught exception', { error });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled promise rejection', { reason, promise });
  process.exit(1);
});
