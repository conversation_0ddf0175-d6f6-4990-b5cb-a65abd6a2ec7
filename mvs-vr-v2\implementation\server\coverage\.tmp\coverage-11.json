{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2061", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/visual-editors-api.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41855, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41855, "count": 1}], "isBlockCoverage": true}, {"functionName": "VisualEditorsApiService", "ranges": [{"startOffset": 628, "endOffset": 688, "count": 13}], "isBlockCoverage": true}, {"functionName": "getVendorData", "ranges": [{"startOffset": 714, "endOffset": 852, "count": 1}], "isBlockCoverage": true}, {"functionName": "getProducts", "ranges": [{"startOffset": 879, "endOffset": 1039, "count": 2}, {"startOffset": 1009, "endOffset": 1038, "count": 1}], "isBlockCoverage": true}, {"functionName": "saveProduct", "ranges": [{"startOffset": 1042, "endOffset": 1342, "count": 2}, {"startOffset": 1091, "endOffset": 1338, "count": 1}], "isBlockCoverage": true}, {"functionName": "getMaterials", "ranges": [{"startOffset": 1370, "endOffset": 1532, "count": 1}], "isBlockCoverage": true}, {"functionName": "saveMaterial", "ranges": [{"startOffset": 1535, "endOffset": 1843, "count": 1}, {"startOffset": 1587, "endOffset": 1718, "count": 0}], "isBlockCoverage": true}, {"functionName": "getAnimations", "ranges": [{"startOffset": 1872, "endOffset": 2036, "count": 1}], "isBlockCoverage": true}, {"functionName": "saveAnimation", "ranges": [{"startOffset": 2039, "endOffset": 2355, "count": 0}], "isBlockCoverage": false}, {"functionName": "getShowrooms", "ranges": [{"startOffset": 2383, "endOffset": 2552, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveShowroom", "ranges": [{"startOffset": 2555, "endOffset": 2877, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLightingSetup", "ranges": [{"startOffset": 2905, "endOffset": 3085, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLightingSetup", "ranges": [{"startOffset": 3088, "endOffset": 3417, "count": 0}], "isBlockCoverage": false}, {"functionName": "uploadFile", "ranges": [{"startOffset": 3448, "endOffset": 3642, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateProduct", "ranges": [{"startOffset": 3672, "endOffset": 4050, "count": 3}, {"startOffset": 3744, "endOffset": 3773, "count": 1}, {"startOffset": 3775, "endOffset": 3829, "count": 2}, {"startOffset": 3858, "endOffset": 3909, "count": 2}, {"startOffset": 3937, "endOffset": 3995, "count": 2}], "isBlockCoverage": true}, {"functionName": "validateMaterial", "ranges": [{"startOffset": 4053, "endOffset": 4888, "count": 2}, {"startOffset": 4128, "endOffset": 4158, "count": 1}, {"startOffset": 4160, "endOffset": 4215, "count": 1}, {"startOffset": 4245, "endOffset": 4296, "count": 1}, {"startOffset": 4321, "endOffset": 4376, "count": 1}, {"startOffset": 4543, "endOffset": 4619, "count": 1}, {"startOffset": 4715, "endOffset": 4750, "count": 1}, {"startOffset": 4752, "endOffset": 4827, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateAnimation", "ranges": [{"startOffset": 4891, "endOffset": 5570, "count": 2}, {"startOffset": 4969, "endOffset": 5000, "count": 1}, {"startOffset": 5002, "endOffset": 5058, "count": 1}, {"startOffset": 5089, "endOffset": 5140, "count": 1}, {"startOffset": 5175, "endOffset": 5230, "count": 1}, {"startOffset": 5306, "endOffset": 5370, "count": 1}, {"startOffset": 5452, "endOffset": 5515, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5634, "endOffset": 14057, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5716, "endOffset": 5899, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5956, "endOffset": 6412, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6021, "endOffset": 6406, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6470, "endOffset": 8922, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6545, "endOffset": 7025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7084, "endOffset": 7562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7628, "endOffset": 8055, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8119, "endOffset": 8916, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8981, "endOffset": 11097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9057, "endOffset": 9545, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9605, "endOffset": 10093, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10164, "endOffset": 11091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11157, "endOffset": 12738, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11234, "endOffset": 11730, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11796, "endOffset": 12732, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12800, "endOffset": 13372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12859, "endOffset": 13366, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13426, "endOffset": 14053, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13502, "endOffset": 13703, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13770, "endOffset": 14047, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "2062", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/test-utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 25406, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 25406, "count": 1}], "isBlockCoverage": true}, {"functionName": "createMockVueComponent", "ranges": [{"startOffset": 352, "endOffset": 666, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 778, "endOffset": 816, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockApiClient", "ranges": [{"startOffset": 848, "endOffset": 1054, "count": 13}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1163, "endOffset": 1198, "count": 13}], "isBlockCoverage": true}, {"functionName": "createMockDirectusClient", "ranges": [{"startOffset": 1235, "endOffset": 1838, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1952, "endOffset": 1992, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockSupabaseClient", "ranges": [{"startOffset": 2029, "endOffset": 3013, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3127, "endOffset": 3167, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateMockVendor", "ranges": [{"startOffset": 3198, "endOffset": 3446, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3554, "endOffset": 3588, "count": 1}], "isBlockCoverage": true}, {"functionName": "generateMockProduct", "ranges": [{"startOffset": 3620, "endOffset": 3958, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4067, "endOffset": 4102, "count": 6}], "isBlockCoverage": true}, {"functionName": "generateMockMaterial", "ranges": [{"startOffset": 4135, "endOffset": 4596, "count": 5}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4706, "endOffset": 4742, "count": 5}], "isBlockCoverage": true}, {"functionName": "generateMockAnimation", "ranges": [{"startOffset": 4776, "endOffset": 5338, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5449, "endOffset": 5486, "count": 3}], "isBlockCoverage": true}, {"functionName": "generateMockShowroom", "ranges": [{"startOffset": 5519, "endOffset": 5802, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5912, "endOffset": 5948, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateMockLightingSetup", "ranges": [{"startOffset": 5986, "endOffset": 6650, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6765, "endOffset": 6806, "count": 0}], "isBlockCoverage": false}, {"functionName": "waitForNextTick", "ranges": [{"startOffset": 6834, "endOffset": 6888, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6993, "endOffset": 7024, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockEvent", "ranges": [{"startOffset": 7052, "endOffset": 7167, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7272, "endOffset": 7303, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockFile", "ranges": [{"startOffset": 7330, "endOffset": 7492, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7596, "endOffset": 7626, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockLocalStorage", "ranges": [{"startOffset": 7661, "endOffset": 8141, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8253, "endOffset": 8291, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockURL", "ranges": [{"startOffset": 8317, "endOffset": 8448, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8551, "endOffset": 8580, "count": 0}], "isBlockCoverage": false}, {"functionName": "simulateNetworkError", "ranges": [{"startOffset": 8613, "endOffset": 8658, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8768, "endOffset": 8804, "count": 0}], "isBlockCoverage": false}, {"functionName": "simulateApiError", "ranges": [{"startOffset": 8833, "endOffset": 8965, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9071, "endOffset": 9103, "count": 0}], "isBlockCoverage": false}, {"functionName": "flushPromises", "ranges": [{"startOffset": 9129, "endOffset": 9183, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9286, "endOffset": 9315, "count": 0}], "isBlockCoverage": false}, {"functionName": "waitFor", "ranges": [{"startOffset": 9335, "endOffset": 9607, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9704, "endOffset": 9727, "count": 0}], "isBlockCoverage": false}]}]}