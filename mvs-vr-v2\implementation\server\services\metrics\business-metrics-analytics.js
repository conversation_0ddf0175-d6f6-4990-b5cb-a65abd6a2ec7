/**
 * Business Metrics Analytics Service
 *
 * This service provides advanced analytics, trend analysis, and predictive insights
 * for business metrics to support data-driven decision making.
 */

import { EventEmitter } from 'events';

// Simple logger for services
const logger = {
  info: msg => console.log(`[INFO] ${msg}`),
  error: msg => console.error(`[ERROR] ${msg}`),
  debug: msg => console.log(`[DEBUG] ${msg}`),
  warn: msg => console.warn(`[WARN] ${msg}`),
};

// Analysis types
const ANALYSIS_TYPES = {
  TREND: 'trend',
  CORRELATION: 'correlation',
  ANOMALY: 'anomaly',
  FORECAST: 'forecast',
  COMPARISON: 'comparison',
};

// Trend directions
const TREND_DIRECTIONS = {
  INCREASING: 'increasing',
  DECREASING: 'decreasing',
  STABLE: 'stable',
  VOLATILE: 'volatile',
};

class BusinessMetricsAnalytics extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      analysisInterval: options.analysisInterval || 3600000, // 1 hour
      trendWindowDays: options.trendWindowDays || 7,
      correlationThreshold: options.correlationThreshold || 0.7,
      anomalyThreshold: options.anomalyThreshold || 2.0,
      ...options,
    };

    // Analytics data
    this.analyses = new Map();
    this.trends = new Map();
    this.correlations = new Map();
    this.forecasts = new Map();

    // External services
    this.metricsCollector = null;

    // Statistics
    this.stats = {
      totalAnalyses: 0,
      trendsIdentified: 0,
      correlationsFound: 0,
      forecastsGenerated: 0,
      lastAnalysis: null,
      startTime: Date.now(),
    };
  }

  /**
   * Set metrics collector reference
   * @param {Object} metricsCollector - Metrics collector instance
   */
  setMetricsCollector(metricsCollector) {
    this.metricsCollector = metricsCollector;
    logger.debug('Metrics collector reference set');
  }

  /**
   * Start analytics processing
   */
  startAnalytics() {
    // Initial analysis
    this.runAnalytics();

    // Schedule regular analysis
    this.analysisInterval = setInterval(() => {
      this.runAnalytics();
    }, this.options.analysisInterval);

    logger.info('Started business metrics analytics');
  }

  /**
   * Stop analytics processing
   */
  stopAnalytics() {
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }

    logger.info('Stopped business metrics analytics');
  }

  /**
   * Run comprehensive analytics
   */
  async runAnalytics() {
    try {
      if (!this.metricsCollector) {
        logger.warn('Metrics collector not available for analytics');
        return;
      }

      const startTime = Date.now();

      // Get current metrics and history
      const currentMetrics = this.metricsCollector.getCurrentMetrics();

      // Run different types of analysis
      await this.analyzeTrends(currentMetrics);
      await this.analyzeCorrelations(currentMetrics);
      await this.detectAnomalies(currentMetrics);
      await this.generateForecasts(currentMetrics);

      // Update statistics
      this.stats.totalAnalyses++;
      this.stats.lastAnalysis = new Date().toISOString();

      const analysisTime = Date.now() - startTime;
      logger.debug(`Completed analytics in ${analysisTime}ms`);

      // Emit analytics complete event
      this.emit('analyticsComplete', {
        analysisTime,
        timestamp: this.stats.lastAnalysis,
      });
    } catch (error) {
      logger.error('Error during analytics processing:', error);
    }
  }

  /**
   * Analyze trends in metrics
   * @param {Array<Object>} currentMetrics - Current metrics
   */
  async analyzeTrends(currentMetrics) {
    for (const metric of currentMetrics) {
      try {
        const history = this.metricsCollector.getMetricHistory(metric.id, 100);

        if (history.length < 10) {
          continue; // Need at least 10 data points for trend analysis
        }

        const trend = this.calculateTrend(history);

        this.trends.set(metric.id, {
          metricId: metric.id,
          metricName: metric.name,
          direction: trend.direction,
          slope: trend.slope,
          confidence: trend.confidence,
          dataPoints: history.length,
          timespan: this.calculateTimespan(history),
          timestamp: new Date().toISOString(),
        });

        this.stats.trendsIdentified++;
      } catch (error) {
        logger.error(`Error analyzing trend for metric ${metric.id}:`, error);
      }
    }
  }

  /**
   * Calculate trend for metric history
   * @param {Array<Object>} history - Metric history
   * @returns {Object} Trend analysis
   */
  calculateTrend(history) {
    // Simple linear regression for trend calculation
    const n = history.length;
    const values = history.map(h => h.value);
    const timestamps = history.map(h => new Date(h.timestamp).getTime());

    // Calculate means
    const meanX = timestamps.reduce((sum, t) => sum + t, 0) / n;
    const meanY = values.reduce((sum, v) => sum + v, 0) / n;

    // Calculate slope
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n; i++) {
      numerator += (timestamps[i] - meanX) * (values[i] - meanY);
      denominator += Math.pow(timestamps[i] - meanX, 2);
    }

    const slope = denominator !== 0 ? numerator / denominator : 0;

    // Calculate correlation coefficient (confidence)
    let sumXY = 0;
    let sumX2 = 0;
    let sumY2 = 0;

    for (let i = 0; i < n; i++) {
      sumXY += timestamps[i] * values[i];
      sumX2 += timestamps[i] * timestamps[i];
      sumY2 += values[i] * values[i];
    }

    const correlation =
      (n * sumXY - timestamps.reduce((a, b) => a + b) * values.reduce((a, b) => a + b)) /
      Math.sqrt(
        (n * sumX2 -
          Math.pow(
            timestamps.reduce((a, b) => a + b),
            2,
          )) *
          (n * sumY2 -
            Math.pow(
              values.reduce((a, b) => a + b),
              2,
            )),
      );

    // Determine trend direction
    let direction;
    const absSlope = Math.abs(slope);

    if (absSlope < 0.001) {
      direction = TREND_DIRECTIONS.STABLE;
    } else if (slope > 0) {
      direction = TREND_DIRECTIONS.INCREASING;
    } else {
      direction = TREND_DIRECTIONS.DECREASING;
    }

    // Check for volatility
    const variance = values.reduce((sum, v) => sum + Math.pow(v - meanY, 2), 0) / n;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = stdDev / meanY;

    if (coefficientOfVariation > 0.3) {
      direction = TREND_DIRECTIONS.VOLATILE;
    }

    return {
      direction,
      slope,
      confidence: Math.abs(correlation),
      variance,
      coefficientOfVariation,
    };
  }

  /**
   * Analyze correlations between metrics
   * @param {Array<Object>} currentMetrics - Current metrics
   */
  async analyzeCorrelations(currentMetrics) {
    const metricPairs = [];

    // Generate all metric pairs
    for (let i = 0; i < currentMetrics.length; i++) {
      for (let j = i + 1; j < currentMetrics.length; j++) {
        metricPairs.push([currentMetrics[i], currentMetrics[j]]);
      }
    }

    // Analyze each pair
    for (const [metric1, metric2] of metricPairs) {
      try {
        const correlation = await this.calculateCorrelation(metric1.id, metric2.id);

        if (Math.abs(correlation.coefficient) >= this.options.correlationThreshold) {
          const correlationKey = `${metric1.id}-${metric2.id}`;

          this.correlations.set(correlationKey, {
            metric1: { id: metric1.id, name: metric1.name },
            metric2: { id: metric2.id, name: metric2.name },
            coefficient: correlation.coefficient,
            strength: this.getCorrelationStrength(correlation.coefficient),
            significance: correlation.significance,
            dataPoints: correlation.dataPoints,
            timestamp: new Date().toISOString(),
          });

          this.stats.correlationsFound++;
        }
      } catch (error) {
        logger.error(`Error analyzing correlation between ${metric1.id} and ${metric2.id}:`, error);
      }
    }
  }

  /**
   * Calculate correlation between two metrics
   * @param {string} metric1Id - First metric ID
   * @param {string} metric2Id - Second metric ID
   * @returns {Promise<Object>} Correlation analysis
   */
  async calculateCorrelation(metric1Id, metric2Id) {
    const history1 = this.metricsCollector.getMetricHistory(metric1Id, 50);
    const history2 = this.metricsCollector.getMetricHistory(metric2Id, 50);

    // Align timestamps
    const alignedData = this.alignMetricData(history1, history2);

    if (alignedData.length < 10) {
      throw new Error('Insufficient aligned data for correlation analysis');
    }

    const values1 = alignedData.map(d => d.value1);
    const values2 = alignedData.map(d => d.value2);

    // Calculate Pearson correlation coefficient
    const n = alignedData.length;
    const mean1 = values1.reduce((sum, v) => sum + v, 0) / n;
    const mean2 = values2.reduce((sum, v) => sum + v, 0) / n;

    let numerator = 0;
    let sum1Sq = 0;
    let sum2Sq = 0;

    for (let i = 0; i < n; i++) {
      const diff1 = values1[i] - mean1;
      const diff2 = values2[i] - mean2;

      numerator += diff1 * diff2;
      sum1Sq += diff1 * diff1;
      sum2Sq += diff2 * diff2;
    }

    const denominator = Math.sqrt(sum1Sq * sum2Sq);
    const coefficient = denominator !== 0 ? numerator / denominator : 0;

    // Calculate significance (simplified)
    const tStat = coefficient * Math.sqrt((n - 2) / (1 - coefficient * coefficient));
    const significance = Math.abs(tStat) > 2.0 ? 'significant' : 'not_significant';

    return {
      coefficient,
      significance,
      dataPoints: n,
    };
  }

  /**
   * Align metric data by timestamp
   * @param {Array<Object>} history1 - First metric history
   * @param {Array<Object>} history2 - Second metric history
   * @returns {Array<Object>} Aligned data
   */
  alignMetricData(history1, history2) {
    const aligned = [];
    const tolerance = 300000; // 5 minutes tolerance

    for (const point1 of history1) {
      const timestamp1 = new Date(point1.timestamp).getTime();

      for (const point2 of history2) {
        const timestamp2 = new Date(point2.timestamp).getTime();

        if (Math.abs(timestamp1 - timestamp2) <= tolerance) {
          aligned.push({
            timestamp: point1.timestamp,
            value1: point1.value,
            value2: point2.value,
          });
          break;
        }
      }
    }

    return aligned;
  }

  /**
   * Get correlation strength description
   * @param {number} coefficient - Correlation coefficient
   * @returns {string} Strength description
   */
  getCorrelationStrength(coefficient) {
    const abs = Math.abs(coefficient);

    if (abs >= 0.9) return 'very_strong';
    if (abs >= 0.7) return 'strong';
    if (abs >= 0.5) return 'moderate';
    if (abs >= 0.3) return 'weak';
    return 'very_weak';
  }

  /**
   * Detect anomalies in current metrics
   * @param {Array<Object>} currentMetrics - Current metrics
   */
  async detectAnomalies(currentMetrics) {
    for (const metric of currentMetrics) {
      try {
        const history = this.metricsCollector.getMetricHistory(metric.id, 50);

        if (history.length < 20) {
          continue; // Need sufficient history for anomaly detection
        }

        const anomaly = this.detectMetricAnomaly(metric, history);

        if (anomaly.isAnomaly) {
          this.emit('anomalyDetected', {
            metricId: metric.id,
            metricName: metric.name,
            currentValue: metric.value,
            expectedRange: anomaly.expectedRange,
            severity: anomaly.severity,
            timestamp: metric.timestamp,
          });
        }
      } catch (error) {
        logger.error(`Error detecting anomaly for metric ${metric.id}:`, error);
      }
    }
  }

  /**
   * Detect anomaly in a specific metric
   * @param {Object} currentMetric - Current metric
   * @param {Array<Object>} history - Metric history
   * @returns {Object} Anomaly detection result
   */
  detectMetricAnomaly(currentMetric, history) {
    const values = history.map(h => h.value);
    const mean = values.reduce((sum, v) => sum + v, 0) / values.length;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    const zScore = stdDev > 0 ? Math.abs(currentMetric.value - mean) / stdDev : 0;
    const isAnomaly = zScore > this.options.anomalyThreshold;

    let severity = 'normal';
    if (zScore > 3.0) {
      severity = 'critical';
    } else if (zScore > 2.5) {
      severity = 'high';
    } else if (zScore > 2.0) {
      severity = 'medium';
    }

    return {
      isAnomaly,
      zScore,
      severity,
      expectedRange: {
        min: mean - 2 * stdDev,
        max: mean + 2 * stdDev,
      },
    };
  }

  /**
   * Generate forecasts for metrics
   * @param {Array<Object>} currentMetrics - Current metrics
   */
  async generateForecasts(currentMetrics) {
    for (const metric of currentMetrics) {
      try {
        const history = this.metricsCollector.getMetricHistory(metric.id, 30);

        if (history.length < 15) {
          continue; // Need sufficient history for forecasting
        }

        const forecast = this.generateMetricForecast(metric, history);

        this.forecasts.set(metric.id, {
          metricId: metric.id,
          metricName: metric.name,
          currentValue: metric.value,
          forecast: forecast.value,
          confidence: forecast.confidence,
          horizon: '1h', // 1 hour forecast
          method: forecast.method,
          timestamp: new Date().toISOString(),
        });

        this.stats.forecastsGenerated++;
      } catch (error) {
        logger.error(`Error generating forecast for metric ${metric.id}:`, error);
      }
    }
  }

  /**
   * Generate forecast for a specific metric
   * @param {Object} currentMetric - Current metric
   * @param {Array<Object>} history - Metric history
   * @returns {Object} Forecast result
   */
  generateMetricForecast(currentMetric, history) {
    // Simple exponential smoothing
    const alpha = 0.3; // Smoothing parameter
    let smoothedValue = history[0].value;

    for (let i = 1; i < history.length; i++) {
      smoothedValue = alpha * history[i].value + (1 - alpha) * smoothedValue;
    }

    // Calculate trend
    const recentValues = history.slice(-5).map(h => h.value);
    const trend =
      recentValues.length > 1
        ? (recentValues[recentValues.length - 1] - recentValues[0]) / (recentValues.length - 1)
        : 0;

    // Forecast next value
    const forecastValue = smoothedValue + trend;

    // Calculate confidence based on recent variance
    const variance =
      recentValues.reduce((sum, v) => sum + Math.pow(v - smoothedValue, 2), 0) /
      recentValues.length;
    const confidence = Math.max(0.1, Math.min(0.9, 1 - Math.sqrt(variance) / smoothedValue));

    return {
      value: Math.max(0, forecastValue),
      confidence,
      method: 'exponential_smoothing',
    };
  }

  /**
   * Calculate timespan of history
   * @param {Array<Object>} history - Metric history
   * @returns {number} Timespan in milliseconds
   */
  calculateTimespan(history) {
    if (history.length < 2) return 0;

    const oldest = new Date(history[0].timestamp).getTime();
    const newest = new Date(history[history.length - 1].timestamp).getTime();

    return newest - oldest;
  }

  /**
   * Get analytics results
   * @returns {Object} Analytics results
   */
  getAnalyticsResults() {
    return {
      trends: Object.fromEntries(this.trends),
      correlations: Object.fromEntries(this.correlations),
      forecasts: Object.fromEntries(this.forecasts),
      summary: {
        trendsCount: this.trends.size,
        correlationsCount: this.correlations.size,
        forecastsCount: this.forecasts.size,
      },
    };
  }

  /**
   * Get statistics
   * @returns {Object} Statistics
   */
  getStatistics() {
    return {
      ...this.stats,
      uptime: Date.now() - this.stats.startTime,
    };
  }
}

export { BusinessMetricsAnalytics, ANALYSIS_TYPES, TREND_DIRECTIONS };
