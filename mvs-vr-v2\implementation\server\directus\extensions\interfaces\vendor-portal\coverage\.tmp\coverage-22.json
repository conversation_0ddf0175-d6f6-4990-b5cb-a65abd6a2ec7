{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 329, "endOffset": 450, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/tests/GuidedSetupWizard.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29134, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29134, "count": 1}, {"startOffset": 1668, "endOffset": 29133, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 351, "endOffset": 629, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 397, "endOffset": 410, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 675, "endOffset": 763, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 724, "endOffset": 737, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 806, "endOffset": 891, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 852, "endOffset": 865, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 936, "endOffset": 1023, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 984, "endOffset": 997, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1068, "endOffset": 1155, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1116, "endOffset": 1129, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1201, "endOffset": 1289, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1250, "endOffset": 1263, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1331, "endOffset": 1431, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1376, "endOffset": 1389, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2624, "endOffset": 7248, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/GuidedSetupWizard.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28909, "count": 1}, {"startOffset": 11704, "endOffset": 11748, "count": 0}, {"startOffset": 11749, "endOffset": 28908, "count": 0}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 3705, "endOffset": 7390, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 7397, "endOffset": 7534, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWizardComplete", "ranges": [{"startOffset": 7556, "endOffset": 8297, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleSaveProgress", "ranges": [{"startOffset": 8308, "endOffset": 8948, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLoadProgress", "ranges": [{"startOffset": 8959, "endOffset": 9183, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleAnalyticsEvent", "ranges": [{"startOffset": 9194, "endOffset": 9328, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleExit", "ranges": [{"startOffset": 9339, "endOffset": 9410, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadOnboardingStatus", "ranges": [{"startOffset": 9421, "endOffset": 10292, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackAnalyticsEvent", "ranges": [{"startOffset": 10303, "endOffset": 10661, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackWizardCompletion", "ranges": [{"startOffset": 10672, "endOffset": 11099, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 11381, "endOffset": 11487, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11941, "endOffset": 12031, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12157, "endOffset": 12241, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12290, "endOffset": 12334, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/GuidedSetupService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 42253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 42253, "count": 1}], "isBlockCoverage": true}, {"functionName": "GuidedSetupService", "ranges": [{"startOffset": 580, "endOffset": 927, "count": 0}], "isBlockCoverage": false}, {"functionName": "getOnboardingStatus", "ranges": [{"startOffset": 1089, "endOffset": 1451, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveOnboardingStatus", "ranges": [{"startOffset": 1746, "endOffset": 2750, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveCompanyProfile", "ranges": [{"startOffset": 2963, "endOffset": 3750, "count": 0}], "isBlockCoverage": false}, {"functionName": "uploadCompanyLogo", "ranges": [{"startOffset": 3938, "endOffset": 4597, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveUserAccounts", "ranges": [{"startOffset": 4806, "endOffset": 6275, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveBranding", "ranges": [{"startOffset": 6473, "endOffset": 7179, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackWizardAnalytics", "ranges": [{"startOffset": 7384, "endOffset": 7935, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/directus.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28293, "count": 1}], "isBlockCoverage": true}, {"functionName": "getDirectusUrl", "ranges": [{"startOffset": 322, "endOffset": 735, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 838, "endOffset": 868, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDirectusToken", "ranges": [{"startOffset": 1010, "endOffset": 1736, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1841, "endOffset": 1873, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentVendorId", "ranges": [{"startOffset": 1987, "endOffset": 2746, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2853, "endOffset": 2887, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentUserRole", "ranges": [{"startOffset": 2991, "endOffset": 3730, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3837, "endOffset": 3871, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasPermission", "ranges": [{"startOffset": 4132, "endOffset": 4665, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4767, "endOffset": 4796, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDate", "ranges": [{"startOffset": 4989, "endOffset": 5397, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5496, "endOffset": 5522, "count": 0}], "isBlockCoverage": false}, {"functionName": "getFileUrl", "ranges": [{"startOffset": 5642, "endOffset": 5778, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5877, "endOffset": 5903, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/GuidedSetupWizard.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7472, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7472, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 1322, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1473, "endOffset": 1495, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1602, "endOffset": 1633, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/GuidedSetupWizard.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1167", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1168", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 0}], "isBlockCoverage": false}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 0}], "isBlockCoverage": false}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}