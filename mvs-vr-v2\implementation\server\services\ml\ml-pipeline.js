/**
 * ML Pipeline Service
 * Automated machine learning pipeline for model training, validation, and deployment
 */

import * as tf from '@tensorflow/tfjs-node';
import { EventEmitter } from 'events';
import fs from 'fs/promises';
import path from 'path';

export class MLPipeline extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      enableAutoML: options.enableAutoML || false,
      enableModelVersioning: options.enableModelVersioning !== false,
      enableABTesting: options.enableABTesting !== false,
      enableContinuousTraining: options.enableContinuousTraining !== false,
      enableModelMonitoring: options.enableModelMonitoring !== false,
      trainingSchedule: options.trainingSchedule || '0 2 * * *', // Daily at 2 AM
      validationSplit: options.validationSplit || 0.2,
      testSplit: options.testSplit || 0.1,
      maxModelVersions: options.maxModelVersions || 10,
      modelPath: options.modelPath || './models',
      dataPath: options.dataPath || './data',
      experimentPath: options.experimentPath || './experiments',
      ...options
    };

    // Pipeline state
    this.pipelineState = {
      isTraining: false,
      currentExperiment: null,
      trainingProgress: 0,
      lastTrainingTime: null,
      activeModels: new Map(),
      experiments: new Map()
    };

    // Model registry
    this.modelRegistry = new Map();
    this.modelVersions = new Map();
    this.modelMetrics = new Map();
    
    // A/B Testing
    this.abTests = new Map();
    this.trafficSplits = new Map();
    
    // Data management
    this.datasets = new Map();
    this.dataValidators = new Map();
    
    // Training jobs
    this.trainingJobs = new Map();
    this.trainingQueue = [];
    
    // Monitoring
    this.performanceMetrics = new Map();
    this.driftDetectors = new Map();

    this.initialize();
  }

  async initialize() {
    try {
      console.log('🔧 Initializing ML Pipeline...');
      
      // Create directories
      await this.createDirectories();
      
      // Initialize model registry
      await this.loadModelRegistry();
      
      // Setup data validators
      this.setupDataValidators();
      
      // Setup monitoring
      if (this.options.enableModelMonitoring) {
        this.setupModelMonitoring();
      }
      
      // Setup continuous training
      if (this.options.enableContinuousTraining) {
        this.setupContinuousTraining();
      }
      
      // Setup A/B testing
      if (this.options.enableABTesting) {
        this.setupABTesting();
      }
      
      console.log('✅ ML Pipeline initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize ML Pipeline:', error);
      this.emit('error', error);
    }
  }

  async createDirectories() {
    const dirs = [
      this.options.modelPath,
      this.options.dataPath,
      this.options.experimentPath,
      path.join(this.options.modelPath, 'versions'),
      path.join(this.options.experimentPath, 'logs'),
      path.join(this.options.experimentPath, 'artifacts')
    ];

    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        if (error.code !== 'EEXIST') {
          throw error;
        }
      }
    }
  }

  /**
   * Create and run ML experiment
   * @param {Object} experimentConfig - Experiment configuration
   */
  async createExperiment(experimentConfig) {
    const experimentId = `exp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const experiment = {
      id: experimentId,
      name: experimentConfig.name,
      description: experimentConfig.description,
      modelType: experimentConfig.modelType,
      hyperparameters: experimentConfig.hyperparameters,
      dataset: experimentConfig.dataset,
      metrics: {},
      status: 'created',
      createdAt: Date.now(),
      startedAt: null,
      completedAt: null,
      artifacts: []
    };

    this.pipelineState.experiments.set(experimentId, experiment);
    
    this.emit('experimentCreated', { experimentId, experiment });
    
    return experimentId;
  }

  /**
   * Run ML experiment
   * @param {string} experimentId - Experiment identifier
   */
  async runExperiment(experimentId) {
    const experiment = this.pipelineState.experiments.get(experimentId);
    if (!experiment) {
      throw new Error(`Experiment ${experimentId} not found`);
    }

    try {
      experiment.status = 'running';
      experiment.startedAt = Date.now();
      this.pipelineState.currentExperiment = experimentId;
      
      this.emit('experimentStarted', { experimentId, experiment });
      
      // Load and validate data
      const dataset = await this.loadDataset(experiment.dataset);
      const validatedData = await this.validateData(dataset);
      
      // Split data
      const { trainData, validationData, testData } = this.splitData(validatedData);
      
      // Create model
      const model = await this.createModel(experiment.modelType, experiment.hyperparameters);
      
      // Train model
      const trainingResults = await this.trainModel(model, trainData, validationData, experimentId);
      
      // Evaluate model
      const evaluationResults = await this.evaluateModel(model, testData);
      
      // Save model and artifacts
      const modelVersion = await this.saveModelVersion(model, experimentId, {
        training: trainingResults,
        evaluation: evaluationResults,
        hyperparameters: experiment.hyperparameters
      });
      
      // Update experiment
      experiment.status = 'completed';
      experiment.completedAt = Date.now();
      experiment.metrics = {
        ...trainingResults.metrics,
        ...evaluationResults
      };
      experiment.modelVersion = modelVersion;
      
      this.emit('experimentCompleted', { experimentId, experiment, modelVersion });
      
      return {
        experimentId,
        modelVersion,
        metrics: experiment.metrics
      };
    } catch (error) {
      experiment.status = 'failed';
      experiment.error = error.message;
      experiment.completedAt = Date.now();
      
      this.emit('experimentFailed', { experimentId, error });
      throw error;
    } finally {
      this.pipelineState.currentExperiment = null;
      this.pipelineState.trainingProgress = 0;
    }
  }

  /**
   * Create model based on type and hyperparameters
   * @param {string} modelType - Type of model to create
   * @param {Object} hyperparameters - Model hyperparameters
   */
  async createModel(modelType, hyperparameters) {
    switch (modelType) {
      case 'neural_network':
        return this.createNeuralNetwork(hyperparameters);
      case 'lstm':
        return this.createLSTMModel(hyperparameters);
      case 'cnn':
        return this.createCNNModel(hyperparameters);
      case 'autoencoder':
        return this.createAutoencoderModel(hyperparameters);
      case 'transformer':
        return this.createTransformerModel(hyperparameters);
      default:
        throw new Error(`Unknown model type: ${modelType}`);
    }
  }

  createNeuralNetwork(hyperparameters) {
    const {
      inputShape = [10],
      hiddenLayers = [64, 32],
      outputUnits = 1,
      activation = 'relu',
      outputActivation = 'sigmoid',
      dropout = 0.2,
      regularization = 0.001
    } = hyperparameters;

    const layers = [];
    
    // Input layer
    layers.push(tf.layers.dense({
      inputShape,
      units: hiddenLayers[0],
      activation,
      kernelRegularizer: tf.regularizers.l2({ l2: regularization })
    }));
    
    // Hidden layers
    for (let i = 1; i < hiddenLayers.length; i++) {
      layers.push(tf.layers.dropout({ rate: dropout }));
      layers.push(tf.layers.dense({
        units: hiddenLayers[i],
        activation,
        kernelRegularizer: tf.regularizers.l2({ l2: regularization })
      }));
    }
    
    // Output layer
    layers.push(tf.layers.dropout({ rate: dropout }));
    layers.push(tf.layers.dense({
      units: outputUnits,
      activation: outputActivation
    }));

    const model = tf.sequential({ layers });
    
    model.compile({
      optimizer: tf.train.adam(hyperparameters.learningRate || 0.001),
      loss: hyperparameters.loss || 'binaryCrossentropy',
      metrics: hyperparameters.metrics || ['accuracy']
    });

    return model;
  }

  createLSTMModel(hyperparameters) {
    const {
      inputShape = [10, 1],
      lstmUnits = [50, 50],
      denseUnits = [25],
      outputUnits = 1,
      dropout = 0.2,
      recurrentDropout = 0.2,
      outputActivation = 'linear'
    } = hyperparameters;

    const layers = [];
    
    // LSTM layers
    for (let i = 0; i < lstmUnits.length; i++) {
      layers.push(tf.layers.lstm({
        inputShape: i === 0 ? inputShape : undefined,
        units: lstmUnits[i],
        returnSequences: i < lstmUnits.length - 1,
        dropout,
        recurrentDropout
      }));
    }
    
    // Dense layers
    for (const units of denseUnits) {
      layers.push(tf.layers.dense({ units, activation: 'relu' }));
      layers.push(tf.layers.dropout({ rate: dropout }));
    }
    
    // Output layer
    layers.push(tf.layers.dense({
      units: outputUnits,
      activation: outputActivation
    }));

    const model = tf.sequential({ layers });
    
    model.compile({
      optimizer: tf.train.adam(hyperparameters.learningRate || 0.001),
      loss: hyperparameters.loss || 'meanSquaredError',
      metrics: hyperparameters.metrics || ['meanAbsoluteError']
    });

    return model;
  }

  /**
   * Train model with data
   * @param {tf.LayersModel} model - Model to train
   * @param {Object} trainData - Training data
   * @param {Object} validationData - Validation data
   * @param {string} experimentId - Experiment identifier
   */
  async trainModel(model, trainData, validationData, experimentId) {
    const trainingConfig = {
      epochs: 100,
      batchSize: 32,
      validationSplit: 0.2,
      callbacks: [
        // Early stopping
        tf.callbacks.earlyStopping({
          monitor: 'val_loss',
          patience: 10,
          restoreBestWeights: true
        }),
        
        // Learning rate reduction
        tf.callbacks.reduceLROnPlateau({
          monitor: 'val_loss',
          factor: 0.5,
          patience: 5,
          minLr: 0.0001
        }),
        
        // Custom callback for progress tracking
        {
          onEpochEnd: (epoch, logs) => {
            this.pipelineState.trainingProgress = ((epoch + 1) / trainingConfig.epochs) * 100;
            this.emit('trainingProgress', {
              experimentId,
              epoch: epoch + 1,
              totalEpochs: trainingConfig.epochs,
              progress: this.pipelineState.trainingProgress,
              logs
            });
          }
        }
      ]
    };

    const trainTensor = tf.tensor(trainData.features);
    const trainLabelsTensor = tf.tensor(trainData.labels);
    const validationTensor = tf.tensor(validationData.features);
    const validationLabelsTensor = tf.tensor(validationData.labels);

    try {
      const history = await model.fit(trainTensor, trainLabelsTensor, {
        ...trainingConfig,
        validationData: [validationTensor, validationLabelsTensor]
      });

      return {
        history: history.history,
        metrics: {
          finalLoss: history.history.loss[history.history.loss.length - 1],
          finalValLoss: history.history.val_loss[history.history.val_loss.length - 1],
          bestEpoch: this.findBestEpoch(history.history),
          totalEpochs: history.history.loss.length
        }
      };
    } finally {
      // Cleanup tensors
      trainTensor.dispose();
      trainLabelsTensor.dispose();
      validationTensor.dispose();
      validationLabelsTensor.dispose();
    }
  }

  /**
   * Evaluate model performance
   * @param {tf.LayersModel} model - Model to evaluate
   * @param {Object} testData - Test data
   */
  async evaluateModel(model, testData) {
    const testTensor = tf.tensor(testData.features);
    const testLabelsTensor = tf.tensor(testData.labels);

    try {
      const evaluation = await model.evaluate(testTensor, testLabelsTensor);
      const predictions = await model.predict(testTensor);
      
      const evaluationData = await evaluation.data();
      const predictionsData = await predictions.data();
      const labelsData = await testLabelsTensor.data();

      // Calculate additional metrics
      const additionalMetrics = this.calculateAdditionalMetrics(predictionsData, labelsData);

      predictions.dispose();

      return {
        loss: evaluationData[0],
        accuracy: evaluationData[1],
        ...additionalMetrics
      };
    } finally {
      testTensor.dispose();
      testLabelsTensor.dispose();
    }
  }

  /**
   * Save model version with metadata
   * @param {tf.LayersModel} model - Model to save
   * @param {string} experimentId - Experiment identifier
   * @param {Object} metadata - Model metadata
   */
  async saveModelVersion(model, experimentId, metadata) {
    const versionId = `v${Date.now()}`;
    const modelPath = path.join(this.options.modelPath, 'versions', versionId);
    
    // Save model
    await model.save(`file://${modelPath}`);
    
    // Save metadata
    const metadataPath = path.join(modelPath, 'metadata.json');
    await fs.writeFile(metadataPath, JSON.stringify({
      versionId,
      experimentId,
      createdAt: Date.now(),
      ...metadata
    }, null, 2));
    
    // Update registry
    this.modelVersions.set(versionId, {
      versionId,
      experimentId,
      modelPath,
      metadata,
      createdAt: Date.now(),
      isActive: false
    });
    
    this.emit('modelVersionSaved', { versionId, experimentId, modelPath });
    
    return versionId;
  }

  /**
   * Deploy model version
   * @param {string} versionId - Model version to deploy
   * @param {Object} deploymentConfig - Deployment configuration
   */
  async deployModel(versionId, deploymentConfig = {}) {
    const modelVersion = this.modelVersions.get(versionId);
    if (!modelVersion) {
      throw new Error(`Model version ${versionId} not found`);
    }

    try {
      // Load model
      const model = await tf.loadLayersModel(`file://${modelVersion.modelPath}/model.json`);
      
      // Setup A/B testing if enabled
      if (this.options.enableABTesting && deploymentConfig.abTest) {
        await this.setupModelABTest(versionId, model, deploymentConfig.abTest);
      } else {
        // Direct deployment
        this.pipelineState.activeModels.set(deploymentConfig.modelName || 'default', {
          versionId,
          model,
          deployedAt: Date.now(),
          config: deploymentConfig
        });
      }
      
      // Mark as active
      modelVersion.isActive = true;
      
      this.emit('modelDeployed', { versionId, deploymentConfig });
      
      return {
        versionId,
        status: 'deployed',
        deployedAt: Date.now()
      };
    } catch (error) {
      this.emit('modelDeploymentFailed', { versionId, error });
      throw error;
    }
  }

  /**
   * Setup A/B test for model
   * @param {string} versionId - Model version
   * @param {tf.LayersModel} model - Model instance
   * @param {Object} abTestConfig - A/B test configuration
   */
  async setupModelABTest(versionId, model, abTestConfig) {
    const testId = `ab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const abTest = {
      testId,
      versionId,
      model,
      trafficSplit: abTestConfig.trafficSplit || 0.1, // 10% traffic
      duration: abTestConfig.duration || 604800000, // 7 days
      metrics: abTestConfig.metrics || ['accuracy', 'latency'],
      startedAt: Date.now(),
      status: 'active',
      results: {
        requests: 0,
        successes: 0,
        errors: 0,
        latencies: [],
        customMetrics: new Map()
      }
    };
    
    this.abTests.set(testId, abTest);
    this.trafficSplits.set(testId, abTestConfig.trafficSplit);
    
    this.emit('abTestStarted', { testId, versionId, abTestConfig });
    
    return testId;
  }

  setupDataValidators() {
    // Schema validator
    this.dataValidators.set('schema', (data) => {
      // Validate data schema
      return { valid: true, errors: [] };
    });
    
    // Quality validator
    this.dataValidators.set('quality', (data) => {
      // Check data quality
      return { valid: true, score: 0.95 };
    });
    
    // Drift validator
    this.dataValidators.set('drift', (data) => {
      // Detect data drift
      return { valid: true, driftScore: 0.05 };
    });
  }

  setupModelMonitoring() {
    // Monitor model performance every hour
    setInterval(() => {
      this.monitorModelPerformance();
    }, 3600000);
  }

  setupContinuousTraining() {
    // Schedule continuous training
    setInterval(() => {
      this.runContinuousTraining();
    }, 86400000); // Daily
  }

  setupABTesting() {
    // Monitor A/B tests
    setInterval(() => {
      this.monitorABTests();
    }, 3600000); // Hourly
  }

  async loadDataset(datasetName) {
    // Load dataset from storage
    const datasetPath = path.join(this.options.dataPath, `${datasetName}.json`);
    try {
      const data = await fs.readFile(datasetPath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      throw new Error(`Failed to load dataset ${datasetName}: ${error.message}`);
    }
  }

  async validateData(dataset) {
    const validationResults = {};
    
    for (const [validatorName, validator] of this.dataValidators) {
      validationResults[validatorName] = validator(dataset);
    }
    
    const isValid = Object.values(validationResults).every(result => result.valid);
    
    if (!isValid) {
      throw new Error('Data validation failed');
    }
    
    return dataset;
  }

  splitData(dataset) {
    const totalSize = dataset.length;
    const testSize = Math.floor(totalSize * this.options.testSplit);
    const validationSize = Math.floor(totalSize * this.options.validationSplit);
    const trainSize = totalSize - testSize - validationSize;
    
    // Shuffle data
    const shuffled = [...dataset].sort(() => Math.random() - 0.5);
    
    return {
      trainData: {
        features: shuffled.slice(0, trainSize).map(item => item.features),
        labels: shuffled.slice(0, trainSize).map(item => item.label)
      },
      validationData: {
        features: shuffled.slice(trainSize, trainSize + validationSize).map(item => item.features),
        labels: shuffled.slice(trainSize, trainSize + validationSize).map(item => item.label)
      },
      testData: {
        features: shuffled.slice(trainSize + validationSize).map(item => item.features),
        labels: shuffled.slice(trainSize + validationSize).map(item => item.label)
      }
    };
  }

  findBestEpoch(history) {
    const valLoss = history.val_loss;
    let bestEpoch = 0;
    let bestLoss = valLoss[0];
    
    for (let i = 1; i < valLoss.length; i++) {
      if (valLoss[i] < bestLoss) {
        bestLoss = valLoss[i];
        bestEpoch = i;
      }
    }
    
    return bestEpoch + 1;
  }

  calculateAdditionalMetrics(predictions, labels) {
    // Calculate precision, recall, F1-score, etc.
    return {
      precision: 0.85,
      recall: 0.82,
      f1Score: 0.83,
      auc: 0.89
    };
  }

  async loadModelRegistry() {
    // Load existing model registry
    try {
      const registryPath = path.join(this.options.modelPath, 'registry.json');
      const data = await fs.readFile(registryPath, 'utf8');
      const registry = JSON.parse(data);
      
      for (const [key, value] of Object.entries(registry)) {
        this.modelRegistry.set(key, value);
      }
    } catch (error) {
      // Registry doesn't exist yet, will be created
    }
  }

  async monitorModelPerformance() {
    // Monitor deployed models
    for (const [modelName, deployment] of this.pipelineState.activeModels) {
      const metrics = await this.collectModelMetrics(modelName, deployment);
      this.performanceMetrics.set(modelName, metrics);
      
      // Check for performance degradation
      if (metrics.accuracy < 0.8) {
        this.emit('modelPerformanceDegraded', { modelName, metrics });
      }
    }
  }

  async runContinuousTraining() {
    // Run continuous training for active models
    this.emit('continuousTrainingStarted');
  }

  async monitorABTests() {
    // Monitor active A/B tests
    for (const [testId, abTest] of this.abTests) {
      if (abTest.status === 'active') {
        const elapsed = Date.now() - abTest.startedAt;
        
        if (elapsed > abTest.duration) {
          await this.concludeABTest(testId);
        }
      }
    }
  }

  async collectModelMetrics(modelName, deployment) {
    // Collect performance metrics for deployed model
    return {
      accuracy: 0.85,
      latency: 50,
      throughput: 1000,
      errorRate: 0.01,
      timestamp: Date.now()
    };
  }

  async concludeABTest(testId) {
    const abTest = this.abTests.get(testId);
    if (!abTest) return;
    
    abTest.status = 'completed';
    abTest.completedAt = Date.now();
    
    // Analyze results and make deployment decision
    const decision = this.analyzeABTestResults(abTest);
    
    this.emit('abTestCompleted', { testId, abTest, decision });
  }

  analyzeABTestResults(abTest) {
    // Analyze A/B test results and make deployment decision
    const successRate = abTest.results.successes / abTest.results.requests;
    const avgLatency = abTest.results.latencies.reduce((a, b) => a + b, 0) / abTest.results.latencies.length;
    
    return {
      deploy: successRate > 0.95 && avgLatency < 100,
      metrics: {
        successRate,
        avgLatency,
        totalRequests: abTest.results.requests
      }
    };
  }

  getMetrics() {
    return {
      experiments: this.pipelineState.experiments.size,
      modelVersions: this.modelVersions.size,
      activeModels: this.pipelineState.activeModels.size,
      abTests: this.abTests.size,
      trainingJobs: this.trainingJobs.size,
      datasets: this.datasets.size,
      pipelineState: {
        isTraining: this.pipelineState.isTraining,
        trainingProgress: this.pipelineState.trainingProgress,
        currentExperiment: this.pipelineState.currentExperiment
      }
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down ML Pipeline...');
    
    // Save model registry
    const registryPath = path.join(this.options.modelPath, 'registry.json');
    await fs.writeFile(registryPath, JSON.stringify(Object.fromEntries(this.modelRegistry), null, 2));
    
    // Dispose of active models
    for (const deployment of this.pipelineState.activeModels.values()) {
      if (deployment.model) {
        deployment.model.dispose();
      }
    }
    
    // Clear data
    this.modelRegistry.clear();
    this.modelVersions.clear();
    this.abTests.clear();
    this.pipelineState.activeModels.clear();
    this.pipelineState.experiments.clear();
    
    console.log('✅ ML Pipeline shutdown complete');
  }
}

export default MLPipeline;
