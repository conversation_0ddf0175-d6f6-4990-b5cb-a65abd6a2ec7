/**
 * Metrics Collector Service
 * Centralized metrics collection and aggregation for Prometheus integration
 */

import { EventEmitter } from 'events';
import { register, Counter, Histogram, Gauge, Summary } from 'prom-client';
import express from 'express';

export class MetricsCollector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      port: options.port || 9464,
      enableDefaultMetrics: options.enableDefaultMetrics !== false,
      enableCustomMetrics: options.enableCustomMetrics !== false,
      metricsPath: options.metricsPath || '/metrics',
      enableHttpServer: options.enableHttpServer !== false,
      collectInterval: options.collectInterval || 10000, // 10 seconds
      enableAggregation: options.enableAggregation !== false,
      retentionPeriod: options.retentionPeriod || 3600000, // 1 hour
      ...options
    };

    // Metrics registry
    this.registry = register;
    
    // HTTP server for metrics endpoint
    this.server = null;
    this.app = null;
    
    // Custom metrics
    this.metrics = {
      // HTTP metrics
      httpRequestsTotal: null,
      httpRequestDuration: null,
      httpRequestSize: null,
      httpResponseSize: null,
      
      // WebSocket metrics
      websocketConnectionsActive: null,
      websocketMessagesTotal: null,
      websocketMessageDuration: null,
      websocketConnectionDuration: null,
      
      // Database metrics
      databaseQueriesTotal: null,
      databaseQueryDuration: null,
      databaseConnectionsActive: null,
      databaseConnectionErrors: null,
      
      // Cache metrics
      cacheOperationsTotal: null,
      cacheHitRatio: null,
      cacheSize: null,
      
      // Security metrics
      authenticationAttemptsTotal: null,
      authenticationFailuresTotal: null,
      mfaVerificationsTotal: null,
      securityEventsTotal: null,
      
      // Business metrics
      activeUsersGauge: null,
      assetUploadsTotal: null,
      showroomVisitsTotal: null,
      apiCallsTotal: null,
      
      // Performance metrics
      memoryUsageGauge: null,
      cpuUsageGauge: null,
      eventLoopLagHistogram: null,
      gcDurationHistogram: null,
      
      // Error metrics
      errorsTotal: null,
      errorRate: null,
      
      // Custom metrics storage
      custom: new Map()
    };
    
    // Aggregated data storage
    this.aggregatedData = new Map();
    this.rawData = new Map();
    
    // Collection timers
    this.collectionTimers = new Map();

    this.initialize();
  }

  async initialize() {
    try {
      console.log('📊 Initializing Metrics Collector...');
      
      // Clear default registry if needed
      if (!this.options.enableDefaultMetrics) {
        this.registry.clear();
      }
      
      // Initialize custom metrics
      if (this.options.enableCustomMetrics) {
        this.initializeCustomMetrics();
      }
      
      // Setup HTTP server
      if (this.options.enableHttpServer) {
        await this.setupHttpServer();
      }
      
      // Start collection
      this.startCollection();
      
      // Setup cleanup
      this.startCleanup();
      
      console.log(`✅ Metrics Collector initialized on port ${this.options.port}`);
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize Metrics Collector:', error);
      this.emit('error', error);
    }
  }

  initializeCustomMetrics() {
    // HTTP metrics
    this.metrics.httpRequestsTotal = new Counter({
      name: 'http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code'],
      registers: [this.registry]
    });

    this.metrics.httpRequestDuration = new Histogram({
      name: 'http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
      registers: [this.registry]
    });

    this.metrics.httpRequestSize = new Histogram({
      name: 'http_request_size_bytes',
      help: 'HTTP request size in bytes',
      labelNames: ['method', 'route'],
      buckets: [100, 1000, 10000, 100000, 1000000],
      registers: [this.registry]
    });

    this.metrics.httpResponseSize = new Histogram({
      name: 'http_response_size_bytes',
      help: 'HTTP response size in bytes',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [100, 1000, 10000, 100000, 1000000],
      registers: [this.registry]
    });

    // WebSocket metrics
    this.metrics.websocketConnectionsActive = new Gauge({
      name: 'websocket_connections_active',
      help: 'Number of active WebSocket connections',
      labelNames: ['pool_id'],
      registers: [this.registry]
    });

    this.metrics.websocketMessagesTotal = new Counter({
      name: 'websocket_messages_total',
      help: 'Total number of WebSocket messages',
      labelNames: ['type', 'direction'],
      registers: [this.registry]
    });

    this.metrics.websocketMessageDuration = new Histogram({
      name: 'websocket_message_duration_seconds',
      help: 'WebSocket message processing duration',
      labelNames: ['type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.registry]
    });

    this.metrics.websocketConnectionDuration = new Histogram({
      name: 'websocket_connection_duration_seconds',
      help: 'WebSocket connection duration',
      buckets: [1, 10, 60, 300, 1800, 3600, 7200],
      registers: [this.registry]
    });

    // Database metrics
    this.metrics.databaseQueriesTotal = new Counter({
      name: 'database_queries_total',
      help: 'Total number of database queries',
      labelNames: ['operation', 'table', 'status'],
      registers: [this.registry]
    });

    this.metrics.databaseQueryDuration = new Histogram({
      name: 'database_query_duration_seconds',
      help: 'Database query duration in seconds',
      labelNames: ['operation', 'table'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5],
      registers: [this.registry]
    });

    this.metrics.databaseConnectionsActive = new Gauge({
      name: 'database_connections_active',
      help: 'Number of active database connections',
      labelNames: ['database'],
      registers: [this.registry]
    });

    // Cache metrics
    this.metrics.cacheOperationsTotal = new Counter({
      name: 'cache_operations_total',
      help: 'Total number of cache operations',
      labelNames: ['operation', 'cache_type', 'result'],
      registers: [this.registry]
    });

    this.metrics.cacheHitRatio = new Gauge({
      name: 'cache_hit_ratio',
      help: 'Cache hit ratio',
      labelNames: ['cache_type'],
      registers: [this.registry]
    });

    this.metrics.cacheSize = new Gauge({
      name: 'cache_size_bytes',
      help: 'Cache size in bytes',
      labelNames: ['cache_type'],
      registers: [this.registry]
    });

    // Security metrics
    this.metrics.authenticationAttemptsTotal = new Counter({
      name: 'authentication_attempts_total',
      help: 'Total number of authentication attempts',
      labelNames: ['method', 'result'],
      registers: [this.registry]
    });

    this.metrics.mfaVerificationsTotal = new Counter({
      name: 'mfa_verifications_total',
      help: 'Total number of MFA verifications',
      labelNames: ['method', 'result'],
      registers: [this.registry]
    });

    this.metrics.securityEventsTotal = new Counter({
      name: 'security_events_total',
      help: 'Total number of security events',
      labelNames: ['event_type', 'severity'],
      registers: [this.registry]
    });

    // Business metrics
    this.metrics.activeUsersGauge = new Gauge({
      name: 'active_users',
      help: 'Number of active users',
      labelNames: ['time_window'],
      registers: [this.registry]
    });

    this.metrics.assetUploadsTotal = new Counter({
      name: 'asset_uploads_total',
      help: 'Total number of asset uploads',
      labelNames: ['asset_type', 'status'],
      registers: [this.registry]
    });

    this.metrics.showroomVisitsTotal = new Counter({
      name: 'showroom_visits_total',
      help: 'Total number of showroom visits',
      labelNames: ['showroom_id'],
      registers: [this.registry]
    });

    // Performance metrics
    this.metrics.memoryUsageGauge = new Gauge({
      name: 'memory_usage_bytes',
      help: 'Memory usage in bytes',
      labelNames: ['type'],
      registers: [this.registry]
    });

    this.metrics.cpuUsageGauge = new Gauge({
      name: 'cpu_usage_percent',
      help: 'CPU usage percentage',
      labelNames: ['type'],
      registers: [this.registry]
    });

    this.metrics.eventLoopLagHistogram = new Histogram({
      name: 'event_loop_lag_seconds',
      help: 'Event loop lag in seconds',
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.registry]
    });

    this.metrics.gcDurationHistogram = new Histogram({
      name: 'gc_duration_seconds',
      help: 'Garbage collection duration in seconds',
      labelNames: ['gc_type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1],
      registers: [this.registry]
    });

    // Error metrics
    this.metrics.errorsTotal = new Counter({
      name: 'errors_total',
      help: 'Total number of errors',
      labelNames: ['error_type', 'severity'],
      registers: [this.registry]
    });

    this.metrics.errorRate = new Gauge({
      name: 'error_rate',
      help: 'Error rate percentage',
      labelNames: ['service'],
      registers: [this.registry]
    });
  }

  async setupHttpServer() {
    this.app = express();
    
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: Date.now(),
        uptime: process.uptime(),
        metrics: {
          total: this.registry.getSingleMetricAsString('').split('\n').length,
          custom: this.metrics.custom.size
        }
      });
    });
    
    // Metrics endpoint
    this.app.get(this.options.metricsPath, async (req, res) => {
      try {
        res.set('Content-Type', this.registry.contentType);
        const metrics = await this.registry.metrics();
        res.end(metrics);
      } catch (error) {
        console.error('Error serving metrics:', error);
        res.status(500).end('Error serving metrics');
      }
    });
    
    // Custom metrics endpoint
    this.app.get('/metrics/custom', (req, res) => {
      const customMetrics = {};
      for (const [name, metric] of this.metrics.custom) {
        customMetrics[name] = metric.get ? metric.get() : metric;
      }
      res.json(customMetrics);
    });
    
    // Aggregated data endpoint
    this.app.get('/metrics/aggregated', (req, res) => {
      const { start, end } = req.query;
      const startTime = start ? parseInt(start) : Date.now() - 3600000; // 1 hour ago
      const endTime = end ? parseInt(end) : Date.now();
      
      const aggregated = this.getAggregatedData(startTime, endTime);
      res.json(aggregated);
    });

    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.options.port, (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  startCollection() {
    // Collect system metrics
    const systemTimer = setInterval(() => {
      this.collectSystemMetrics();
    }, this.options.collectInterval);
    
    this.collectionTimers.set('system', systemTimer);
    
    // Collect aggregated metrics
    if (this.options.enableAggregation) {
      const aggregationTimer = setInterval(() => {
        this.aggregateMetrics();
      }, this.options.collectInterval * 2);
      
      this.collectionTimers.set('aggregation', aggregationTimer);
    }
  }

  startCleanup() {
    // Cleanup old data every 10 minutes
    const cleanupTimer = setInterval(() => {
      this.cleanupOldData();
    }, 600000);
    
    this.collectionTimers.set('cleanup', cleanupTimer);
  }

  collectSystemMetrics() {
    const timestamp = Date.now();
    
    // Memory metrics
    const memUsage = process.memoryUsage();
    this.metrics.memoryUsageGauge.set({ type: 'heap_used' }, memUsage.heapUsed);
    this.metrics.memoryUsageGauge.set({ type: 'heap_total' }, memUsage.heapTotal);
    this.metrics.memoryUsageGauge.set({ type: 'rss' }, memUsage.rss);
    this.metrics.memoryUsageGauge.set({ type: 'external' }, memUsage.external);
    
    // CPU metrics
    const cpuUsage = process.cpuUsage();
    this.metrics.cpuUsageGauge.set({ type: 'user' }, cpuUsage.user / 1000000); // Convert to seconds
    this.metrics.cpuUsageGauge.set({ type: 'system' }, cpuUsage.system / 1000000);
    
    // Store raw data for aggregation
    this.rawData.set(timestamp, {
      memory: memUsage,
      cpu: cpuUsage,
      timestamp
    });
  }

  aggregateMetrics() {
    const now = Date.now();
    const fiveMinutesAgo = now - 300000; // 5 minutes
    
    // Get data from the last 5 minutes
    const recentData = Array.from(this.rawData.entries())
      .filter(([timestamp]) => timestamp > fiveMinutesAgo)
      .map(([, data]) => data);
    
    if (recentData.length === 0) return;
    
    // Calculate aggregations
    const aggregated = {
      timestamp: now,
      memory: {
        avg_heap_used: this.average(recentData.map(d => d.memory.heapUsed)),
        max_heap_used: Math.max(...recentData.map(d => d.memory.heapUsed)),
        min_heap_used: Math.min(...recentData.map(d => d.memory.heapUsed))
      },
      cpu: {
        avg_user: this.average(recentData.map(d => d.cpu.user)),
        avg_system: this.average(recentData.map(d => d.cpu.system))
      },
      count: recentData.length
    };
    
    this.aggregatedData.set(now, aggregated);
    
    this.emit('metricsAggregated', aggregated);
  }

  cleanupOldData() {
    const cutoff = Date.now() - this.options.retentionPeriod;
    
    // Clean up raw data
    for (const [timestamp] of this.rawData) {
      if (timestamp < cutoff) {
        this.rawData.delete(timestamp);
      }
    }
    
    // Clean up aggregated data
    for (const [timestamp] of this.aggregatedData) {
      if (timestamp < cutoff) {
        this.aggregatedData.delete(timestamp);
      }
    }
  }

  /**
   * Record HTTP request metrics
   */
  recordHttpRequest(method, route, statusCode, duration, requestSize, responseSize) {
    const labels = { method, route, status_code: statusCode.toString() };
    
    this.metrics.httpRequestsTotal.inc(labels);
    this.metrics.httpRequestDuration.observe(labels, duration / 1000);
    
    if (requestSize) {
      this.metrics.httpRequestSize.observe({ method, route }, requestSize);
    }
    
    if (responseSize) {
      this.metrics.httpResponseSize.observe(labels, responseSize);
    }
  }

  /**
   * Record WebSocket metrics
   */
  recordWebSocketEvent(event, data = {}) {
    switch (event) {
      case 'connection':
        this.metrics.websocketConnectionsActive.inc({ pool_id: data.poolId || 'default' });
        break;
      case 'disconnection':
        this.metrics.websocketConnectionsActive.dec({ pool_id: data.poolId || 'default' });
        if (data.duration) {
          this.metrics.websocketConnectionDuration.observe(data.duration / 1000);
        }
        break;
      case 'message':
        this.metrics.websocketMessagesTotal.inc({ 
          type: data.type || 'unknown', 
          direction: data.direction || 'unknown' 
        });
        if (data.duration) {
          this.metrics.websocketMessageDuration.observe({ type: data.type || 'unknown' }, data.duration / 1000);
        }
        break;
    }
  }

  /**
   * Record database query metrics
   */
  recordDatabaseQuery(operation, table, duration, status = 'success') {
    const labels = { operation, table, status };
    
    this.metrics.databaseQueriesTotal.inc(labels);
    this.metrics.databaseQueryDuration.observe({ operation, table }, duration / 1000);
  }

  /**
   * Record cache operation metrics
   */
  recordCacheOperation(operation, cacheType, result) {
    this.metrics.cacheOperationsTotal.inc({ operation, cache_type: cacheType, result });
  }

  /**
   * Record security event metrics
   */
  recordSecurityEvent(eventType, severity = 'info') {
    this.metrics.securityEventsTotal.inc({ event_type: eventType, severity });
  }

  /**
   * Create custom metric
   */
  createCustomMetric(name, type, options = {}) {
    let metric;
    
    switch (type) {
      case 'counter':
        metric = new Counter({ name, ...options, registers: [this.registry] });
        break;
      case 'gauge':
        metric = new Gauge({ name, ...options, registers: [this.registry] });
        break;
      case 'histogram':
        metric = new Histogram({ name, ...options, registers: [this.registry] });
        break;
      case 'summary':
        metric = new Summary({ name, ...options, registers: [this.registry] });
        break;
      default:
        throw new Error(`Unknown metric type: ${type}`);
    }
    
    this.metrics.custom.set(name, metric);
    return metric;
  }

  /**
   * Get aggregated data for time range
   */
  getAggregatedData(startTime, endTime) {
    const result = [];
    
    for (const [timestamp, data] of this.aggregatedData) {
      if (timestamp >= startTime && timestamp <= endTime) {
        result.push(data);
      }
    }
    
    return result.sort((a, b) => a.timestamp - b.timestamp);
  }

  average(numbers) {
    return numbers.length > 0 ? numbers.reduce((a, b) => a + b, 0) / numbers.length : 0;
  }

  getMetrics() {
    return {
      customMetrics: this.metrics.custom.size,
      rawDataPoints: this.rawData.size,
      aggregatedDataPoints: this.aggregatedData.size,
      collectionTimers: this.collectionTimers.size,
      registryMetrics: this.registry.getSingleMetricAsString('').split('\n').length
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down Metrics Collector...');
    
    // Clear timers
    for (const timer of this.collectionTimers.values()) {
      clearInterval(timer);
    }
    
    // Close HTTP server
    if (this.server) {
      await new Promise((resolve) => {
        this.server.close(resolve);
      });
    }
    
    // Clear registry
    this.registry.clear();
    
    // Clear data
    this.rawData.clear();
    this.aggregatedData.clear();
    this.metrics.custom.clear();
    this.collectionTimers.clear();
    
    console.log('✅ Metrics Collector shutdown complete');
  }
}

export default MetricsCollector;
