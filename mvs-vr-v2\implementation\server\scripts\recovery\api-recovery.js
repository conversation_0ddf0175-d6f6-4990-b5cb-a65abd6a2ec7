/**
 * API Recovery Script
 * 
 * This script handles recovery of API services.
 */

const axios = require('axios');
const { execSync } = require('child_process');
const logger = require('../../utils/logger').getLogger('api-recovery');

// Configuration
const config = {
  apiUrl: process.env.API_URL || 'http://localhost:3000',
  healthEndpoint: '/api/health',
  maxRetries: 5,
  retryDelay: 5000, // 5 seconds
  restartCommand: process.env.API_RESTART_COMMAND || 'docker-compose restart api'
};

/**
 * Recover API services
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverAPI(options = {}) {
  const {
    test = false,
    restart = true,
    target = config.apiUrl
  } = options;
  
  logger.info('Starting API recovery');
  
  try {
    if (test) {
      logger.info('[TEST] Would perform API recovery');
      const healthStatus = await checkAPIHealth(target);
      return {
        success: true,
        test: true,
        currentHealth: healthStatus,
        message: 'API recovery test completed'
      };
    }
    
    // Check current API health
    let isHealthy = await checkAPIHealth(target);
    
    if (isHealthy) {
      logger.info('API is already healthy, no recovery needed');
      return {
        success: true,
        message: 'API is already healthy',
        restarted: false
      };
    }
    
    // Restart API service if requested
    if (restart) {
      logger.info('Restarting API service');
      await restartAPIService();
      
      // Wait for service to start
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
    }
    
    // Wait for API to become healthy
    isHealthy = await waitForAPIHealth(target);
    
    if (!isHealthy) {
      throw new Error('API failed to become healthy after recovery attempts');
    }
    
    logger.info('API recovery completed successfully');
    
    return {
      success: true,
      message: 'API service recovered successfully',
      restarted: restart
    };
    
  } catch (error) {
    logger.error(`API recovery failed: ${error.message}`, { error });
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Check API health
 * @param {string} apiUrl - API URL
 * @returns {Promise<boolean>} Health status
 */
async function checkAPIHealth(apiUrl = config.apiUrl) {
  try {
    const response = await axios.get(`${apiUrl}${config.healthEndpoint}`, {
      timeout: 5000,
      validateStatus: (status) => status === 200
    });
    
    return response.status === 200 && response.data?.status === 'healthy';
  } catch (error) {
    logger.debug(`API health check failed: ${error.message}`);
    return false;
  }
}

/**
 * Restart API service
 * @returns {Promise<void>}
 */
async function restartAPIService() {
  try {
    logger.info(`Executing restart command: ${config.restartCommand}`);
    execSync(config.restartCommand, { stdio: 'inherit' });
    logger.info('API service restart command executed');
  } catch (error) {
    logger.error(`Failed to restart API service: ${error.message}`);
    throw error;
  }
}

/**
 * Wait for API to become healthy
 * @param {string} apiUrl - API URL
 * @returns {Promise<boolean>} Final health status
 */
async function waitForAPIHealth(apiUrl = config.apiUrl) {
  logger.info('Waiting for API to become healthy');
  
  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    logger.info(`Health check attempt ${attempt}/${config.maxRetries}`);
    
    const isHealthy = await checkAPIHealth(apiUrl);
    
    if (isHealthy) {
      logger.info(`API became healthy after ${attempt} attempts`);
      return true;
    }
    
    if (attempt < config.maxRetries) {
      logger.info(`Waiting ${config.retryDelay}ms before next attempt`);
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
    }
  }
  
  logger.error(`API failed to become healthy after ${config.maxRetries} attempts`);
  return false;
}

/**
 * Get API status information
 * @param {string} apiUrl - API URL
 * @returns {Promise<Object>} API status
 */
async function getAPIStatus(apiUrl = config.apiUrl) {
  try {
    const response = await axios.get(`${apiUrl}${config.healthEndpoint}`, {
      timeout: 5000
    });
    
    return {
      available: true,
      status: response.status,
      data: response.data,
      responseTime: response.headers['x-response-time'] || 'unknown'
    };
  } catch (error) {
    return {
      available: false,
      error: error.message,
      code: error.code
    };
  }
}

/**
 * Perform comprehensive API health check
 * @param {string} apiUrl - API URL
 * @returns {Promise<Object>} Comprehensive health status
 */
async function comprehensiveHealthCheck(apiUrl = config.apiUrl) {
  const healthCheck = {
    timestamp: new Date().toISOString(),
    overall: false,
    checks: {}
  };
  
  // Basic health endpoint
  try {
    const basicHealth = await checkAPIHealth(apiUrl);
    healthCheck.checks.basic = {
      status: basicHealth ? 'healthy' : 'unhealthy',
      success: basicHealth
    };
  } catch (error) {
    healthCheck.checks.basic = {
      status: 'error',
      success: false,
      error: error.message
    };
  }
  
  // Test additional endpoints if basic health passes
  if (healthCheck.checks.basic.success) {
    const testEndpoints = [
      { name: 'auth', path: '/api/auth/status' },
      { name: 'database', path: '/api/health/database' },
      { name: 'storage', path: '/api/health/storage' }
    ];
    
    for (const endpoint of testEndpoints) {
      try {
        const response = await axios.get(`${apiUrl}${endpoint.path}`, {
          timeout: 3000,
          validateStatus: (status) => status < 500
        });
        
        healthCheck.checks[endpoint.name] = {
          status: response.status < 400 ? 'healthy' : 'degraded',
          success: response.status < 500,
          statusCode: response.status
        };
      } catch (error) {
        healthCheck.checks[endpoint.name] = {
          status: 'error',
          success: false,
          error: error.message
        };
      }
    }
  }
  
  // Determine overall health
  healthCheck.overall = Object.values(healthCheck.checks).every(check => check.success);
  
  return healthCheck;
}

// If script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    restart: !args.includes('--no-restart'),
    target: args.find(arg => arg.startsWith('--target='))?.split('=')[1]
  };
  
  recoverAPI(options)
    .then(result => {
      if (result.success) {
        console.log('API recovery completed successfully');
        process.exit(0);
      } else {
        console.error('API recovery failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  recoverAPI,
  checkAPIHealth,
  waitForAPIHealth,
  getAPIStatus,
  comprehensiveHealthCheck
};
