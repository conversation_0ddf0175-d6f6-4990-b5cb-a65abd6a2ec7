{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1476", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/sprint7-enhancements.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44425, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44425, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 331, "endOffset": 1583, "count": 0}], "isBlockCoverage": false}, {"functionName": "detectZScoreAnomalies", "ranges": [{"startOffset": 1823, "endOffset": 1909, "count": 1}], "isBlockCoverage": true}, {"functionName": "detectMADAnomalies", "ranges": [{"startOffset": 1912, "endOffset": 1995, "count": 1}], "isBlockCoverage": true}, {"functionName": "detectIQRAnomalies", "ranges": [{"startOffset": 1998, "endOffset": 2081, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 2116, "endOffset": 2876, "count": 2}], "isBlockCoverage": true}, {"functionName": "BusinessContinuityService", "ranges": [{"startOffset": 2120, "endOffset": 2146, "count": 2}], "isBlockCoverage": true}, {"functionName": "stop", "ranges": [{"startOffset": 2149, "endOffset": 2161, "count": 2}], "isBlockCoverage": true}, {"functionName": "calculateBusinessImpact", "ranges": [{"startOffset": 2164, "endOffset": 2624, "count": 3}, {"startOffset": 2241, "endOffset": 2322, "count": 1}, {"startOffset": 2322, "endOffset": 2620, "count": 2}, {"startOffset": 2355, "endOffset": 2620, "count": 1}, {"startOffset": 2552, "endOffset": 2620, "count": 0}], "isBlockCoverage": true}, {"functionName": "updateBusinessMetric", "ranges": [{"startOffset": 2627, "endOffset": 2672, "count": 2}], "isBlockCoverage": true}, {"functionName": "getBusinessMetrics", "ranges": [{"startOffset": 2675, "endOffset": 2842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2929, "endOffset": 13299, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2993, "endOffset": 6855, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3029, "endOffset": 5321, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3090, "endOffset": 4170, "count": 3}], "isBlockCoverage": true}, {"functionName": "res.json", "ranges": [{"startOffset": 3170, "endOffset": 4145, "count": 3}, {"startOffset": 3718, "endOffset": 3746, "count": 1}, {"startOffset": 3748, "endOffset": 3815, "count": 1}, {"startOffset": 3815, "endOffset": 3844, "count": 2}, {"startOffset": 3844, "endOffset": 4087, "count": 1}, {"startOffset": 4087, "endOffset": 4144, "count": 0}], "isBlockCoverage": true}, {"functionName": "sanitize", "ranges": [{"startOffset": 3266, "endOffset": 3691, "count": 2}, {"startOffset": 3328, "endOffset": 3335, "count": 0}, {"startOffset": 3373, "endOffset": 3677, "count": 9}, {"startOffset": 3414, "endOffset": 3432, "count": 8}, {"startOffset": 3433, "endOffset": 3456, "count": 7}, {"startOffset": 3457, "endOffset": 3476, "count": 6}, {"startOffset": 3477, "endOffset": 3500, "count": 5}, {"startOffset": 3502, "endOffset": 3564, "count": 5}, {"startOffset": 3564, "endOffset": 3661, "count": 4}, {"startOffset": 3604, "endOffset": 3661, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4210, "endOffset": 4608, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4644, "endOffset": 4850, "count": 1}], "isBlockCoverage": true}, {"functionName": "app.get.res.json.users.id", "ranges": [{"startOffset": 4904, "endOffset": 5011, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5021, "endOffset": 5306, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5379, "endOffset": 5988, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6037, "endOffset": 6379, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6436, "endOffset": 6849, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6914, "endOffset": 10994, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6963, "endOffset": 7032, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7093, "endOffset": 8338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8138, "endOffset": 8160, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8395, "endOffset": 9675, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9473, "endOffset": 9495, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9732, "endOffset": 10988, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10786, "endOffset": 10808, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11043, "endOffset": 13295, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11094, "endOffset": 11553, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11357, "endOffset": 11545, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11570, "endOffset": 11642, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11698, "endOffset": 12675, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12719, "endOffset": 13289, "count": 1}], "isBlockCoverage": true}]}]}