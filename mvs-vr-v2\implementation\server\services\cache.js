/**
 * Cache Service
 * 
 * This service manages caching operations for the MVS-VR platform.
 * It supports both in-memory and Redis caching with automatic fallback.
 */

const configService = require('./config');

// In-memory cache implementation
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  set(key, value, ttl = 3600) {
    // Clear existing timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // Set value
    this.cache.set(key, {
      value,
      createdAt: Date.now(),
      ttl
    });

    // Set expiration timer
    if (ttl > 0) {
      const timer = setTimeout(() => {
        this.delete(key);
      }, ttl * 1000);
      this.timers.set(key, timer);
    }

    return true;
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) {
      return null;
    }

    // Check if expired
    if (item.ttl > 0 && (Date.now() - item.createdAt) > (item.ttl * 1000)) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  delete(key) {
    // Clear timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }

    return this.cache.delete(key);
  }

  clear() {
    // Clear all timers
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();
    this.cache.clear();
  }

  has(key) {
    return this.cache.has(key) && this.get(key) !== null;
  }

  size() {
    return this.cache.size;
  }

  keys() {
    return Array.from(this.cache.keys());
  }

  getStats() {
    const now = Date.now();
    let expired = 0;
    let active = 0;

    for (const [key, item] of this.cache.entries()) {
      if (item.ttl > 0 && (now - item.createdAt) > (item.ttl * 1000)) {
        expired++;
      } else {
        active++;
      }
    }

    return {
      total: this.cache.size,
      active,
      expired,
      timers: this.timers.size
    };
  }
}

let cacheInstance = null;
let cacheType = 'memory';
let isInitialized = false;

/**
 * Initialize cache service
 * @returns {Promise<void>}
 */
async function initialize() {
  try {
    const config = await configService.getSystemConfig();
    
    // For now, we'll use memory cache
    // In the future, we can add Redis support based on configuration
    cacheInstance = new MemoryCache();
    cacheType = 'memory';
    
    isInitialized = true;
    console.log(`Cache service initialized with type: ${cacheType}`);
  } catch (error) {
    console.error('Failed to initialize cache service:', error);
    throw error;
  }
}

/**
 * Get cache instance
 * @returns {Object} Cache instance
 */
function getCache() {
  if (!isInitialized) {
    throw new Error('Cache not initialized. Call initialize() first.');
  }
  return cacheInstance;
}

/**
 * Set a value in cache
 * @param {string} key - Cache key
 * @param {any} value - Value to cache
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<boolean>} Success status
 */
async function set(key, value, ttl = 3600) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    return cacheInstance.set(key, value, ttl);
  } catch (error) {
    console.error('Cache set failed:', error);
    return false;
  }
}

/**
 * Get a value from cache
 * @param {string} key - Cache key
 * @returns {Promise<any>} Cached value or null
 */
async function get(key) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    return cacheInstance.get(key);
  } catch (error) {
    console.error('Cache get failed:', error);
    return null;
  }
}

/**
 * Delete a value from cache
 * @param {string} key - Cache key
 * @returns {Promise<boolean>} Success status
 */
async function del(key) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    return cacheInstance.delete(key);
  } catch (error) {
    console.error('Cache delete failed:', error);
    return false;
  }
}

/**
 * Check if key exists in cache
 * @param {string} key - Cache key
 * @returns {Promise<boolean>} Existence status
 */
async function has(key) {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    return cacheInstance.has(key);
  } catch (error) {
    console.error('Cache has failed:', error);
    return false;
  }
}

/**
 * Clear all cache entries
 * @returns {Promise<boolean>} Success status
 */
async function clear() {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    cacheInstance.clear();
    return true;
  } catch (error) {
    console.error('Cache clear failed:', error);
    return false;
  }
}

/**
 * Ping cache to check connectivity
 * @returns {Promise<boolean>} Connection status
 */
async function ping() {
  try {
    if (!isInitialized) {
      await initialize();
    }
    
    // Test cache by setting and getting a test value
    const testKey = '__cache_ping_test__';
    const testValue = Date.now();
    
    await set(testKey, testValue, 1);
    const retrieved = await get(testKey);
    await del(testKey);
    
    return retrieved === testValue;
  } catch (error) {
    console.error('Cache ping failed:', error);
    return false;
  }
}

/**
 * Get cache health status
 * @returns {Promise<Object>} Health status
 */
async function getHealthStatus() {
  try {
    const isHealthy = await ping();
    const stats = isInitialized ? cacheInstance.getStats() : null;
    
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      type: cacheType,
      initialized: isInitialized,
      timestamp: new Date().toISOString(),
      stats
    };
  } catch (error) {
    return {
      status: 'error',
      type: cacheType,
      initialized: isInitialized,
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

/**
 * Get cache statistics
 * @returns {Promise<Object>} Cache statistics
 */
async function getStatistics() {
  try {
    if (!isInitialized) {
      return {
        timestamp: new Date().toISOString(),
        error: 'Cache not initialized'
      };
    }
    
    const stats = cacheInstance.getStats();
    
    return {
      timestamp: new Date().toISOString(),
      type: cacheType,
      ...stats
    };
  } catch (error) {
    return {
      timestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

/**
 * Get or set a value with a factory function
 * @param {string} key - Cache key
 * @param {Function} factory - Function to generate value if not cached
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<any>} Cached or generated value
 */
async function getOrSet(key, factory, ttl = 3600) {
  try {
    let value = await get(key);
    
    if (value === null) {
      value = await factory();
      await set(key, value, ttl);
    }
    
    return value;
  } catch (error) {
    console.error('Cache getOrSet failed:', error);
    // If cache fails, still try to get the value from factory
    try {
      return await factory();
    } catch (factoryError) {
      console.error('Factory function failed:', factoryError);
      throw factoryError;
    }
  }
}

module.exports = {
  initialize,
  getCache,
  set,
  get,
  del,
  has,
  clear,
  ping,
  getHealthStatus,
  getStatistics,
  getOrSet
};
