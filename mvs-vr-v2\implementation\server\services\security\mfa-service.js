/**
 * Multi-Factor Authentication (MFA) Service
 * Provides TOTP, SMS, email, and hardware token authentication
 */

import crypto from 'crypto';
import { EventEmitter } from 'events';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';

export class MFAService extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      serviceName: options.serviceName || 'MVS-VR',
      issuer: options.issuer || 'MVS-VR Platform',
      totpWindow: options.totpWindow || 2, // Allow 2 time steps before/after
      totpStep: options.totpStep || 30, // 30 second time steps
      backupCodeLength: options.backupCodeLength || 8,
      backupCodeCount: options.backupCodeCount || 10,
      smsProvider: options.smsProvider || null,
      emailProvider: options.emailProvider || null,
      enableHardwareTokens: options.enableHardwareTokens || false,
      sessionTimeout: options.sessionTimeout || 3600000, // 1 hour
      maxAttempts: options.maxAttempts || 5,
      lockoutDuration: options.lockoutDuration || 900000, // 15 minutes
      ...options
    };

    // User MFA data storage
    this.userMFA = new Map(); // userId -> MFA data
    this.pendingVerifications = new Map(); // verificationId -> verification data
    this.activeSessions = new Map(); // sessionId -> session data
    this.attemptCounts = new Map(); // userId -> attempt data
    
    // Metrics
    this.metrics = {
      totpVerifications: 0,
      smsVerifications: 0,
      emailVerifications: 0,
      hardwareTokenVerifications: 0,
      backupCodeUsages: 0,
      failedAttempts: 0,
      successfulVerifications: 0,
      lastReset: Date.now()
    };

    this.initialize();
  }

  initialize() {
    console.log('🔐 Initializing MFA Service');
    
    // Start cleanup timer for expired sessions and verifications
    this.startCleanupTimer();
    
    console.log('✅ MFA Service initialized');
    this.emit('ready');
  }

  /**
   * Setup TOTP for a user
   * @param {string} userId - User identifier
   * @param {Object} options - Setup options
   */
  async setupTOTP(userId, options = {}) {
    try {
      // Generate secret
      const secret = speakeasy.generateSecret({
        name: `${this.options.serviceName} (${options.userEmail || userId})`,
        issuer: this.options.issuer,
        length: 32
      });

      // Generate QR code
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

      // Store user MFA data
      const mfaData = this.getUserMFA(userId);
      mfaData.totp = {
        secret: secret.base32,
        verified: false,
        setupAt: Date.now(),
        backupCodes: this.generateBackupCodes()
      };
      this.userMFA.set(userId, mfaData);

      this.emit('totpSetup', { userId, setupAt: Date.now() });

      return {
        secret: secret.base32,
        qrCode: qrCodeUrl,
        manualEntryKey: secret.base32,
        backupCodes: mfaData.totp.backupCodes
      };
    } catch (error) {
      console.error('TOTP setup error:', error);
      throw new Error(`TOTP setup failed: ${error.message}`);
    }
  }

  /**
   * Verify TOTP token
   * @param {string} userId - User identifier
   * @param {string} token - TOTP token
   */
  async verifyTOTP(userId, token) {
    try {
      const mfaData = this.getUserMFA(userId);
      
      if (!mfaData.totp || !mfaData.totp.secret) {
        throw new Error('TOTP not setup for user');
      }

      // Check attempt limits
      if (!this.checkAttemptLimits(userId)) {
        throw new Error('Too many failed attempts. Account temporarily locked.');
      }

      // Verify token
      const verified = speakeasy.totp.verify({
        secret: mfaData.totp.secret,
        encoding: 'base32',
        token: token,
        window: this.options.totpWindow,
        step: this.options.totpStep
      });

      if (verified) {
        // Mark TOTP as verified if first time
        if (!mfaData.totp.verified) {
          mfaData.totp.verified = true;
          mfaData.totp.verifiedAt = Date.now();
        }

        this.resetAttemptCount(userId);
        this.metrics.totpVerifications++;
        this.metrics.successfulVerifications++;

        this.emit('totpVerified', { userId, timestamp: Date.now() });

        return {
          verified: true,
          method: 'totp',
          timestamp: Date.now()
        };
      } else {
        this.incrementAttemptCount(userId);
        this.metrics.failedAttempts++;
        
        this.emit('totpFailed', { userId, timestamp: Date.now() });
        
        throw new Error('Invalid TOTP token');
      }
    } catch (error) {
      console.error('TOTP verification error:', error);
      throw error;
    }
  }

  /**
   * Setup SMS MFA for a user
   * @param {string} userId - User identifier
   * @param {string} phoneNumber - User's phone number
   */
  async setupSMS(userId, phoneNumber) {
    if (!this.options.smsProvider) {
      throw new Error('SMS provider not configured');
    }

    try {
      const mfaData = this.getUserMFA(userId);
      mfaData.sms = {
        phoneNumber: phoneNumber,
        verified: false,
        setupAt: Date.now()
      };
      this.userMFA.set(userId, mfaData);

      // Send verification SMS
      const verificationCode = this.generateVerificationCode();
      const verificationId = await this.sendSMSVerification(phoneNumber, verificationCode);

      this.pendingVerifications.set(verificationId, {
        userId,
        method: 'sms',
        code: verificationCode,
        phoneNumber,
        createdAt: Date.now(),
        expiresAt: Date.now() + 300000 // 5 minutes
      });

      this.emit('smsSetup', { userId, phoneNumber, verificationId });

      return { verificationId };
    } catch (error) {
      console.error('SMS setup error:', error);
      throw new Error(`SMS setup failed: ${error.message}`);
    }
  }

  /**
   * Send SMS verification code
   * @param {string} userId - User identifier
   */
  async sendSMSCode(userId) {
    const mfaData = this.getUserMFA(userId);
    
    if (!mfaData.sms || !mfaData.sms.verified) {
      throw new Error('SMS MFA not setup or verified for user');
    }

    if (!this.checkAttemptLimits(userId)) {
      throw new Error('Too many failed attempts. Account temporarily locked.');
    }

    try {
      const verificationCode = this.generateVerificationCode();
      const verificationId = await this.sendSMSVerification(mfaData.sms.phoneNumber, verificationCode);

      this.pendingVerifications.set(verificationId, {
        userId,
        method: 'sms',
        code: verificationCode,
        phoneNumber: mfaData.sms.phoneNumber,
        createdAt: Date.now(),
        expiresAt: Date.now() + 300000 // 5 minutes
      });

      this.emit('smsCodeSent', { userId, verificationId });

      return { verificationId };
    } catch (error) {
      console.error('SMS send error:', error);
      throw new Error(`Failed to send SMS: ${error.message}`);
    }
  }

  /**
   * Verify SMS code
   * @param {string} verificationId - Verification identifier
   * @param {string} code - SMS verification code
   */
  async verifySMSCode(verificationId, code) {
    const verification = this.pendingVerifications.get(verificationId);
    
    if (!verification) {
      throw new Error('Invalid or expired verification ID');
    }

    if (Date.now() > verification.expiresAt) {
      this.pendingVerifications.delete(verificationId);
      throw new Error('Verification code expired');
    }

    if (!this.checkAttemptLimits(verification.userId)) {
      throw new Error('Too many failed attempts. Account temporarily locked.');
    }

    try {
      if (verification.code === code) {
        // Mark SMS as verified if setup verification
        const mfaData = this.getUserMFA(verification.userId);
        if (mfaData.sms && !mfaData.sms.verified) {
          mfaData.sms.verified = true;
          mfaData.sms.verifiedAt = Date.now();
        }

        this.pendingVerifications.delete(verificationId);
        this.resetAttemptCount(verification.userId);
        this.metrics.smsVerifications++;
        this.metrics.successfulVerifications++;

        this.emit('smsVerified', { 
          userId: verification.userId, 
          phoneNumber: verification.phoneNumber,
          timestamp: Date.now() 
        });

        return {
          verified: true,
          method: 'sms',
          timestamp: Date.now()
        };
      } else {
        this.incrementAttemptCount(verification.userId);
        this.metrics.failedAttempts++;
        
        this.emit('smsFailed', { 
          userId: verification.userId, 
          timestamp: Date.now() 
        });
        
        throw new Error('Invalid SMS verification code');
      }
    } catch (error) {
      console.error('SMS verification error:', error);
      throw error;
    }
  }

  /**
   * Setup email MFA for a user
   * @param {string} userId - User identifier
   * @param {string} email - User's email address
   */
  async setupEmail(userId, email) {
    if (!this.options.emailProvider) {
      throw new Error('Email provider not configured');
    }

    try {
      const mfaData = this.getUserMFA(userId);
      mfaData.email = {
        email: email,
        verified: false,
        setupAt: Date.now()
      };
      this.userMFA.set(userId, mfaData);

      // Send verification email
      const verificationCode = this.generateVerificationCode();
      const verificationId = await this.sendEmailVerification(email, verificationCode);

      this.pendingVerifications.set(verificationId, {
        userId,
        method: 'email',
        code: verificationCode,
        email,
        createdAt: Date.now(),
        expiresAt: Date.now() + 900000 // 15 minutes
      });

      this.emit('emailSetup', { userId, email, verificationId });

      return { verificationId };
    } catch (error) {
      console.error('Email setup error:', error);
      throw new Error(`Email setup failed: ${error.message}`);
    }
  }

  /**
   * Use backup code for verification
   * @param {string} userId - User identifier
   * @param {string} backupCode - Backup code
   */
  async useBackupCode(userId, backupCode) {
    const mfaData = this.getUserMFA(userId);
    
    if (!mfaData.totp || !mfaData.totp.backupCodes) {
      throw new Error('No backup codes available for user');
    }

    if (!this.checkAttemptLimits(userId)) {
      throw new Error('Too many failed attempts. Account temporarily locked.');
    }

    try {
      const codeIndex = mfaData.totp.backupCodes.findIndex(code => 
        code.code === backupCode && !code.used
      );

      if (codeIndex === -1) {
        this.incrementAttemptCount(userId);
        this.metrics.failedAttempts++;
        throw new Error('Invalid or already used backup code');
      }

      // Mark backup code as used
      mfaData.totp.backupCodes[codeIndex].used = true;
      mfaData.totp.backupCodes[codeIndex].usedAt = Date.now();

      this.resetAttemptCount(userId);
      this.metrics.backupCodeUsages++;
      this.metrics.successfulVerifications++;

      this.emit('backupCodeUsed', { 
        userId, 
        codeIndex, 
        remainingCodes: mfaData.totp.backupCodes.filter(c => !c.used).length,
        timestamp: Date.now() 
      });

      return {
        verified: true,
        method: 'backup-code',
        remainingCodes: mfaData.totp.backupCodes.filter(c => !c.used).length,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Backup code verification error:', error);
      throw error;
    }
  }

  /**
   * Generate new backup codes
   * @param {string} userId - User identifier
   */
  generateNewBackupCodes(userId) {
    const mfaData = this.getUserMFA(userId);
    
    if (!mfaData.totp) {
      throw new Error('TOTP not setup for user');
    }

    const newBackupCodes = this.generateBackupCodes();
    mfaData.totp.backupCodes = newBackupCodes;
    mfaData.totp.backupCodesGeneratedAt = Date.now();

    this.emit('backupCodesGenerated', { userId, timestamp: Date.now() });

    return newBackupCodes;
  }

  /**
   * Create MFA session after successful verification
   * @param {string} userId - User identifier
   * @param {string} method - Verification method used
   */
  createMFASession(userId, method) {
    const sessionId = this.generateSessionId();
    const session = {
      userId,
      method,
      createdAt: Date.now(),
      expiresAt: Date.now() + this.options.sessionTimeout,
      ipAddress: null, // Would be set by middleware
      userAgent: null  // Would be set by middleware
    };

    this.activeSessions.set(sessionId, session);

    this.emit('mfaSessionCreated', { userId, sessionId, method });

    return sessionId;
  }

  /**
   * Verify MFA session
   * @param {string} sessionId - Session identifier
   */
  verifyMFASession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    
    if (!session) {
      return { valid: false, reason: 'Session not found' };
    }

    if (Date.now() > session.expiresAt) {
      this.activeSessions.delete(sessionId);
      return { valid: false, reason: 'Session expired' };
    }

    return { 
      valid: true, 
      userId: session.userId, 
      method: session.method,
      createdAt: session.createdAt 
    };
  }

  /**
   * Revoke MFA session
   * @param {string} sessionId - Session identifier
   */
  revokeMFASession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      this.activeSessions.delete(sessionId);
      this.emit('mfaSessionRevoked', { sessionId, userId: session.userId });
      return true;
    }
    return false;
  }

  /**
   * Get user's MFA status
   * @param {string} userId - User identifier
   */
  getUserMFAStatus(userId) {
    const mfaData = this.getUserMFA(userId);
    
    return {
      totpEnabled: !!(mfaData.totp && mfaData.totp.verified),
      smsEnabled: !!(mfaData.sms && mfaData.sms.verified),
      emailEnabled: !!(mfaData.email && mfaData.email.verified),
      hardwareTokenEnabled: !!(mfaData.hardwareToken && mfaData.hardwareToken.verified),
      backupCodesAvailable: mfaData.totp ? 
        mfaData.totp.backupCodes.filter(c => !c.used).length : 0,
      setupAt: Math.min(
        mfaData.totp?.setupAt || Infinity,
        mfaData.sms?.setupAt || Infinity,
        mfaData.email?.setupAt || Infinity
      )
    };
  }

  getUserMFA(userId) {
    if (!this.userMFA.has(userId)) {
      this.userMFA.set(userId, {});
    }
    return this.userMFA.get(userId);
  }

  generateBackupCodes() {
    const codes = [];
    for (let i = 0; i < this.options.backupCodeCount; i++) {
      codes.push({
        code: crypto.randomBytes(this.options.backupCodeLength / 2).toString('hex'),
        used: false,
        generatedAt: Date.now()
      });
    }
    return codes;
  }

  generateVerificationCode() {
    return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
  }

  generateSessionId() {
    return `mfa_${Date.now()}_${crypto.randomBytes(16).toString('hex')}`;
  }

  async sendSMSVerification(phoneNumber, code) {
    // Placeholder for SMS provider integration
    console.log(`📱 SMS to ${phoneNumber}: Your verification code is ${code}`);
    return `sms_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  async sendEmailVerification(email, code) {
    // Placeholder for email provider integration
    console.log(`📧 Email to ${email}: Your verification code is ${code}`);
    return `email_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  checkAttemptLimits(userId) {
    const attempts = this.attemptCounts.get(userId);
    if (!attempts) return true;

    if (attempts.count >= this.options.maxAttempts) {
      if (Date.now() < attempts.lockedUntil) {
        return false;
      } else {
        // Lockout period expired, reset
        this.attemptCounts.delete(userId);
        return true;
      }
    }

    return true;
  }

  incrementAttemptCount(userId) {
    const attempts = this.attemptCounts.get(userId) || { count: 0, firstAttempt: Date.now() };
    attempts.count++;
    
    if (attempts.count >= this.options.maxAttempts) {
      attempts.lockedUntil = Date.now() + this.options.lockoutDuration;
      this.emit('userLocked', { userId, lockedUntil: attempts.lockedUntil });
    }
    
    this.attemptCounts.set(userId, attempts);
  }

  resetAttemptCount(userId) {
    this.attemptCounts.delete(userId);
  }

  startCleanupTimer() {
    setInterval(() => {
      this.cleanupExpiredData();
    }, 60000); // Run every minute
  }

  cleanupExpiredData() {
    const now = Date.now();
    
    // Clean up expired verifications
    for (const [id, verification] of this.pendingVerifications) {
      if (now > verification.expiresAt) {
        this.pendingVerifications.delete(id);
      }
    }
    
    // Clean up expired sessions
    for (const [id, session] of this.activeSessions) {
      if (now > session.expiresAt) {
        this.activeSessions.delete(id);
      }
    }
    
    // Clean up expired lockouts
    for (const [userId, attempts] of this.attemptCounts) {
      if (attempts.lockedUntil && now > attempts.lockedUntil) {
        this.attemptCounts.delete(userId);
      }
    }
  }

  getMetrics() {
    return {
      ...this.metrics,
      totalUsers: this.userMFA.size,
      activeSessions: this.activeSessions.size,
      pendingVerifications: this.pendingVerifications.size,
      lockedUsers: Array.from(this.attemptCounts.values())
        .filter(a => a.lockedUntil && Date.now() < a.lockedUntil).length
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down MFA Service...');
    
    // Clear sensitive data
    this.userMFA.clear();
    this.pendingVerifications.clear();
    this.activeSessions.clear();
    this.attemptCounts.clear();
    
    console.log('✅ MFA Service shutdown complete');
  }
}

export default MFAService;
