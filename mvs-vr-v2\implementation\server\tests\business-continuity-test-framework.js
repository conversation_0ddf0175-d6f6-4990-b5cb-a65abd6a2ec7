/**
 * Business Continuity Test Framework
 * 
 * Comprehensive testing framework for business continuity integration
 * including service monitoring, recovery prioritization, and dashboard functionality.
 */

const { BusinessServiceMonitor, HEALTH_STATES } = require('../services/continuity/business-service-monitor');
const { BusinessRecoveryPrioritizer, PRIORITY_LEVELS } = require('../services/continuity/business-recovery-prioritizer');
const { BusinessContinuityDashboard, WIDGET_TYPES } = require('../services/continuity/business-continuity-dashboard');

const logger = require('../utils/logger').getLogger('business-continuity-test');

// Test data generators
class TestDataGenerator {
  /**
   * Generate test service configuration
   * @param {number} serviceCount - Number of services to generate
   * @returns {Object} Service configuration
   */
  static generateServiceConfig(serviceCount = 10) {
    const services = [];
    const serviceTypes = ['core', 'supporting', 'external', 'infrastructure'];
    
    for (let i = 1; i <= serviceCount; i++) {
      const service = {
        id: `service-${i}`,
        name: `Test Service ${i}`,
        description: `Test service ${i} for business continuity testing`,
        type: serviceTypes[Math.floor(Math.random() * serviceTypes.length)],
        priority: Math.floor(Math.random() * 5) + 1,
        healthEndpoint: `http://localhost:3000/health/service-${i}`,
        dependencies: this.generateDependencies(i, serviceCount),
        businessImpact: {
          revenue: Math.floor(Math.random() * 5) + 1,
          customer: Math.floor(Math.random() * 5) + 1,
          operational: Math.floor(Math.random() * 5) + 1,
          compliance: Math.floor(Math.random() * 5) + 1,
          reputation: Math.floor(Math.random() * 5) + 1
        },
        sla: {
          availability: 99.9,
          responseTime: 200,
          criticalRecoveryTime: 60,
          unhealthyRecoveryTime: 30,
          degradedRecoveryTime: 15
        },
        rto: Math.floor(Math.random() * 240) + 60, // 1-4 hours
        rpo: Math.floor(Math.random() * 60) + 15,  // 15-75 minutes
        recoveryProcedures: [
          'Check service logs',
          'Restart service',
          'Verify dependencies',
          'Run health checks'
        ],
        resources: {
          personnel: Math.floor(Math.random() * 3) + 1,
          infrastructure: 'standard',
          budget: 'medium',
          expertise: 'standard',
          tools: ['monitoring', 'logging', 'alerting']
        }
      };
      
      services.push(service);
    }
    
    return { services };
  }
  
  /**
   * Generate service dependencies
   * @param {number} serviceIndex - Current service index
   * @param {number} totalServices - Total number of services
   * @returns {Array<string>} Dependencies
   */
  static generateDependencies(serviceIndex, totalServices) {
    const dependencies = [];
    const maxDependencies = Math.min(3, serviceIndex - 1);
    const dependencyCount = Math.floor(Math.random() * maxDependencies);
    
    for (let i = 0; i < dependencyCount; i++) {
      const depIndex = Math.floor(Math.random() * (serviceIndex - 1)) + 1;
      const depId = `service-${depIndex}`;
      
      if (!dependencies.includes(depId)) {
        dependencies.push(depId);
      }
    }
    
    return dependencies;
  }
  
  /**
   * Generate mock health responses
   * @param {Array<Object>} services - Services to generate responses for
   * @returns {Map} Health responses
   */
  static generateHealthResponses(services) {
    const responses = new Map();
    const healthStates = Object.values(HEALTH_STATES);
    
    for (const service of services) {
      const randomState = healthStates[Math.floor(Math.random() * healthStates.length)];
      
      responses.set(service.id, {
        status: randomState,
        timestamp: new Date().toISOString(),
        responseTime: Math.floor(Math.random() * 500) + 50,
        details: {
          version: '1.0.0',
          uptime: Math.floor(Math.random() * 86400),
          memory: Math.floor(Math.random() * 1000) + 500
        }
      });
    }
    
    return responses;
  }
}

/**
 * Test suite for business continuity integration
 */
class BusinessContinuityTestSuite {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      tests: []
    };
    
    // Test services
    this.serviceMonitor = null;
    this.recoveryPrioritizer = null;
    this.dashboard = null;
    
    // Test data
    this.testConfig = null;
    this.mockHealthResponses = null;
  }
  
  /**
   * Run all business continuity tests
   * @returns {Promise<Object>} Test results
   */
  async runAllTests() {
    logger.info('Starting business continuity test suite');
    
    // Setup test environment
    await this.setupTestEnvironment();
    
    // Test service monitoring
    await this.testServiceMonitoring();
    
    // Test recovery prioritization
    await this.testRecoveryPrioritization();
    
    // Test dashboard functionality
    await this.testDashboardFunctionality();
    
    // Test integration scenarios
    await this.testIntegrationScenarios();
    
    // Test performance
    await this.testPerformance();
    
    // Cleanup
    await this.cleanup();
    
    logger.info(`Test suite completed. Passed: ${this.results.passed}/${this.results.total}`);
    
    return this.results;
  }
  
  /**
   * Setup test environment
   */
  async setupTestEnvironment() {
    // Generate test configuration
    this.testConfig = TestDataGenerator.generateServiceConfig(15);
    this.mockHealthResponses = TestDataGenerator.generateHealthResponses(this.testConfig.services);
    
    // Create temporary config files
    const fs = require('fs');
    const path = require('path');
    const { promisify } = require('util');
    const writeFileAsync = promisify(fs.writeFile);
    const mkdirAsync = promisify(fs.mkdir);
    
    const tempDir = path.join(__dirname, '../temp/test-config');
    await mkdirAsync(tempDir, { recursive: true });
    
    const configPath = path.join(tempDir, 'business-services.json');
    await writeFileAsync(configPath, JSON.stringify(this.testConfig, null, 2));
    
    const recoveryConfigPath = path.join(tempDir, 'recovery-priorities.json');
    await writeFileAsync(recoveryConfigPath, JSON.stringify(this.testConfig, null, 2));
    
    // Initialize services with test configuration
    this.serviceMonitor = new BusinessServiceMonitor({
      configPath,
      outputPath: path.join(tempDir, 'monitoring'),
      checkInterval: 5000 // 5 seconds for testing
    });
    
    this.recoveryPrioritizer = new BusinessRecoveryPrioritizer({
      configPath: recoveryConfigPath,
      outputPath: path.join(tempDir, 'recovery')
    });
    
    this.dashboard = new BusinessContinuityDashboard({
      outputPath: path.join(tempDir, 'dashboard'),
      reportPath: path.join(tempDir, 'reports'),
      refreshInterval: 10000 // 10 seconds for testing
    });
    
    // Set up service references
    this.dashboard.setServices({
      serviceMonitor: this.serviceMonitor,
      recoveryPrioritizer: this.recoveryPrioritizer,
      alertManager: null // Mock alert manager if needed
    });
    
    logger.debug('Test environment setup completed');
  }
  
  /**
   * Test service monitoring functionality
   */
  async testServiceMonitoring() {
    await this.runTest('Service Monitor Initialization', async () => {
      this.assert(this.serviceMonitor !== null, 'Service monitor should be initialized');
      
      const stats = this.serviceMonitor.getStatistics();
      this.assert(stats.totalServices === 15, 'Should load 15 test services');
    });
    
    await this.runTest('Service Health Checking', async () => {
      // Mock health check responses
      const originalCheckServiceHealth = this.serviceMonitor.checkServiceHealth;
      this.serviceMonitor.checkServiceHealth = async (service) => {
        const mockResponse = this.mockHealthResponses.get(service.id);
        return {
          state: mockResponse.status,
          lastCheck: new Date().toISOString(),
          responseTime: mockResponse.responseTime,
          errorCount: 0,
          uptime: 95,
          details: mockResponse.details
        };
      };
      
      // Run health check
      await this.serviceMonitor.checkAllServices();
      
      const healthData = this.serviceMonitor.getServiceHealth();
      this.assert(Object.keys(healthData).length === 15, 'Should have health data for all services');
      
      // Restore original method
      this.serviceMonitor.checkServiceHealth = originalCheckServiceHealth;
    });
    
    await this.runTest('Dependency Graph Building', async () => {
      const dependencyGraph = this.serviceMonitor.getDependencyGraph();
      
      this.assert(dependencyGraph.nodes.length === 15, 'Should have 15 nodes in dependency graph');
      this.assert(dependencyGraph.edges.length > 0, 'Should have dependency edges');
    });
    
    await this.runTest('Business Metrics Calculation', async () => {
      const metrics = this.serviceMonitor.getBusinessMetrics();
      
      this.assert(metrics.system_health_score !== undefined, 'Should calculate system health score');
      this.assert(metrics.service_availability !== undefined, 'Should calculate service availability');
    });
  }
  
  /**
   * Test recovery prioritization functionality
   */
  async testRecoveryPrioritization() {
    await this.runTest('Recovery Prioritizer Initialization', async () => {
      this.assert(this.recoveryPrioritizer !== null, 'Recovery prioritizer should be initialized');
      
      const stats = this.recoveryPrioritizer.getStatistics();
      this.assert(stats.totalServices === 15, 'Should load 15 services for prioritization');
    });
    
    await this.runTest('Priority Calculation', async () => {
      const priorities = this.recoveryPrioritizer.getRecoveryPriorities();
      
      this.assert(priorities.length === 15, 'Should calculate priorities for all services');
      this.assert(priorities[0].priority.level <= priorities[priorities.length - 1].priority.level, 
                 'Services should be sorted by priority');
    });
    
    await this.runTest('Recovery Plan Generation', async () => {
      const affectedServices = ['service-1', 'service-2', 'service-3', 'service-5'];
      const plan = this.recoveryPrioritizer.generateRecoveryPlan(affectedServices);
      
      this.assert(plan.id !== undefined, 'Recovery plan should have an ID');
      this.assert(plan.phases.length > 0, 'Recovery plan should have phases');
      this.assert(plan.totalEstimatedTime > 0, 'Recovery plan should have estimated time');
      this.assert(plan.affectedServices === affectedServices.length, 'Should track affected services count');
    });
    
    await this.runTest('Business Impact Assessment', async () => {
      const priorities = this.recoveryPrioritizer.getRecoveryPriorities();
      const criticalServices = priorities.filter(p => p.priority.level === PRIORITY_LEVELS.CRITICAL);
      
      this.assert(criticalServices.length > 0, 'Should identify critical services');
      
      for (const service of criticalServices) {
        this.assert(service.priority.businessImpact.score > 0, 'Critical services should have business impact score');
      }
    });
  }
  
  /**
   * Test dashboard functionality
   */
  async testDashboardFunctionality() {
    await this.runTest('Dashboard Initialization', async () => {
      this.assert(this.dashboard !== null, 'Dashboard should be initialized');
      
      const stats = this.dashboard.getStatistics();
      this.assert(stats.totalWidgets > 0, 'Dashboard should have widgets');
    });
    
    await this.runTest('Widget Data Generation', async () => {
      // Trigger dashboard refresh
      await this.dashboard.refreshDashboard();
      
      const dashboardData = this.dashboard.getDashboardData();
      
      this.assert(dashboardData[WIDGET_TYPES.STATUS_OVERVIEW] !== undefined, 
                 'Should generate status overview data');
      this.assert(dashboardData[WIDGET_TYPES.SERVICE_HEALTH] !== undefined, 
                 'Should generate service health data');
      this.assert(dashboardData[WIDGET_TYPES.BUSINESS_METRICS] !== undefined, 
                 'Should generate business metrics data');
    });
    
    await this.runTest('KPI Calculation', async () => {
      const kpis = this.dashboard.getKPIs();
      
      this.assert(kpis.system_availability !== undefined, 'Should calculate system availability KPI');
      this.assert(kpis.mttr !== undefined, 'Should calculate MTTR KPI');
      this.assert(kpis.service_health_score !== undefined, 'Should calculate service health score KPI');
    });
    
    await this.runTest('Report Generation', async () => {
      const executiveReport = await this.dashboard.generateReport('executive');
      
      this.assert(executiveReport.id !== undefined, 'Report should have an ID');
      this.assert(executiveReport.type === 'executive', 'Report should have correct type');
      this.assert(executiveReport.data !== undefined, 'Report should have data');
      this.assert(executiveReport.summary !== undefined, 'Report should have summary');
      
      const operationalReport = await this.dashboard.generateReport('operational');
      this.assert(operationalReport.type === 'operational', 'Should generate operational report');
    });
  }
  
  /**
   * Test integration scenarios
   */
  async testIntegrationScenarios() {
    await this.runTest('Service Outage Scenario', async () => {
      // Simulate service outage
      const affectedServices = ['service-1', 'service-3'];
      
      // Generate recovery plan
      const plan = this.recoveryPrioritizer.generateRecoveryPlan(affectedServices);
      
      // Verify dashboard reflects the outage
      await this.dashboard.refreshDashboard();
      const statusData = this.dashboard.getDashboardData(WIDGET_TYPES.STATUS_OVERVIEW);
      
      this.assert(plan.phases.length > 0, 'Should generate recovery phases for outage');
      this.assert(statusData !== undefined, 'Dashboard should update with outage information');
    });
    
    await this.runTest('Cascading Failure Scenario', async () => {
      // Simulate cascading failure starting with a core service
      const coreServices = this.testConfig.services.filter(s => s.type === 'core');
      
      if (coreServices.length > 0) {
        const plan = this.recoveryPrioritizer.generateRecoveryPlan([coreServices[0].id]);
        
        this.assert(plan.phases.length > 0, 'Should handle cascading failure recovery');
        this.assert(plan.totalEstimatedTime > 0, 'Should estimate recovery time for cascading failure');
      }
    });
    
    await this.runTest('Business Impact Correlation', async () => {
      // Test correlation between service health and business metrics
      const healthData = this.serviceMonitor.getServiceHealth();
      const businessMetrics = this.serviceMonitor.getBusinessMetrics();
      
      this.assert(Object.keys(healthData).length > 0, 'Should have service health data');
      this.assert(Object.keys(businessMetrics).length > 0, 'Should have business metrics');
      
      // Verify business metrics reflect service health
      const systemHealthScore = businessMetrics.system_health_score?.value || 0;
      this.assert(systemHealthScore >= 0 && systemHealthScore <= 100, 
                 'System health score should be valid percentage');
    });
  }
  
  /**
   * Test performance
   */
  async testPerformance() {
    await this.runTest('Large Scale Service Monitoring', async () => {
      // Generate larger test configuration
      const largeConfig = TestDataGenerator.generateServiceConfig(100);
      const largeResponses = TestDataGenerator.generateHealthResponses(largeConfig.services);
      
      // Create temporary monitor for large scale test
      const fs = require('fs');
      const path = require('path');
      const { promisify } = require('util');
      const writeFileAsync = promisify(fs.writeFile);
      
      const tempConfigPath = path.join(__dirname, '../temp/test-config/large-services.json');
      await writeFileAsync(tempConfigPath, JSON.stringify(largeConfig, null, 2));
      
      const largeMonitor = new BusinessServiceMonitor({
        configPath: tempConfigPath,
        outputPath: path.join(__dirname, '../temp/test-config/large-monitoring'),
        checkInterval: 60000
      });
      
      // Mock health checks for performance
      largeMonitor.checkServiceHealth = async (service) => {
        const mockResponse = largeResponses.get(service.id);
        return {
          state: mockResponse.status,
          lastCheck: new Date().toISOString(),
          responseTime: mockResponse.responseTime,
          errorCount: 0,
          uptime: 95,
          details: mockResponse.details
        };
      };
      
      const startTime = Date.now();
      await largeMonitor.checkAllServices();
      const endTime = Date.now();
      
      const processingTime = endTime - startTime;
      
      this.assert(processingTime < 10000, 'Should process 100 services in less than 10 seconds');
      
      const stats = largeMonitor.getStatistics();
      this.assert(stats.totalServices === 100, 'Should handle 100 services');
    });
    
    await this.runTest('Recovery Plan Generation Performance', async () => {
      // Test recovery plan generation with many affected services
      const allServiceIds = this.testConfig.services.map(s => s.id);
      
      const startTime = Date.now();
      const plan = this.recoveryPrioritizer.generateRecoveryPlan(allServiceIds);
      const endTime = Date.now();
      
      const processingTime = endTime - startTime;
      
      this.assert(processingTime < 5000, 'Should generate recovery plan in less than 5 seconds');
      this.assert(plan.phases.length > 0, 'Should generate valid recovery plan');
    });
  }
  
  /**
   * Run individual test
   * @param {string} name - Test name
   * @param {Function} testFn - Test function
   */
  async runTest(name, testFn) {
    this.results.total++;
    
    try {
      await testFn();
      this.results.passed++;
      this.results.tests.push({
        name,
        status: 'PASSED',
        timestamp: new Date().toISOString()
      });
      logger.info(`✓ ${name}`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({
        name,
        status: 'FAILED',
        error: error.message,
        timestamp: new Date().toISOString()
      });
      logger.error(`✗ ${name}: ${error.message}`);
    }
  }
  
  /**
   * Assert condition
   * @param {boolean} condition - Condition to check
   * @param {string} message - Error message
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }
  
  /**
   * Cleanup test resources
   */
  async cleanup() {
    try {
      // Stop monitoring
      if (this.serviceMonitor) {
        this.serviceMonitor.stopMonitoring();
      }
      
      // Clean up temporary files
      const fs = require('fs');
      const path = require('path');
      const { promisify } = require('util');
      const rimraf = require('rimraf');
      
      const tempDir = path.join(__dirname, '../temp');
      if (fs.existsSync(tempDir)) {
        rimraf.sync(tempDir);
      }
      
      logger.debug('Test cleanup completed');
    } catch (error) {
      logger.error('Error during test cleanup:', error);
    }
  }
}

/**
 * Run business continuity tests
 * @returns {Promise<Object>} Test results
 */
async function runBusinessContinuityTests() {
  const testSuite = new BusinessContinuityTestSuite();
  
  try {
    const results = await testSuite.runAllTests();
    return results;
  } catch (error) {
    logger.error('Test suite error:', error);
    await testSuite.cleanup();
    throw error;
  }
}

// If script is run directly
if (require.main === module) {
  runBusinessContinuityTests()
    .then(results => {
      console.log('\n=== Business Continuity Test Results ===');
      console.log(`Total Tests: ${results.total}`);
      console.log(`Passed: ${results.passed}`);
      console.log(`Failed: ${results.failed}`);
      console.log(`Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
      
      if (results.failed > 0) {
        console.log('\nFailed Tests:');
        results.tests
          .filter(test => test.status === 'FAILED')
          .forEach(test => {
            console.log(`- ${test.name}: ${test.error}`);
          });
      }
      
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('Test framework error:', error);
      process.exit(1);
    });
}

module.exports = {
  BusinessContinuityTestSuite,
  TestDataGenerator,
  runBusinessContinuityTests
};
