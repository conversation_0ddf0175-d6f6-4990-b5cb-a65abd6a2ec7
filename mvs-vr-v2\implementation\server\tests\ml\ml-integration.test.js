/**
 * Machine Learning Integration Tests
 * Comprehensive tests for ML services including analytics, predictions, and recommendations
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MLAnalyticsEngine } from '../../services/ml/analytics-engine.js';
import { PredictiveModels } from '../../services/ml/predictive-models.js';
import { RecommendationEngine } from '../../services/ml/recommendation-engine.js';
import { MLPipeline } from '../../services/ml/ml-pipeline.js';

// Mock TensorFlow to avoid GPU/CPU issues in tests
vi.mock('@tensorflow/tfjs-node', () => ({
  setBackend: vi.fn().mockResolvedValue(undefined),
  ready: vi.fn().mockResolvedValue(undefined),
  sequential: vi.fn(() => ({
    add: vi.fn(),
    compile: vi.fn(),
    fit: vi.fn().mockResolvedValue({
      history: {
        loss: [0.5, 0.3, 0.2],
        val_loss: [0.6, 0.4, 0.3],
        accuracy: [0.7, 0.8, 0.85]
      }
    }),
    predict: vi.fn(() => ({
      data: vi.fn().mockResolvedValue([0.8]),
      dispose: vi.fn()
    })),
    evaluate: vi.fn(() => ({
      data: vi.fn().mockResolvedValue([0.2, 0.85])
    })),
    save: vi.fn().mockResolvedValue(undefined),
    dispose: vi.fn()
  })),
  layers: {
    dense: vi.fn(() => ({})),
    lstm: vi.fn(() => ({})),
    dropout: vi.fn(() => ({})),
    batchNormalization: vi.fn(() => ({})),
    embedding: vi.fn(() => ({})),
    flatten: vi.fn(() => ({})),
    dot: vi.fn(() => ({})),
    multiply: vi.fn(() => ({})),
    concatenate: vi.fn(() => ({})),
    add: vi.fn(() => ({})),
    activation: vi.fn(() => ({}))
  },
  input: vi.fn(() => ({})),
  model: vi.fn(() => ({
    compile: vi.fn(),
    fit: vi.fn(),
    predict: vi.fn(),
    dispose: vi.fn()
  })),
  train: {
    adam: vi.fn(() => ({}))
  },
  regularizers: {
    l2: vi.fn(() => ({}))
  },
  callbacks: {
    earlyStopping: vi.fn(() => ({})),
    reduceLROnPlateau: vi.fn(() => ({}))
  },
  tensor: vi.fn(() => ({
    data: vi.fn().mockResolvedValue([1, 2, 3]),
    dispose: vi.fn()
  })),
  tensor2d: vi.fn(() => ({
    data: vi.fn().mockResolvedValue([1, 2, 3]),
    dispose: vi.fn(),
    argMax: vi.fn(() => ({
      data: vi.fn().mockResolvedValue([0])
    }))
  })),
  tensor3d: vi.fn(() => ({
    data: vi.fn().mockResolvedValue([1, 2, 3]),
    dispose: vi.fn()
  })),
  zeros: vi.fn(() => ({
    dispose: vi.fn()
  })),
  scalar: vi.fn(() => ({
    dispose: vi.fn()
  })),
  loadLayersModel: vi.fn().mockResolvedValue({
    predict: vi.fn(),
    dispose: vi.fn()
  })
}));

describe('Machine Learning Integration Tests', () => {
  describe('ML Analytics Engine', () => {
    let analyticsEngine;

    beforeEach(async () => {
      analyticsEngine = new MLAnalyticsEngine({
        enableUserBehaviorAnalytics: true,
        enablePerformanceAnalytics: true,
        enableAnomalyDetection: true,
        enablePredictiveAnalytics: true
      });

      await new Promise(resolve => {
        if (analyticsEngine.listenerCount('ready') > 0) {
          analyticsEngine.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (analyticsEngine) {
        await analyticsEngine.shutdown();
      }
    });

    it('should initialize with ML models', () => {
      expect(analyticsEngine).toBeDefined();
      expect(analyticsEngine.options.enableUserBehaviorAnalytics).toBe(true);
      expect(analyticsEngine.models.userBehavior).toBeDefined();
      expect(analyticsEngine.models.churnPrediction).toBeDefined();
      expect(analyticsEngine.models.anomalyDetection).toBeDefined();
    });

    it('should analyze user behavior', async () => {
      const userData = {
        userId: 'user123',
        sessionDuration: 1800,
        pageViews: 15,
        clickCount: 45,
        scrollDepth: 0.8,
        timeOnPage: 120,
        bounceRate: 0.2,
        returnVisitor: true,
        deviceType: 'desktop',
        hourOfDay: 14,
        dayOfWeek: 3,
        conversionEvents: 2,
        errorCount: 0,
        loadTime: 2.5
      };

      const result = await analyticsEngine.analyzeUserBehavior(userData);

      expect(result).toBeDefined();
      expect(result.userId).toBe('user123');
      expect(result.behaviorCluster).toBeDefined();
      expect(result.insights).toBeDefined();
      expect(result.confidence).toBeDefined();
    });

    it('should predict user churn', async () => {
      // First analyze user behavior to create data
      const userData = {
        userId: 'user456',
        sessionDuration: 300,
        pageViews: 2,
        clickCount: 5,
        scrollDepth: 0.3,
        timeOnPage: 30,
        bounceRate: 0.8,
        returnVisitor: false,
        deviceType: 'mobile',
        hourOfDay: 23,
        dayOfWeek: 6,
        conversionEvents: 0,
        errorCount: 2,
        loadTime: 8.0
      };

      await analyticsEngine.analyzeUserBehavior(userData);
      const churnResult = await analyticsEngine.predictChurn('user456');

      expect(churnResult).toBeDefined();
      expect(churnResult.userId).toBe('user456');
      expect(churnResult.churnProbability).toBeDefined();
      expect(churnResult.riskLevel).toBeDefined();
      expect(churnResult.recommendations).toBeDefined();
      expect(Array.isArray(churnResult.recommendations)).toBe(true);
    });

    it('should detect anomalies', async () => {
      const testData = [
        { value: 100, timestamp: Date.now() },
        { value: 105, timestamp: Date.now() + 1000 },
        { value: 98, timestamp: Date.now() + 2000 },
        { value: 500, timestamp: Date.now() + 3000 } // Anomaly
      ];

      const result = await analyticsEngine.detectAnomalies(testData);

      expect(result).toBeDefined();
      expect(typeof result.isAnomaly).toBe('boolean');
      expect(result.reconstructionError).toBeDefined();
      expect(result.severity).toBeDefined();
      expect(result.timestamp).toBeDefined();
    });

    it('should forecast performance', async () => {
      const historicalData = Array.from({ length: 24 }, (_, i) => ({
        timestamp: Date.now() - (24 - i) * 3600000,
        cpuUsage: 50 + Math.random() * 20,
        memoryUsage: 60 + Math.random() * 15,
        responseTime: 100 + Math.random() * 50
      }));

      const forecast = await analyticsEngine.forecastPerformance(historicalData, 6);

      expect(forecast).toBeDefined();
      expect(Array.isArray(forecast.predictions)).toBe(true);
      expect(forecast.confidence).toBeDefined();
      expect(forecast.trend).toBeDefined();
      expect(forecast.timestamp).toBeDefined();
    });

    it('should provide service metrics', () => {
      const metrics = analyticsEngine.getMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.userBehaviorData).toBe('number');
      expect(typeof metrics.performanceData).toBe('number');
      expect(typeof metrics.insights).toBe('number');
      expect(typeof metrics.predictions).toBe('number');
      expect(typeof metrics.modelsLoaded).toBe('number');
      expect(metrics.trainingState).toBeDefined();
    });
  });

  describe('Predictive Models', () => {
    let predictiveModels;

    beforeEach(async () => {
      predictiveModels = new PredictiveModels({
        enableDemandForecasting: true,
        enableUserChurnPrediction: true,
        enablePerformancePrediction: true,
        enableRevenueForecasting: true,
        forecastHorizon: 7
      });

      await new Promise(resolve => {
        if (predictiveModels.listenerCount('ready') > 0) {
          predictiveModels.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (predictiveModels) {
        await predictiveModels.shutdown();
      }
    });

    it('should initialize predictive models', () => {
      expect(predictiveModels).toBeDefined();
      expect(predictiveModels.models.demandForecasting.lstm).toBeDefined();
      expect(predictiveModels.models.churnPrediction.neuralNetwork).toBeDefined();
      expect(predictiveModels.models.performancePrediction.lstm).toBeDefined();
      expect(predictiveModels.models.revenueForecasting.lstm).toBeDefined();
    });

    it('should forecast demand', async () => {
      const historicalData = Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (30 - i) * 86400000),
        demand: 1000 + Math.random() * 200,
        seasonality: Math.sin((i / 7) * Math.PI) * 100,
        trend: i * 5
      }));

      const forecast = await predictiveModels.forecastDemand(historicalData, 7);

      expect(forecast).toBeDefined();
      expect(Array.isArray(forecast.predictions)).toBe(true);
      expect(forecast.confidence).toBeDefined();
      expect(forecast.seasonality).toBeDefined();
      expect(forecast.trend).toBeDefined();
      expect(forecast.metadata).toBeDefined();
      expect(forecast.metadata.horizon).toBe(7);
    });

    it('should predict user churn', async () => {
      const userFeatures = {
        userId: 'user789',
        accountAge: 365,
        totalSessions: 150,
        avgSessionDuration: 1200,
        lastLoginDays: 7,
        supportTickets: 2,
        featureUsage: 0.6,
        paymentHistory: 'regular',
        engagementScore: 0.4,
        satisfactionScore: 3.2
      };

      const prediction = await predictiveModels.predictUserChurn(userFeatures);

      expect(prediction).toBeDefined();
      expect(prediction.userId).toBe('user789');
      expect(prediction.churnProbability).toBeDefined();
      expect(prediction.riskLevel).toBeDefined();
      expect(prediction.confidence).toBeDefined();
      expect(Array.isArray(prediction.interventions)).toBe(true);
      expect(Array.isArray(prediction.factors)).toBe(true);
    });

    it('should predict performance metrics', async () => {
      const historicalMetrics = Array.from({ length: 24 }, (_, i) => ({
        timestamp: Date.now() - (24 - i) * 3600000,
        cpuUsage: 45 + Math.random() * 30,
        memoryUsage: 55 + Math.random() * 25,
        responseTime: 80 + Math.random() * 40,
        throughput: 800 + Math.random() * 200,
        errorRate: Math.random() * 0.05
      }));

      const prediction = await predictiveModels.predictPerformance(historicalMetrics, 6);

      expect(prediction).toBeDefined();
      expect(Array.isArray(prediction.predictions)).toBe(true);
      expect(Array.isArray(prediction.anomalies)).toBe(true);
      expect(Array.isArray(prediction.alerts)).toBe(true);
      expect(prediction.confidence).toBeDefined();
      expect(prediction.metadata).toBeDefined();
    });

    it('should forecast revenue', async () => {
      const historicalRevenue = Array.from({ length: 90 }, (_, i) => ({
        date: new Date(Date.now() - (90 - i) * 86400000),
        revenue: 10000 + Math.random() * 5000,
        orders: 100 + Math.random() * 50,
        customers: 80 + Math.random() * 40,
        avgOrderValue: 100 + Math.random() * 50
      }));

      const forecast = await predictiveModels.forecastRevenue(historicalRevenue, 7);

      expect(forecast).toBeDefined();
      expect(Array.isArray(forecast.predictions)).toBe(true);
      expect(forecast.insights).toBeDefined();
      expect(forecast.confidence).toBeDefined();
      expect(forecast.totalForecast).toBeDefined();
      expect(forecast.growthRate).toBeDefined();
      expect(forecast.metadata).toBeDefined();
    });

    it('should provide model metrics', () => {
      const metrics = predictiveModels.getMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.models).toBeDefined();
      expect(typeof metrics.models.demandForecasting).toBe('number');
      expect(typeof metrics.models.churnPrediction).toBe('number');
      expect(typeof metrics.models.performancePrediction).toBe('number');
      expect(typeof metrics.models.revenueForecasting).toBe('number');
      expect(typeof metrics.predictions).toBe('number');
      expect(metrics.trainingState).toBeDefined();
    });
  });

  describe('Recommendation Engine', () => {
    let recommendationEngine;

    beforeEach(async () => {
      recommendationEngine = new RecommendationEngine({
        enableCollaborativeFiltering: true,
        enableContentBasedFiltering: true,
        enableHybridRecommendations: true,
        enableDeepLearning: true,
        maxRecommendations: 10
      });

      await new Promise(resolve => {
        if (recommendationEngine.listenerCount('ready') > 0) {
          recommendationEngine.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (recommendationEngine) {
        await recommendationEngine.shutdown();
      }
    });

    it('should initialize recommendation models', () => {
      expect(recommendationEngine).toBeDefined();
      expect(recommendationEngine.models.collaborativeFiltering.matrixFactorization).toBeDefined();
      expect(recommendationEngine.models.contentBased.contentSimilarity).toBeDefined();
      expect(recommendationEngine.models.deepLearning.wideAndDeep).toBeDefined();
    });

    it('should record user interactions', () => {
      recommendationEngine.recordInteraction('user123', 'item456', 4.5, {
        context: 'purchase',
        timestamp: Date.now()
      });

      const interactions = recommendationEngine.interactions.get('user123');
      expect(interactions).toBeDefined();
      expect(interactions.length).toBe(1);
      expect(interactions[0].itemId).toBe('item456');
      expect(interactions[0].rating).toBe(4.5);
    });

    it('should update item features', () => {
      const itemFeatures = {
        category: 'electronics',
        price: 299.99,
        brand: 'TechCorp',
        rating: 4.2,
        reviews: 150
      };

      recommendationEngine.updateItemFeatures('item789', itemFeatures);

      const storedFeatures = recommendationEngine.itemFeatures.get('item789');
      expect(storedFeatures).toEqual(itemFeatures);
    });

    it('should update user features', () => {
      const userFeatures = {
        age: 28,
        gender: 'M',
        location: 'US',
        preferences: ['electronics', 'books'],
        purchaseHistory: 25
      };

      recommendationEngine.updateUserFeatures('user456', userFeatures);

      const storedFeatures = recommendationEngine.userFeatures.get('user456');
      expect(storedFeatures).toEqual(userFeatures);
    });

    it('should generate collaborative filtering recommendations', async () => {
      // Setup test data
      recommendationEngine.recordInteraction('user1', 'item1', 5);
      recommendationEngine.recordInteraction('user1', 'item2', 4);
      recommendationEngine.recordInteraction('user2', 'item1', 4);
      recommendationEngine.recordInteraction('user2', 'item3', 5);

      const recommendations = await recommendationEngine.generateRecommendations('user1', {
        method: 'collaborative',
        count: 5
      });

      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeGreaterThan(0);
      
      for (const rec of recommendations) {
        expect(rec.itemId).toBeDefined();
        expect(rec.score).toBeDefined();
        expect(rec.method).toBe('collaborative');
        expect(rec.confidence).toBeDefined();
      }
    });

    it('should generate content-based recommendations', async () => {
      // Setup test data
      recommendationEngine.updateItemFeatures('item1', { category: 'electronics', price: 100 });
      recommendationEngine.updateItemFeatures('item2', { category: 'electronics', price: 150 });
      recommendationEngine.updateItemFeatures('item3', { category: 'books', price: 20 });
      
      recommendationEngine.recordInteraction('user1', 'item1', 5);

      const recommendations = await recommendationEngine.generateRecommendations('user1', {
        method: 'content',
        count: 5
      });

      expect(Array.isArray(recommendations)).toBe(true);
      
      for (const rec of recommendations) {
        expect(rec.itemId).toBeDefined();
        expect(rec.score).toBeDefined();
        expect(rec.method).toBe('content');
        expect(rec.confidence).toBeDefined();
      }
    });

    it('should generate hybrid recommendations', async () => {
      // Setup test data
      recommendationEngine.updateUserFeatures('user1', { age: 25, preferences: ['electronics'] });
      recommendationEngine.updateItemFeatures('item1', { category: 'electronics', price: 100 });
      recommendationEngine.recordInteraction('user1', 'item1', 5);

      const recommendations = await recommendationEngine.generateRecommendations('user1', {
        method: 'hybrid',
        count: 5,
        includeExplanations: true
      });

      expect(Array.isArray(recommendations)).toBe(true);
      
      for (const rec of recommendations) {
        expect(rec.itemId).toBeDefined();
        expect(rec.score).toBeDefined();
        expect(rec.method).toBe('hybrid');
        expect(rec.confidence).toBeDefined();
        expect(rec.explanation).toBeDefined();
      }
    });

    it('should provide recommendation metrics', () => {
      const metrics = recommendationEngine.getMetrics();

      expect(metrics).toBeDefined();
      expect(metrics.models).toBeDefined();
      expect(typeof metrics.models.collaborativeFiltering).toBe('number');
      expect(typeof metrics.models.contentBased).toBe('number');
      expect(typeof metrics.models.hybrid).toBe('number');
      expect(typeof metrics.models.deepLearning).toBe('number');
      expect(metrics.data).toBeDefined();
      expect(metrics.cache).toBeDefined();
      expect(metrics.performance).toBeDefined();
    });
  });

  describe('ML Pipeline', () => {
    let mlPipeline;

    beforeEach(async () => {
      mlPipeline = new MLPipeline({
        enableAutoML: false,
        enableModelVersioning: true,
        enableABTesting: true,
        modelPath: './test-models',
        dataPath: './test-data',
        experimentPath: './test-experiments'
      });

      await new Promise(resolve => {
        if (mlPipeline.listenerCount('ready') > 0) {
          mlPipeline.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (mlPipeline) {
        await mlPipeline.shutdown();
      }
    });

    it('should initialize ML pipeline', () => {
      expect(mlPipeline).toBeDefined();
      expect(mlPipeline.options.enableModelVersioning).toBe(true);
      expect(mlPipeline.options.enableABTesting).toBe(true);
      expect(mlPipeline.pipelineState).toBeDefined();
      expect(mlPipeline.modelRegistry).toBeDefined();
    });

    it('should create ML experiment', async () => {
      const experimentConfig = {
        name: 'Test Experiment',
        description: 'Testing ML pipeline',
        modelType: 'neural_network',
        hyperparameters: {
          hiddenLayers: [64, 32],
          learningRate: 0.001,
          dropout: 0.2
        },
        dataset: 'test_dataset'
      };

      const experimentId = await mlPipeline.createExperiment(experimentConfig);

      expect(experimentId).toBeDefined();
      expect(experimentId.startsWith('exp_')).toBe(true);
      
      const experiment = mlPipeline.pipelineState.experiments.get(experimentId);
      expect(experiment).toBeDefined();
      expect(experiment.name).toBe('Test Experiment');
      expect(experiment.status).toBe('created');
    });

    it('should create different model types', async () => {
      const nnModel = await mlPipeline.createModel('neural_network', {
        inputShape: [10],
        hiddenLayers: [32, 16],
        outputUnits: 1
      });

      const lstmModel = await mlPipeline.createModel('lstm', {
        inputShape: [10, 1],
        lstmUnits: [50],
        denseUnits: [25],
        outputUnits: 1
      });

      expect(nnModel).toBeDefined();
      expect(lstmModel).toBeDefined();
    });

    it('should setup A/B test for model', async () => {
      const mockModel = {
        predict: vi.fn(),
        dispose: vi.fn()
      };

      const abTestConfig = {
        trafficSplit: 0.1,
        duration: 86400000, // 1 day
        metrics: ['accuracy', 'latency']
      };

      const testId = await mlPipeline.setupModelABTest('v123', mockModel, abTestConfig);

      expect(testId).toBeDefined();
      expect(testId.startsWith('ab_')).toBe(true);
      
      const abTest = mlPipeline.abTests.get(testId);
      expect(abTest).toBeDefined();
      expect(abTest.trafficSplit).toBe(0.1);
      expect(abTest.status).toBe('active');
    });

    it('should provide pipeline metrics', () => {
      const metrics = mlPipeline.getMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.experiments).toBe('number');
      expect(typeof metrics.modelVersions).toBe('number');
      expect(typeof metrics.activeModels).toBe('number');
      expect(typeof metrics.abTests).toBe('number');
      expect(metrics.pipelineState).toBeDefined();
      expect(typeof metrics.pipelineState.isTraining).toBe('boolean');
      expect(typeof metrics.pipelineState.trainingProgress).toBe('number');
    });
  });

  describe('Integration Tests', () => {
    let analyticsEngine;
    let predictiveModels;
    let recommendationEngine;
    let mlPipeline;

    beforeEach(async () => {
      // Initialize all ML services
      analyticsEngine = new MLAnalyticsEngine({ enableUserBehaviorAnalytics: true });
      predictiveModels = new PredictiveModels({ enableDemandForecasting: true });
      recommendationEngine = new RecommendationEngine({ enableHybridRecommendations: true });
      mlPipeline = new MLPipeline({ enableModelVersioning: true });

      // Wait for all services to be ready
      await Promise.all([
        new Promise(resolve => {
          if (analyticsEngine.listenerCount('ready') > 0) {
            analyticsEngine.once('ready', resolve);
          } else {
            resolve();
          }
        }),
        new Promise(resolve => {
          if (predictiveModels.listenerCount('ready') > 0) {
            predictiveModels.once('ready', resolve);
          } else {
            resolve();
          }
        }),
        new Promise(resolve => {
          if (recommendationEngine.listenerCount('ready') > 0) {
            recommendationEngine.once('ready', resolve);
          } else {
            resolve();
          }
        }),
        new Promise(resolve => {
          if (mlPipeline.listenerCount('ready') > 0) {
            mlPipeline.once('ready', resolve);
          } else {
            resolve();
          }
        })
      ]);
    });

    afterEach(async () => {
      await Promise.all([
        analyticsEngine?.shutdown(),
        predictiveModels?.shutdown(),
        recommendationEngine?.shutdown(),
        mlPipeline?.shutdown()
      ]);
    });

    it('should integrate analytics and recommendations', async () => {
      // Analyze user behavior
      const userData = {
        userId: 'integration_user',
        sessionDuration: 1200,
        pageViews: 10,
        clickCount: 25,
        deviceType: 'desktop'
      };

      const behaviorAnalysis = await analyticsEngine.analyzeUserBehavior(userData);
      
      // Use behavior analysis for recommendations
      recommendationEngine.updateUserFeatures(userData.userId, {
        behaviorCluster: behaviorAnalysis.behaviorCluster,
        engagementLevel: behaviorAnalysis.confidence
      });

      const recommendations = await recommendationEngine.generateRecommendations(userData.userId);

      expect(behaviorAnalysis).toBeDefined();
      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
    });

    it('should handle high-volume ML operations', async () => {
      const operations = [];
      const userCount = 10;

      // Simulate multiple concurrent ML operations
      for (let i = 0; i < userCount; i++) {
        operations.push(
          analyticsEngine.analyzeUserBehavior({
            userId: `user_${i}`,
            sessionDuration: Math.random() * 3600,
            pageViews: Math.floor(Math.random() * 20),
            clickCount: Math.floor(Math.random() * 50)
          })
        );
      }

      const results = await Promise.all(operations);

      expect(results).toHaveLength(userCount);
      results.forEach(result => {
        expect(result).toBeDefined();
        expect(result.userId).toBeDefined();
        expect(result.behaviorCluster).toBeDefined();
      });
    });

    it('should provide comprehensive ML metrics', () => {
      const analyticsMetrics = analyticsEngine.getMetrics();
      const predictiveMetrics = predictiveModels.getMetrics();
      const recommendationMetrics = recommendationEngine.getMetrics();
      const pipelineMetrics = mlPipeline.getMetrics();

      const combinedMetrics = {
        analytics: analyticsMetrics,
        predictive: predictiveMetrics,
        recommendations: recommendationMetrics,
        pipeline: pipelineMetrics
      };

      expect(combinedMetrics.analytics).toBeDefined();
      expect(combinedMetrics.predictive).toBeDefined();
      expect(combinedMetrics.recommendations).toBeDefined();
      expect(combinedMetrics.pipeline).toBeDefined();

      // Verify all services are operational
      expect(combinedMetrics.analytics.modelsLoaded).toBeGreaterThan(0);
      expect(combinedMetrics.predictive.models).toBeDefined();
      expect(combinedMetrics.recommendations.models).toBeDefined();
      expect(combinedMetrics.pipeline.pipelineState).toBeDefined();
    });
  });
});
