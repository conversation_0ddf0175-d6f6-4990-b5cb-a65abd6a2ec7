/**
 * Test Configuration Utility
 * Provides easy switching between local and remote staging servers
 */

import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env.test') });

export type TestEnvironment = 'local' | 'staging' | 'production';

export interface TestConfig {
  environment: TestEnvironment;
  serverUrl: string;
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey: string;
  };
  redis: {
    url: string;
  };
  timeouts: {
    test: number;
    setup: number;
  };
}

/**
 * Get current test environment from environment variable
 */
export function getCurrentEnvironment(): TestEnvironment {
  const env = process.env.TEST_ENV as TestEnvironment;
  return env || 'local';
}

/**
 * Get configuration for specified environment
 */
export function getTestConfig(environment?: TestEnvironment): TestConfig {
  const env = environment || getCurrentEnvironment();
  
  const configs: Record<TestEnvironment, TestConfig> = {
    local: {
      environment: 'local',
      serverUrl: process.env.LOCAL_SERVER_URL || 'http://localhost:3000',
      supabase: {
        url: process.env.LOCAL_SUPABASE_URL || 'http://localhost:54321',
        anonKey: process.env.LOCAL_SUPABASE_ANON_KEY || 'local-anon-key',
        serviceRoleKey: process.env.LOCAL_SUPABASE_SERVICE_ROLE_KEY || 'local-service-key',
      },
      redis: {
        url: process.env.LOCAL_REDIS_URL || 'redis://localhost:6379',
      },
      timeouts: {
        test: parseInt(process.env.TEST_TIMEOUT || '5000'),
        setup: parseInt(process.env.TEST_SETUP_TIMEOUT || '10000'),
      },
    },
    staging: {
      environment: 'staging',
      serverUrl: process.env.STAGING_SERVER_URL || 'https://mvs.kanousai.com',
      supabase: {
        url: process.env.STAGING_SUPABASE_URL || 'https://hiyqiqbgiueyyvqoqhht.supabase.co',
        anonKey: process.env.STAGING_SUPABASE_ANON_KEY || 'staging-anon-key',
        serviceRoleKey: process.env.STAGING_SUPABASE_SERVICE_ROLE_KEY || 'staging-service-key',
      },
      redis: {
        url: process.env.STAGING_REDIS_URL || 'redis://staging-redis:6379',
      },
      timeouts: {
        test: parseInt(process.env.TEST_TIMEOUT || '10000'),
        setup: parseInt(process.env.TEST_SETUP_TIMEOUT || '15000'),
      },
    },
    production: {
      environment: 'production',
      serverUrl: process.env.PRODUCTION_SERVER_URL || 'https://mvs.kanousai.com',
      supabase: {
        url: process.env.PRODUCTION_SUPABASE_URL || 'https://production.supabase.co',
        anonKey: process.env.PRODUCTION_SUPABASE_ANON_KEY || 'production-anon-key',
        serviceRoleKey: process.env.PRODUCTION_SUPABASE_SERVICE_ROLE_KEY || 'production-service-key',
      },
      redis: {
        url: process.env.PRODUCTION_REDIS_URL || 'redis://production-redis:6379',
      },
      timeouts: {
        test: parseInt(process.env.TEST_TIMEOUT || '15000'),
        setup: parseInt(process.env.TEST_SETUP_TIMEOUT || '20000'),
      },
    },
  };

  return configs[env];
}

/**
 * Switch test environment and update process.env
 */
export function switchTestEnvironment(environment: TestEnvironment): TestConfig {
  const config = getTestConfig(environment);
  
  // Update process.env with selected configuration
  process.env.TEST_ENV = environment;
  process.env.NEXT_PUBLIC_SUPABASE_URL = config.supabase.url;
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = config.supabase.anonKey;
  process.env.SUPABASE_SERVICE_ROLE_KEY = config.supabase.serviceRoleKey;
  process.env.REDIS_URL = config.redis.url;
  process.env.TEST_TIMEOUT = config.timeouts.test.toString();
  process.env.TEST_SETUP_TIMEOUT = config.timeouts.setup.toString();
  
  console.log(`🔄 Switched to ${environment} test environment`);
  console.log(`📡 Server: ${config.serverUrl}`);
  console.log(`🗄️  Supabase: ${config.supabase.url}`);
  console.log(`🔴 Redis: ${config.redis.url}`);
  
  return config;
}

/**
 * Check if current environment is using real services
 */
export function isUsingRealServices(): boolean {
  const env = getCurrentEnvironment();
  return env === 'staging' || env === 'production';
}

/**
 * Check if current environment should use mocks
 */
export function shouldUseMocks(): boolean {
  return !isUsingRealServices();
}

/**
 * Get environment-specific test tags
 */
export function getTestTags(): string[] {
  const env = getCurrentEnvironment();
  const tags = [env];
  
  if (isUsingRealServices()) {
    tags.push('integration', 'real-services');
  } else {
    tags.push('unit', 'mocked');
  }
  
  return tags;
}

// Export current configuration
export const testConfig = getTestConfig();
export const currentEnvironment = getCurrentEnvironment();

// Helper functions for common test scenarios
export const isLocal = () => currentEnvironment === 'local';
export const isStaging = () => currentEnvironment === 'staging';
export const isProduction = () => currentEnvironment === 'production';
