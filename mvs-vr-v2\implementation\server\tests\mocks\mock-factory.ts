/**
 * Mock Factory
 * Centralized factory for creating consistent mocks across tests
 */

import { vi } from 'vitest';
import { createRedisMock, createSimpleRedisMock } from './redis-mock.js';

/**
 * Create a mock Supabase client
 */
export function createSupabaseMock() {
  const mockQuery = {
    data: [],
    error: null,
    status: 200,
    statusText: 'OK',
  };

  const mockQueryBuilder = {
    select: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    neq: vi.fn().mockReturnThis(),
    gt: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lt: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    like: vi.fn().mockReturnThis(),
    ilike: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue(mockQuery),
    maybeSingle: vi.fn().mockResolvedValue(mockQuery),
    then: vi.fn().mockResolvedValue(mockQuery),
  };

  return {
    from: vi.fn().mockReturnValue(mockQueryBuilder),
    select: vi.fn().mockReturnValue(mockQueryBuilder),
    insert: vi.fn().mockReturnValue(mockQueryBuilder),
    update: vi.fn().mockReturnValue(mockQueryBuilder),
    delete: vi.fn().mockReturnValue(mockQueryBuilder),
    auth: {
      signInWithPassword: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' }, session: { access_token: 'test-token' } },
        error: null,
      }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null,
      }),
    },
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn().mockResolvedValue({ data: { path: 'test-path' }, error: null }),
        download: vi.fn().mockResolvedValue({ data: new Blob(), error: null }),
        remove: vi.fn().mockResolvedValue({ data: [], error: null }),
      }),
    },
  };
}

/**
 * Create a mock logger
 */
export function createLoggerMock() {
  return {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
    log: vi.fn(),
  };
}

/**
 * Create a mock Express request
 */
export function createRequestMock(overrides: any = {}) {
  return {
    method: 'GET',
    url: '/test',
    path: '/test',
    headers: {},
    query: {},
    params: {},
    body: {},
    user: null,
    session: null,
    get: vi.fn((header: string) => overrides.headers?.[header.toLowerCase()]),
    ...overrides,
  };
}

/**
 * Create a mock Express response
 */
export function createResponseMock() {
  const res = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    set: vi.fn().mockReturnThis(),
    cookie: vi.fn().mockReturnThis(),
    clearCookie: vi.fn().mockReturnThis(),
    redirect: vi.fn().mockReturnThis(),
    end: vi.fn().mockReturnThis(),
    locals: {},
    headersSent: false,
  };

  return res;
}

/**
 * Create a mock Next function
 */
export function createNextMock() {
  return vi.fn();
}

/**
 * Create a mock crypto module
 */
export function createCryptoMock() {
  return {
    createHash: vi.fn().mockReturnValue({
      update: vi.fn().mockReturnThis(),
      digest: vi.fn().mockReturnValue('mocked-hash'),
    }),
    randomBytes: vi.fn().mockReturnValue(Buffer.from('mocked-random-bytes')),
    pbkdf2Sync: vi.fn().mockReturnValue(Buffer.from('mocked-pbkdf2')),
  };
}

/**
 * Create a mock JWT module
 */
export function createJwtMock() {
  return {
    sign: vi.fn().mockReturnValue('mocked-jwt-token'),
    verify: vi.fn().mockReturnValue({ userId: 'test-user-id', role: 'user' }),
    decode: vi.fn().mockReturnValue({ userId: 'test-user-id', role: 'user' }),
  };
}

/**
 * Create a mock file system
 */
export function createFsMock() {
  return {
    readFileSync: vi.fn().mockReturnValue('mocked-file-content'),
    writeFileSync: vi.fn(),
    existsSync: vi.fn().mockReturnValue(true),
    mkdirSync: vi.fn(),
    unlinkSync: vi.fn(),
    statSync: vi.fn().mockReturnValue({
      isFile: () => true,
      isDirectory: () => false,
      size: 1024,
    }),
  };
}

/**
 * Create a mock performance timer
 */
export function createPerformanceMock() {
  let mockTime = 1000;
  
  return {
    now: vi.fn(() => {
      mockTime += 100; // Simulate time passing
      return mockTime;
    }),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByName: vi.fn().mockReturnValue([]),
  };
}

/**
 * Create a comprehensive mock context for tests
 */
export function createTestContext() {
  return {
    redis: createRedisMock(),
    supabase: createSupabaseMock(),
    logger: createLoggerMock(),
    crypto: createCryptoMock(),
    jwt: createJwtMock(),
    fs: createFsMock(),
    performance: createPerformanceMock(),
    
    // Helper methods
    createRequest: createRequestMock,
    createResponse: createResponseMock,
    createNext: createNextMock,
    
    // Cleanup method
    cleanup: () => {
      vi.clearAllMocks();
    },
  };
}

/**
 * Mock environment variables
 */
export function mockEnvVars(vars: Record<string, string>) {
  const originalEnv = process.env;
  
  beforeEach(() => {
    process.env = { ...originalEnv, ...vars };
  });
  
  afterEach(() => {
    process.env = originalEnv;
  });
}

// Export commonly used mocks
export {
  createRedisMock,
  createSimpleRedisMock,
};
