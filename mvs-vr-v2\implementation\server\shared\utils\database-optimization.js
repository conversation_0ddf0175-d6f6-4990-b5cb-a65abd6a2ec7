/**
 * Database Optimization Utilities
 *
 * This module provides utilities for optimizing database queries and operations.
 * It includes query builders, batch operations, and performance monitoring.
 */

import { createClient } from '@supabase/supabase-js';
import { logger } from '../../api/middleware/auth-middleware.js';
import Redis from 'ioredis';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Redis client
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Query cache configuration
const QUERY_CACHE_TTL = 60 * 5; // 5 minutes in seconds
const QUERY_CACHE_PREFIX = 'db:query:';

/**
 * Optimized query builder
 * @param {string} table - Table name
 * @returns {Object} Query builder
 */
function optimizedQuery(table) {
  return {
    /**
     * Select columns from table
     * @param {string|Array} columns - Columns to select
     * @param {Object} options - Query options
     * @returns {Promise<Object>} Query result
     */
    async select(columns = '*', options = {}) {
      const {
        cache = false,
        ttl = QUERY_CACHE_TTL,
        filters = {},
        order = null,
        limit = null,
        offset = null,
      } = options;

      try {
        // Generate cache key if caching is enabled
        let cacheKey = null;
        if (cache) {
          cacheKey =
            QUERY_CACHE_PREFIX +
            JSON.stringify({
              table,
              columns,
              filters,
              order,
              limit,
              offset,
            });

          // Check cache
          const cachedResult = await redis.get(cacheKey);
          if (cachedResult) {
            return JSON.parse(cachedResult);
          }
        }

        // Build query
        let query = supabase
          .from(table)
          .select(typeof columns === 'string' ? columns : columns.join(','));

        // Apply filters
        for (const [column, value] of Object.entries(filters)) {
          if (Array.isArray(value)) {
            query = query.in(column, value);
          } else if (typeof value === 'object' && value !== null) {
            if (value.eq !== undefined) query = query.eq(column, value.eq);
            if (value.neq !== undefined) query = query.neq(column, value.neq);
            if (value.gt !== undefined) query = query.gt(column, value.gt);
            if (value.gte !== undefined) query = query.gte(column, value.gte);
            if (value.lt !== undefined) query = query.lt(column, value.lt);
            if (value.lte !== undefined) query = query.lte(column, value.lte);
            if (value.like !== undefined) query = query.like(column, value.like);
            if (value.ilike !== undefined) query = query.ilike(column, value.ilike);
            if (value.is !== undefined) query = query.is(column, value.is);
          } else {
            query = query.eq(column, value);
          }
        }

        // Apply ordering
        if (order) {
          const { column, ascending = true } = order;
          query = query.order(column, { ascending });
        }

        // Apply pagination
        if (limit !== null) {
          query = query.limit(limit);
        }

        if (offset !== null) {
          query = query.range(offset, offset + (limit || 1) - 1);
        }

        // Execute query
        const startTime = Date.now();
        const { data, error } = await query;
        const queryTime = Date.now() - startTime;

        // Log slow queries
        if (queryTime > 500) {
          logger.warn(`Slow query detected (${queryTime}ms):`, {
            table,
            columns,
            filters,
            order,
            limit,
            offset,
          });
        }

        // Cache result if caching is enabled
        if (cache && cacheKey && data && !error) {
          await redis.set(cacheKey, JSON.stringify({ data, error }), 'EX', ttl);
        }

        return { data, error };
      } catch (error) {
        logger.error(`Error in optimizedQuery.select for table ${table}:`, error);
        return { data: null, error };
      }
    },

    /**
     * Insert rows into table
     * @param {Object|Array} rows - Row(s) to insert
     * @param {Object} options - Insert options
     * @returns {Promise<Object>} Insert result
     */
    async insert(rows, options = {}) {
      const { returning = 'minimal', upsert = false } = options;

      try {
        // Execute insert
        const startTime = Date.now();
        const { data, error } = await supabase.from(table).insert(rows, { returning, upsert });
        const queryTime = Date.now() - startTime;

        // Log slow queries
        if (queryTime > 500) {
          logger.warn(`Slow insert detected (${queryTime}ms):`, {
            table,
            rowCount: Array.isArray(rows) ? rows.length : 1,
          });
        }

        // Invalidate cache for this table
        await invalidateTableCache(table);

        return { data, error };
      } catch (error) {
        logger.error(`Error in optimizedQuery.insert for table ${table}:`, error);
        return { data: null, error };
      }
    },

    /**
     * Update rows in table
     * @param {Object} updates - Updates to apply
     * @param {Object} options - Update options
     * @returns {Promise<Object>} Update result
     */
    async update(updates, options = {}) {
      const { returning = 'minimal', filters = {} } = options;

      try {
        // Build query
        let query = supabase.from(table).update(updates, { returning });

        // Apply filters
        for (const [column, value] of Object.entries(filters)) {
          if (Array.isArray(value)) {
            query = query.in(column, value);
          } else if (typeof value === 'object' && value !== null) {
            if (value.eq !== undefined) query = query.eq(column, value.eq);
            if (value.neq !== undefined) query = query.neq(column, value.neq);
            if (value.gt !== undefined) query = query.gt(column, value.gt);
            if (value.gte !== undefined) query = query.gte(column, value.gte);
            if (value.lt !== undefined) query = query.lt(column, value.lt);
            if (value.lte !== undefined) query = query.lte(column, value.lte);
            if (value.like !== undefined) query = query.like(column, value.like);
            if (value.ilike !== undefined) query = query.ilike(column, value.ilike);
            if (value.is !== undefined) query = query.is(column, value.is);
          } else {
            query = query.eq(column, value);
          }
        }

        // Execute update
        const startTime = Date.now();
        const { data, error } = await query;
        const queryTime = Date.now() - startTime;

        // Log slow queries
        if (queryTime > 500) {
          logger.warn(`Slow update detected (${queryTime}ms):`, {
            table,
            filters,
          });
        }

        // Invalidate cache for this table
        await invalidateTableCache(table);

        return { data, error };
      } catch (error) {
        logger.error(`Error in optimizedQuery.update for table ${table}:`, error);
        return { data: null, error };
      }
    },

    /**
     * Delete rows from table
     * @param {Object} options - Delete options
     * @returns {Promise<Object>} Delete result
     */
    async delete(options = {}) {
      const { returning = 'minimal', filters = {} } = options;

      try {
        // Build query
        let query = supabase.from(table).delete({ returning });

        // Apply filters
        for (const [column, value] of Object.entries(filters)) {
          if (Array.isArray(value)) {
            query = query.in(column, value);
          } else if (typeof value === 'object' && value !== null) {
            if (value.eq !== undefined) query = query.eq(column, value.eq);
            if (value.neq !== undefined) query = query.neq(column, value.neq);
            if (value.gt !== undefined) query = query.gt(column, value.gt);
            if (value.gte !== undefined) query = query.gte(column, value.gte);
            if (value.lt !== undefined) query = query.lt(column, value.lt);
            if (value.lte !== undefined) query = query.lte(column, value.lte);
            if (value.like !== undefined) query = query.like(column, value.like);
            if (value.ilike !== undefined) query = query.ilike(column, value.ilike);
            if (value.is !== undefined) query = query.is(column, value.is);
          } else {
            query = query.eq(column, value);
          }
        }

        // Execute delete
        const startTime = Date.now();
        const { data, error } = await query;
        const queryTime = Date.now() - startTime;

        // Log slow queries
        if (queryTime > 500) {
          logger.warn(`Slow delete detected (${queryTime}ms):`, {
            table,
            filters,
          });
        }

        // Invalidate cache for this table
        await invalidateTableCache(table);

        return { data, error };
      } catch (error) {
        logger.error(`Error in optimizedQuery.delete for table ${table}:`, error);
        return { data: null, error };
      }
    },
  };
}

/**
 * Invalidate cache for a table
 * @param {string} table - Table name
 * @returns {Promise<void>}
 */
async function invalidateTableCache(table) {
  try {
    // Get all cache keys for this table
    const keys = await redis.keys(`${QUERY_CACHE_PREFIX}*${table}*`);

    // Delete all matching keys
    if (keys.length > 0) {
      await redis.del(keys);
      logger.debug(`Invalidated ${keys.length} cache entries for table ${table}`);
    }
  } catch (error) {
    logger.error(`Error invalidating cache for table ${table}:`, error);
  }
}

export { optimizedQuery, invalidateTableCache };
