/**
 * Performance Monitoring Tests
 * Comprehensive tests for performance monitoring services
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PerformanceMonitor } from '../../../services/monitoring/performance-monitor.js';
import { MetricsCollector } from '../../../services/monitoring/metrics-collector.js';
import { PerformanceMiddleware } from '../../../middleware/monitoring/performance-middleware.js';

describe('Performance Monitoring Tests', () => {
  describe('Performance Monitor', () => {
    let performanceMonitor;

    beforeEach(async () => {
      performanceMonitor = new PerformanceMonitor({
        enableRealTimeMonitoring: true,
        enableResourceMonitoring: false, // Disable for testing
        enableApplicationMonitoring: true,
        enableNetworkMonitoring: false, // Disable for testing
        monitoringInterval: 100, // Fast interval for testing
        alertThresholds: {
          cpuUsage: 80,
          memoryUsage: 85,
          responseTime: 1000,
        },
      });

      await new Promise(resolve => {
        if (performanceMonitor.listenerCount('ready') > 0) {
          performanceMonitor.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (performanceMonitor) {
        await performanceMonitor.shutdown();
      }
    });

    it('should initialize and start monitoring', () => {
      expect(performanceMonitor).toBeDefined();
      expect(performanceMonitor.options.enableRealTimeMonitoring).toBe(true);
      expect(performanceMonitor.metrics.system).toBeDefined();
      expect(performanceMonitor.metrics.application).toBeDefined();
    });

    it('should collect real-time metrics', async () => {
      let metricsReceived = false;

      performanceMonitor.on('realTimeMetrics', metrics => {
        metricsReceived = true;
        expect(metrics).toBeDefined();
        expect(metrics.timestamp).toBeDefined();
        expect(metrics.cpu).toBeDefined();
        expect(metrics.memory).toBeDefined();
        expect(metrics.eventLoop).toBeDefined();
      });

      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(metricsReceived).toBe(true);
    });

    it('should trigger alerts on threshold violations', async () => {
      let alertReceived = false;

      performanceMonitor.on('alert', alert => {
        alertReceived = true;
        expect(alert).toBeDefined();
        expect(alert.type).toBeDefined();
        expect(alert.data).toBeDefined();
        expect(alert.timestamp).toBeDefined();
        expect(alert.severity).toBeDefined();
      });

      // Simulate high memory usage alert
      performanceMonitor.triggerAlert('high_memory', {
        current: 90,
        threshold: 85,
      });

      expect(alertReceived).toBe(true);
    });

    it('should collect performance baselines', async () => {
      let baselineReceived = false;

      performanceMonitor.on('baselineCollected', baseline => {
        baselineReceived = true;
        expect(baseline).toBeDefined();
        expect(baseline.timestamp).toBeDefined();
        expect(baseline.cpu).toBeDefined();
        expect(baseline.memory).toBeDefined();
      });

      // Manually trigger baseline collection
      performanceMonitor.collectBaselines();

      expect(baselineReceived).toBe(true);
    });

    it('should provide performance summary', () => {
      const summary = performanceMonitor.getPerformanceSummary();

      expect(summary).toBeDefined();
      expect(summary.alerts).toBeDefined();
      expect(summary.uptime).toBeDefined();
      expect(summary.timestamp).toBeDefined();
    });

    it('should get metrics for time range', async () => {
      // Wait for some metrics to be collected
      await new Promise(resolve => setTimeout(resolve, 200));

      const endTime = Date.now();
      const startTime = endTime - 300000; // 5 minutes ago

      const metrics = performanceMonitor.getMetricsForTimeRange(startTime, endTime);

      expect(metrics).toBeDefined();
      expect(metrics.system).toBeDefined();
      expect(metrics.application).toBeDefined();
      expect(metrics.network).toBeDefined();
    });

    it('should provide service metrics', () => {
      const metrics = performanceMonitor.getMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.systemMetrics).toBe('number');
      expect(typeof metrics.applicationMetrics).toBe('number');
      expect(typeof metrics.activeAlerts).toBe('number');
      expect(typeof metrics.observers).toBe('number');
    });
  });

  describe('Metrics Collector', () => {
    let metricsCollector;

    beforeEach(async () => {
      metricsCollector = new MetricsCollector({
        port: 9465, // Different port for testing
        enableDefaultMetrics: false,
        enableCustomMetrics: true,
        enableHttpServer: false, // Disable HTTP server for testing
        collectInterval: 100, // Fast interval for testing
      });

      await new Promise(resolve => {
        if (metricsCollector.listenerCount('ready') > 0) {
          metricsCollector.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (metricsCollector) {
        await metricsCollector.shutdown();
      }
    });

    it('should initialize with custom metrics', () => {
      expect(metricsCollector).toBeDefined();
      expect(metricsCollector.metrics.httpRequestsTotal).toBeDefined();
      expect(metricsCollector.metrics.websocketConnectionsActive).toBeDefined();
      expect(metricsCollector.metrics.databaseQueriesTotal).toBeDefined();
    });

    it('should record HTTP request metrics', () => {
      const method = 'GET';
      const route = '/api/test';
      const statusCode = 200;
      const duration = 150;
      const requestSize = 1024;
      const responseSize = 2048;

      metricsCollector.recordHttpRequest(
        method,
        route,
        statusCode,
        duration,
        requestSize,
        responseSize,
      );

      // Verify metrics were recorded (would need to check Prometheus registry in real implementation)
      expect(metricsCollector.metrics.httpRequestsTotal).toBeDefined();
      expect(metricsCollector.metrics.httpRequestDuration).toBeDefined();
    });

    it('should record WebSocket event metrics', () => {
      // Test connection event
      metricsCollector.recordWebSocketEvent('connection', { poolId: 'test-pool' });

      // Test message event
      metricsCollector.recordWebSocketEvent('message', {
        type: 'chat',
        direction: 'inbound',
        duration: 50,
      });

      // Test disconnection event
      metricsCollector.recordWebSocketEvent('disconnection', {
        poolId: 'test-pool',
        duration: 30000,
      });

      expect(metricsCollector.metrics.websocketConnectionsActive).toBeDefined();
      expect(metricsCollector.metrics.websocketMessagesTotal).toBeDefined();
    });

    it('should record database query metrics', () => {
      metricsCollector.recordDatabaseQuery('SELECT', 'users', 25, 'success');
      metricsCollector.recordDatabaseQuery('INSERT', 'orders', 100, 'success');
      metricsCollector.recordDatabaseQuery('UPDATE', 'products', 75, 'error');

      expect(metricsCollector.metrics.databaseQueriesTotal).toBeDefined();
      expect(metricsCollector.metrics.databaseQueryDuration).toBeDefined();
    });

    it('should record cache operation metrics', () => {
      metricsCollector.recordCacheOperation('get', 'user', 'hit');
      metricsCollector.recordCacheOperation('get', 'session', 'miss');
      metricsCollector.recordCacheOperation('set', 'asset', 'success');

      expect(metricsCollector.metrics.cacheOperationsTotal).toBeDefined();
    });

    it('should record security event metrics', () => {
      metricsCollector.recordSecurityEvent('login_attempt', 'info');
      metricsCollector.recordSecurityEvent('failed_login', 'warning');
      metricsCollector.recordSecurityEvent('brute_force', 'critical');

      expect(metricsCollector.metrics.securityEventsTotal).toBeDefined();
    });

    it('should create custom metrics', () => {
      const counter = metricsCollector.createCustomMetric('test_counter', 'counter', {
        help: 'Test counter metric',
      });

      const histogram = metricsCollector.createCustomMetric('test_histogram', 'histogram', {
        help: 'Test histogram metric',
        buckets: [0.1, 0.5, 1, 2, 5],
      });

      const gauge = metricsCollector.createCustomMetric('test_gauge', 'gauge', {
        help: 'Test gauge metric',
      });

      expect(counter).toBeDefined();
      expect(histogram).toBeDefined();
      expect(gauge).toBeDefined();
      expect(metricsCollector.metrics.custom.size).toBe(3);
    });

    it('should aggregate metrics', async () => {
      let aggregationReceived = false;

      metricsCollector.on('metricsAggregated', aggregated => {
        aggregationReceived = true;
        expect(aggregated).toBeDefined();
        expect(aggregated.timestamp).toBeDefined();
        expect(aggregated.memory).toBeDefined();
        expect(aggregated.cpu).toBeDefined();
      });

      // Wait for aggregation
      await new Promise(resolve => setTimeout(resolve, 300));

      // Manually trigger aggregation
      metricsCollector.aggregateMetrics();

      expect(aggregationReceived).toBe(true);
    });

    it('should provide service metrics', () => {
      const metrics = metricsCollector.getMetrics();

      expect(metrics).toBeDefined();
      expect(typeof metrics.customMetrics).toBe('number');
      expect(typeof metrics.rawDataPoints).toBe('number');
      expect(typeof metrics.aggregatedDataPoints).toBe('number');
    });
  });

  describe('Performance Middleware', () => {
    let performanceMiddleware;
    let mockMetricsCollector;
    let mockPerformanceMonitor;

    beforeEach(() => {
      // Create mock services
      mockMetricsCollector = {
        recordHttpRequest: vi.fn(),
        recordSecurityEvent: vi.fn(),
        createCustomHistogram: vi.fn(() => ({
          observe: vi.fn(),
        })),
      };

      mockPerformanceMonitor = {
        emit: vi.fn(),
      };

      performanceMiddleware = new PerformanceMiddleware({
        enableRequestTracking: true,
        enableResponseTracking: true,
        enableErrorTracking: true,
        slowRequestThreshold: 100, // Low threshold for testing
        metricsCollector: mockMetricsCollector,
        performanceMonitor: mockPerformanceMonitor,
      });
    });

    afterEach(() => {
      if (performanceMiddleware) {
        performanceMiddleware.shutdown();
      }
    });

    it('should create middleware function', () => {
      const middleware = performanceMiddleware.middleware();
      expect(typeof middleware).toBe('function');
    });

    it('should track request performance', done => {
      const middleware = performanceMiddleware.middleware();

      // Mock request and response
      const req = {
        method: 'GET',
        path: '/api/test',
        route: { path: '/api/test' },
        url: '/api/test',
        get: vi.fn(header => {
          if (header === 'User-Agent') return 'test-agent';
          if (header === 'Content-Length') return '1024';
          return null;
        }),
        ip: '127.0.0.1',
        user: { id: 'user123', sessionId: 'session123' },
      };

      const res = {
        statusCode: 200,
        end: vi.fn(function (...args) {
          // Simulate response completion
          this.emit('finish');
          return this;
        }),
        send: vi.fn(),
        json: vi.fn(),
        on: vi.fn((event, callback) => {
          if (event === 'finish') {
            setTimeout(callback, 10); // Simulate async completion
          }
        }),
        locals: {},
        emit: vi.fn(),
      };

      const next = vi.fn();

      // Execute middleware
      middleware(req, res, next);

      // Simulate response completion
      setTimeout(() => {
        res.end();

        // Verify tracking
        setTimeout(() => {
          expect(next).toHaveBeenCalled();
          expect(mockMetricsCollector.recordHttpRequest).toHaveBeenCalled();
          expect(mockPerformanceMonitor.emit).toHaveBeenCalledWith(
            'requestStart',
            expect.any(Object),
          );
          expect(mockPerformanceMonitor.emit).toHaveBeenCalledWith(
            'requestEnd',
            expect.any(Object),
          );
          done();
        }, 50);
      }, 20);
    });

    it('should skip excluded paths', () => {
      const middleware = performanceMiddleware.middleware();

      const req = {
        path: '/health',
        method: 'GET',
      };
      const res = {};
      const next = vi.fn();

      middleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(mockMetricsCollector.recordHttpRequest).not.toHaveBeenCalled();
    });

    it('should track custom metrics', () => {
      const context = { customMetrics: new Map() };
      performanceMiddleware.requestContext.run(context, () => {
        performanceMiddleware.addCustomMetric('test_metric', 42, { label: 'test' });

        expect(context.customMetrics.has('test_metric')).toBe(true);
        const metric = context.customMetrics.get('test_metric');
        expect(metric.value).toBe(42);
        expect(metric.labels.label).toBe('test');
      });
    });

    it('should track custom timers', () => {
      const context = { timers: new Map() };
      performanceMiddleware.requestContext.run(context, () => {
        performanceMiddleware.startTimer('test_timer');
        expect(context.timers.has('test_timer')).toBe(true);

        // Simulate some work
        setTimeout(() => {
          const duration = performanceMiddleware.endTimer('test_timer', { operation: 'test' });
          expect(duration).toBeGreaterThan(0);
          expect(context.timers.has('test_timer')).toBe(false);
        }, 10);
      });
    });

    it('should provide performance statistics', () => {
      const stats = performanceMiddleware.getStats();

      expect(stats).toBeDefined();
      expect(typeof stats.total).toBe('number');
      expect(typeof stats.successful).toBe('number');
      expect(typeof stats.failed).toBe('number');
      expect(typeof stats.slow).toBe('number');
      expect(typeof stats.activeRequests).toBe('number');
    });

    it('should get active requests information', () => {
      const activeRequests = performanceMiddleware.getActiveRequests();

      expect(Array.isArray(activeRequests)).toBe(true);
    });

    it('should reset statistics', () => {
      performanceMiddleware.requestStats.total = 100;
      performanceMiddleware.resetStats();

      expect(performanceMiddleware.requestStats.total).toBe(0);
      expect(performanceMiddleware.requestStats.successful).toBe(0);
      expect(performanceMiddleware.requestStats.failed).toBe(0);
      expect(performanceMiddleware.requestStats.slow).toBe(0);
    });
  });

  describe('Integration Tests', () => {
    let performanceMonitor;
    let metricsCollector;
    let performanceMiddleware;

    beforeEach(async () => {
      // Initialize services
      performanceMonitor = new PerformanceMonitor({
        enableRealTimeMonitoring: true,
        enableResourceMonitoring: false,
        enableApplicationMonitoring: true,
        enableNetworkMonitoring: false,
        monitoringInterval: 100,
      });

      metricsCollector = new MetricsCollector({
        port: 9466,
        enableHttpServer: false,
        collectInterval: 100,
      });

      performanceMiddleware = new PerformanceMiddleware({
        metricsCollector,
        performanceMonitor,
        slowRequestThreshold: 50,
      });

      // Wait for services to be ready
      await Promise.all([
        new Promise(resolve => {
          if (performanceMonitor.listenerCount('ready') > 0) {
            performanceMonitor.once('ready', resolve);
          } else {
            resolve();
          }
        }),
        new Promise(resolve => {
          if (metricsCollector.listenerCount('ready') > 0) {
            metricsCollector.once('ready', resolve);
          } else {
            resolve();
          }
        }),
      ]);
    });

    afterEach(async () => {
      await Promise.all([
        performanceMonitor?.shutdown(),
        metricsCollector?.shutdown(),
        performanceMiddleware?.shutdown(),
      ]);
    });

    it('should integrate monitoring services', async () => {
      let alertReceived = false;
      let metricsReceived = false;

      performanceMonitor.on('alert', () => {
        alertReceived = true;
      });

      metricsCollector.on('metricsAggregated', () => {
        metricsReceived = true;
      });

      // Simulate some activity
      metricsCollector.recordHttpRequest('GET', '/api/test', 200, 150);
      performanceMonitor.triggerAlert('test_alert', { value: 100 });

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(alertReceived).toBe(true);
    });

    it('should handle high load scenarios', async () => {
      const requestCount = 50;
      const promises = [];

      // Simulate multiple concurrent requests
      for (let i = 0; i < requestCount; i++) {
        promises.push(
          new Promise(resolve => {
            metricsCollector.recordHttpRequest('GET', `/api/test/${i}`, 200, Math.random() * 100);
            resolve();
          }),
        );
      }

      await Promise.all(promises);

      // Verify metrics were recorded
      const metrics = metricsCollector.getMetrics();
      expect(metrics.rawDataPoints).toBeGreaterThan(0);
    });
  });
});
