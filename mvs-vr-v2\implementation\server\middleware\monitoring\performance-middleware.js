/**
 * Performance Monitoring Middleware
 * Express middleware for automatic performance tracking and APM integration
 */

import { performance } from 'perf_hooks';
import { AsyncLocalStorage } from 'async_hooks';

export class PerformanceMiddleware {
  constructor(options = {}) {
    this.options = {
      enableRequestTracking: options.enableRequestTracking !== false,
      enableResponseTracking: options.enableResponseTracking !== false,
      enableErrorTracking: options.enableErrorTracking !== false,
      enableCustomMetrics: options.enableCustomMetrics !== false,
      slowRequestThreshold: options.slowRequestThreshold || 1000, // 1 second
      enableTracing: options.enableTracing !== false,
      enableSampling: options.enableSampling || false,
      samplingRate: options.samplingRate || 0.1, // 10%
      excludePaths: options.excludePaths || ['/health', '/metrics'],
      ...options
    };

    // Services (injected)
    this.apmService = options.apmService || null;
    this.metricsCollector = options.metricsCollector || null;
    this.performanceMonitor = options.performanceMonitor || null;

    // Request context storage
    this.requestContext = new AsyncLocalStorage();
    
    // Request tracking
    this.activeRequests = new Map();
    this.requestStats = {
      total: 0,
      successful: 0,
      failed: 0,
      slow: 0
    };
  }

  /**
   * Main middleware function
   */
  middleware() {
    return (req, res, next) => {
      // Skip excluded paths
      if (this.shouldSkipPath(req.path)) {
        return next();
      }

      // Skip sampling if enabled
      if (this.options.enableSampling && Math.random() > this.options.samplingRate) {
        return next();
      }

      const requestId = this.generateRequestId();
      const startTime = performance.now();
      const startTimestamp = Date.now();

      // Create request context
      const context = {
        requestId,
        method: req.method,
        path: req.path,
        route: req.route?.path || req.path,
        userAgent: req.get('User-Agent'),
        ip: req.ip || req.connection.remoteAddress,
        startTime,
        startTimestamp,
        userId: req.user?.id || null,
        sessionId: req.user?.sessionId || null
      };

      // Store in async local storage
      this.requestContext.run(context, () => {
        // Track active request
        this.activeRequests.set(requestId, context);
        this.requestStats.total++;

        // Start APM span if available
        if (this.apmService && this.options.enableTracing) {
          this.startAPMSpan(context, req);
        }

        // Track request start
        if (this.options.enableRequestTracking) {
          this.trackRequestStart(context, req);
        }

        // Setup response tracking
        this.setupResponseTracking(context, req, res);

        // Setup error tracking
        if (this.options.enableErrorTracking) {
          this.setupErrorTracking(context, req, res);
        }

        next();
      });
    };
  }

  shouldSkipPath(path) {
    return this.options.excludePaths.some(excludePath => 
      path.startsWith(excludePath)
    );
  }

  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  startAPMSpan(context, req) {
    if (!this.apmService) return;

    const spanName = `${context.method} ${context.route}`;
    
    this.apmService.createSpan(spanName, {
      attributes: {
        'http.method': context.method,
        'http.route': context.route,
        'http.url': req.url,
        'http.user_agent': context.userAgent,
        'http.remote_addr': context.ip,
        'user.id': context.userId,
        'session.id': context.sessionId,
        'request.id': context.requestId
      }
    }, async () => {
      // Span will be automatically closed when request completes
    });
  }

  trackRequestStart(context, req) {
    // Record request size if available
    const contentLength = req.get('Content-Length');
    if (contentLength) {
      context.requestSize = parseInt(contentLength);
    }

    // Track custom metrics
    if (this.options.enableCustomMetrics && this.metricsCollector) {
      // Will be recorded when response completes
    }

    // Emit request start event
    if (this.performanceMonitor) {
      this.performanceMonitor.emit('requestStart', {
        requestId: context.requestId,
        method: context.method,
        route: context.route,
        timestamp: context.startTimestamp,
        userId: context.userId
      });
    }
  }

  setupResponseTracking(context, req, res) {
    // Track response completion
    const originalEnd = res.end;
    const originalSend = res.send;
    const originalJson = res.json;

    res.end = (...args) => {
      this.trackRequestEnd(context, req, res);
      return originalEnd.apply(res, args);
    };

    res.send = (...args) => {
      // Track response size
      if (args[0] && typeof args[0] === 'string') {
        context.responseSize = Buffer.byteLength(args[0]);
      }
      return originalSend.apply(res, args);
    };

    res.json = (...args) => {
      // Track JSON response size
      if (args[0]) {
        context.responseSize = Buffer.byteLength(JSON.stringify(args[0]));
      }
      return originalJson.apply(res, args);
    };

    // Track response headers
    res.on('finish', () => {
      this.trackRequestEnd(context, req, res);
    });
  }

  setupErrorTracking(context, req, res) {
    // Override error handling
    const originalNext = res.locals.next || (() => {});
    
    res.locals.next = (error) => {
      if (error) {
        this.trackRequestError(context, req, res, error);
      }
      return originalNext(error);
    };
  }

  trackRequestEnd(context, req, res) {
    if (context.completed) return; // Prevent double tracking
    context.completed = true;

    const endTime = performance.now();
    const duration = endTime - context.startTime;
    const statusCode = res.statusCode;

    // Update context
    context.endTime = endTime;
    context.duration = duration;
    context.statusCode = statusCode;
    context.responseSize = context.responseSize || 0;

    // Update stats
    if (statusCode >= 200 && statusCode < 400) {
      this.requestStats.successful++;
    } else {
      this.requestStats.failed++;
    }

    if (duration > this.options.slowRequestThreshold) {
      this.requestStats.slow++;
    }

    // Record metrics
    if (this.metricsCollector) {
      this.metricsCollector.recordHttpRequest(
        context.method,
        context.route,
        statusCode,
        duration,
        context.requestSize,
        context.responseSize
      );
    }

    // Track with APM
    if (this.apmService) {
      this.apmService.trackHttpRequest(
        context.method,
        context.route,
        statusCode,
        duration
      );
    }

    // Performance monitoring
    if (this.performanceMonitor) {
      this.performanceMonitor.emit('requestEnd', {
        requestId: context.requestId,
        method: context.method,
        route: context.route,
        statusCode,
        duration,
        requestSize: context.requestSize,
        responseSize: context.responseSize,
        userId: context.userId,
        timestamp: Date.now()
      });

      // Alert on slow requests
      if (duration > this.options.slowRequestThreshold) {
        this.performanceMonitor.emit('slowRequest', {
          requestId: context.requestId,
          method: context.method,
          route: context.route,
          duration,
          threshold: this.options.slowRequestThreshold
        });
      }
    }

    // Clean up
    this.activeRequests.delete(context.requestId);

    // Log performance data
    this.logPerformanceData(context);
  }

  trackRequestError(context, req, res, error) {
    context.error = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code
    };

    // Record error metrics
    if (this.metricsCollector) {
      this.metricsCollector.recordSecurityEvent('request_error', 'error');
    }

    // Track with APM
    if (this.apmService) {
      // APM will automatically track the error in the span
    }

    // Performance monitoring
    if (this.performanceMonitor) {
      this.performanceMonitor.emit('requestError', {
        requestId: context.requestId,
        method: context.method,
        route: context.route,
        error: context.error,
        userId: context.userId,
        timestamp: Date.now()
      });
    }

    console.error(`Request error [${context.requestId}]:`, error);
  }

  logPerformanceData(context) {
    const logLevel = context.duration > this.options.slowRequestThreshold ? 'warn' : 'info';
    const logData = {
      requestId: context.requestId,
      method: context.method,
      route: context.route,
      statusCode: context.statusCode,
      duration: `${context.duration.toFixed(2)}ms`,
      requestSize: context.requestSize || 0,
      responseSize: context.responseSize || 0,
      userId: context.userId,
      ip: context.ip
    };

    if (logLevel === 'warn') {
      console.warn('Slow request detected:', logData);
    } else {
      console.log('Request completed:', logData);
    }
  }

  /**
   * Get current request context
   */
  getCurrentContext() {
    return this.requestContext.getStore();
  }

  /**
   * Add custom metric to current request
   */
  addCustomMetric(name, value, labels = {}) {
    const context = this.getCurrentContext();
    if (context) {
      if (!context.customMetrics) {
        context.customMetrics = new Map();
      }
      context.customMetrics.set(name, { value, labels, timestamp: Date.now() });
    }
  }

  /**
   * Start custom timer
   */
  startTimer(name) {
    const context = this.getCurrentContext();
    if (context) {
      if (!context.timers) {
        context.timers = new Map();
      }
      context.timers.set(name, performance.now());
    }
  }

  /**
   * End custom timer and record duration
   */
  endTimer(name, labels = {}) {
    const context = this.getCurrentContext();
    if (context && context.timers && context.timers.has(name)) {
      const startTime = context.timers.get(name);
      const duration = performance.now() - startTime;
      
      // Record with metrics collector
      if (this.metricsCollector && this.options.enableCustomMetrics) {
        const histogram = this.metricsCollector.createCustomHistogram(
          `custom_timer_${name}_duration_seconds`,
          `Duration of custom timer ${name}`,
          [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2, 5]
        );
        histogram.observe(labels, duration / 1000);
      }

      context.timers.delete(name);
      return duration;
    }
    return null;
  }

  /**
   * Get performance statistics
   */
  getStats() {
    return {
      ...this.requestStats,
      activeRequests: this.activeRequests.size,
      averageResponseTime: this.calculateAverageResponseTime(),
      requestsPerSecond: this.calculateRequestsPerSecond()
    };
  }

  calculateAverageResponseTime() {
    const recentRequests = Array.from(this.activeRequests.values())
      .filter(req => req.endTime)
      .slice(-100); // Last 100 requests

    if (recentRequests.length === 0) return 0;

    const totalDuration = recentRequests.reduce((sum, req) => sum + req.duration, 0);
    return totalDuration / recentRequests.length;
  }

  calculateRequestsPerSecond() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    const recentRequests = Array.from(this.activeRequests.values())
      .filter(req => req.startTimestamp > oneMinuteAgo);

    return recentRequests.length / 60; // Requests per second over last minute
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.requestStats = {
      total: 0,
      successful: 0,
      failed: 0,
      slow: 0
    };
  }

  /**
   * Get active requests information
   */
  getActiveRequests() {
    return Array.from(this.activeRequests.values()).map(req => ({
      requestId: req.requestId,
      method: req.method,
      route: req.route,
      duration: performance.now() - req.startTime,
      userId: req.userId,
      ip: req.ip
    }));
  }

  /**
   * Shutdown middleware
   */
  shutdown() {
    console.log('🛑 Shutting down Performance Middleware...');
    
    this.activeRequests.clear();
    this.resetStats();
    
    console.log('✅ Performance Middleware shutdown complete');
  }
}

/**
 * Factory function to create middleware
 */
export function createPerformanceMiddleware(options = {}) {
  const middleware = new PerformanceMiddleware(options);
  return middleware.middleware();
}

/**
 * Express middleware function
 */
export function performanceMiddleware(options = {}) {
  return createPerformanceMiddleware(options);
}

export default PerformanceMiddleware;
