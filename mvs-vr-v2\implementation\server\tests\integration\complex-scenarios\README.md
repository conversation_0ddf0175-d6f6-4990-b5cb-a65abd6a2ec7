# Complex Scenarios Integration Tests

This directory contains integration tests for complex scenarios that were moved from unit tests due to mocking complexity and architectural constraints.

## Purpose

These tests focus on real-world integration scenarios rather than isolated unit testing, allowing us to:

1. **Test Real Functionality**: Use actual services instead of complex mocks
2. **Avoid Mocking Issues**: Eliminate complex dependency injection problems
3. **Focus on Behavior**: Test end-to-end behavior rather than implementation details
4. **Performance Testing**: Measure actual performance in realistic conditions

## Test Categories

### Database Optimization Integration
- **File**: `database-optimization-integration.test.ts`
- **Purpose**: Test database optimization with real Supabase and Redis
- **Scope**: Query optimization, caching, performance monitoring
- **Requirements**: Test database, Redis instance

### Performance Optimization Integration  
- **File**: `performance-optimization-integration.test.ts`
- **Purpose**: Test middleware performance with real HTTP requests
- **Scope**: Caching, compression, ETags, response optimization
- **Requirements**: Test server, Redis instance

## Running Integration Tests

### Prerequisites
```bash
# Start test services
docker-compose -f docker-compose.test.yml up -d

# Set test environment variables
export NODE_ENV=test
export SUPABASE_URL=your_test_supabase_url
export REDIS_URL=redis://localhost:6379
```

### Execute Tests
```bash
# Run all integration tests
npm run test:integration

# Run specific complex scenarios
npm run test -- tests/integration/complex-scenarios

# Run with coverage
npm run test:integration -- --coverage
```

## Migration History

These tests were migrated from unit tests due to the following issues:

### Database Optimization Tests (11 tests)
- **Issue**: Real implementation executing instead of mocked version
- **Root Cause**: Complex module-level dependency injection
- **Solution**: Use real test database and Redis for integration testing

### Performance Optimization Tests (5 tests)
- **Issue**: Middleware context binding problems
- **Root Cause**: Express middleware losing `this` context in test environment
- **Solution**: Use real Express server and HTTP requests for testing

## Future Enhancements

1. **Docker Test Environment**: Automated test container setup
2. **Performance Benchmarking**: Automated performance regression testing
3. **Load Testing**: High-volume scenario testing
4. **Monitoring Integration**: Real-time test metrics and alerting

## Best Practices

1. **Isolation**: Each test should clean up after itself
2. **Realistic Data**: Use realistic test data volumes
3. **Performance Metrics**: Measure and assert on performance characteristics
4. **Error Scenarios**: Test failure modes and recovery
5. **Resource Management**: Properly manage test resources and cleanup

## Contributing

When adding new integration tests:

1. Follow the existing patterns for setup/teardown
2. Use realistic test scenarios
3. Include performance assertions where appropriate
4. Document any special requirements or setup steps
5. Ensure tests are deterministic and can run in parallel
