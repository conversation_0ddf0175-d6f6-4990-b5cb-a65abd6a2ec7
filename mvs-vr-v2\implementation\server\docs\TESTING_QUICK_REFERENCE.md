# Testing Quick Reference Guide

## 🚀 Quick Commands

```bash
# Run stable tests only
npm run test:stable

# Check test stability
npm run test:stability

# Run specific test file
npm test -- tests/unit/api-key-middleware.test.ts

# Run with coverage
npm run test:coverage
```

## 📋 Test Template

```typescript
import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('Module Name', () => {
  let mockDependency: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockDependency = {
      method: vi.fn(),
    };
  });

  describe('function name', () => {
    it('should do something specific', () => {
      // Arrange
      const input = 'test-input';
      const expected = 'expected-output';
      
      // Act
      const result = functionUnderTest(input);
      
      // Assert
      expect(result).toBe(expected);
    });
  });
});
```

## ✅ Good Patterns

### Simple Mocks
```typescript
const mockRedis = {
  get: vi.fn(),
  set: vi.fn(),
};
```

### Clear Assertions
```typescript
expect(mockFunction).toHaveBeenCalledWith('expected-arg');
expect(result).toEqual({ data: 'expected' });
```

### Deterministic Data
```typescript
const testData = {
  id: 'test-id-123',
  name: 'Test Name',
};
```

## ❌ Avoid These

### Complex Mock Chains
```typescript
// Don't do this
mockSupabase.from().select().eq().single().mockResolvedValue();
```

### Real Service Calls
```typescript
// Don't do this
const realRedis = new Redis('redis://localhost');
```

### Random Data
```typescript
// Don't do this
const randomId = Math.random().toString();
```

## 🔧 Common Fixes

### Mock Chain Issues
```typescript
// Instead of complex chains, use simple returns
mockSupabase.query.mockResolvedValue({ data: testData });
```

### Context Binding Issues
```typescript
// Use arrow functions or proper binding
const mockResponse = {
  send: vi.fn().mockReturnThis(),
  status: vi.fn().mockReturnThis(),
};
```

### Module Import Issues
```typescript
// Use proper mocking before imports
vi.mock('module-name', () => ({
  default: mockImplementation,
}));
```

## 📁 File Organization

```
tests/
├── unit/                    # Simple, isolated tests
│   ├── api-key-middleware.test.ts    # ✅ Good example
│   ├── simple-rate-limit.test.ts     # ✅ Good example
│   └── visual-editors/               # ✅ Good example
├── integration/             # Complex scenarios
│   └── complex-scenarios/   # Moved complex tests here
└── e2e/                    # End-to-end tests
```

## 🎯 When to Use What

### Unit Tests
- Simple functions
- Clear inputs/outputs
- No external dependencies
- Fast execution

### Integration Tests
- Multiple components
- Real service interactions
- Complex workflows
- Slower execution

### E2E Tests
- Full user scenarios
- Browser interactions
- Complete workflows
- Slowest execution

## 🆘 Troubleshooting

### Test Failing?
1. Check if it's in our stable test list
2. Simplify the mocks
3. Move to integration if too complex
4. Ask team for help

### Mock Not Working?
1. Ensure mock is before import
2. Use simple mock structure
3. Check vi.clearAllMocks() in beforeEach
4. Verify mock method names

### Timeout Issues?
1. Increase timeout for slow tests
2. Check for infinite loops
3. Ensure mocks return promises
4. Consider moving to integration

## 📞 Getting Help

1. **Check Examples**: Look at stable test files
2. **Use Patterns**: Copy from working tests
3. **Ask Team**: Don't struggle alone
4. **Document**: Share solutions you find

## 🏆 Success Metrics

- Tests run consistently
- No flaky failures
- Fast execution (< 1s per test)
- Clear error messages
- Easy to understand and modify
