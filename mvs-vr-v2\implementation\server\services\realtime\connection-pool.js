/**
 * Connection Pool Service
 * Manages WebSocket connection pools with load balancing and health monitoring
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';

export class ConnectionPool extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      maxPoolSize: options.maxPoolSize || 1000,
      minPoolSize: options.minPoolSize || 10,
      maxConnectionsPerPool: options.maxConnectionsPerPool || 100,
      healthCheckInterval: options.healthCheckInterval || 30000,
      loadBalancingStrategy: options.loadBalancingStrategy || 'round-robin', // round-robin, least-connections, weighted
      enableAutoScaling: options.enableAutoScaling || true,
      scaleUpThreshold: options.scaleUpThreshold || 0.8, // 80% capacity
      scaleDownThreshold: options.scaleDownThreshold || 0.3, // 30% capacity
      connectionTimeout: options.connectionTimeout || 30000,
      enableMetrics: options.enableMetrics || true,
      ...options
    };

    // Pool management
    this.pools = new Map(); // poolId -> pool object
    this.connections = new Map(); // connectionId -> connection object
    this.poolAssignments = new Map(); // connectionId -> poolId
    
    // Load balancing
    this.roundRobinIndex = 0;
    this.poolWeights = new Map(); // poolId -> weight
    
    // Metrics
    this.metrics = {
      totalPools: 0,
      totalConnections: 0,
      activeConnections: 0,
      poolUtilization: 0,
      averageResponseTime: 0,
      connectionErrors: 0,
      lastReset: Date.now()
    };

    this.initialize();
  }

  initialize() {
    console.log('🏊 Initializing Connection Pool Manager');
    
    if (this.options.enableMetrics) {
      this.startMetricsCollection();
    }
    
    this.startHealthChecks();
    
    // Create initial pools
    this.createInitialPools();
    
    console.log('✅ Connection Pool Manager initialized');
    this.emit('ready');
  }

  createInitialPools() {
    const initialPoolCount = Math.max(1, Math.floor(this.options.minPoolSize / this.options.maxConnectionsPerPool));
    
    for (let i = 0; i < initialPoolCount; i++) {
      this.createPool(`pool_${i}`, {
        weight: 1,
        region: 'default',
        priority: 1
      });
    }
  }

  /**
   * Create a new connection pool
   * @param {string} poolId - Unique pool identifier
   * @param {Object} options - Pool configuration options
   */
  createPool(poolId, options = {}) {
    if (this.pools.has(poolId)) {
      throw new Error(`Pool ${poolId} already exists`);
    }

    const pool = {
      id: poolId,
      connections: new Map(),
      maxConnections: options.maxConnections || this.options.maxConnectionsPerPool,
      weight: options.weight || 1,
      region: options.region || 'default',
      priority: options.priority || 1,
      status: 'active', // active, draining, inactive
      createdAt: Date.now(),
      lastHealthCheck: Date.now(),
      metrics: {
        totalConnections: 0,
        activeConnections: 0,
        averageResponseTime: 0,
        errorCount: 0,
        throughput: 0
      },
      metadata: options.metadata || {}
    };

    this.pools.set(poolId, pool);
    this.poolWeights.set(poolId, pool.weight);
    this.metrics.totalPools++;

    console.log(`🏊 Created connection pool: ${poolId} (max: ${pool.maxConnections})`);
    this.emit('poolCreated', { poolId, pool });

    return pool;
  }

  /**
   * Add a connection to the pool system
   * @param {string} connectionId - Unique connection identifier
   * @param {Object} connection - Connection object
   * @param {Object} options - Assignment options
   */
  addConnection(connectionId, connection, options = {}) {
    if (this.connections.has(connectionId)) {
      throw new Error(`Connection ${connectionId} already exists`);
    }

    // Select pool based on load balancing strategy
    const poolId = options.poolId || this.selectPool(options);
    const pool = this.pools.get(poolId);

    if (!pool) {
      throw new Error(`Pool ${poolId} not found`);
    }

    if (pool.connections.size >= pool.maxConnections) {
      // Try to find another pool or create a new one
      const alternativePoolId = this.selectPool({ ...options, excludePool: poolId });
      if (alternativePoolId && alternativePoolId !== poolId) {
        return this.addConnection(connectionId, connection, { ...options, poolId: alternativePoolId });
      }
      
      // Auto-scale if enabled
      if (this.options.enableAutoScaling) {
        const newPoolId = this.autoScale('up');
        if (newPoolId) {
          return this.addConnection(connectionId, connection, { ...options, poolId: newPoolId });
        }
      }
      
      throw new Error(`Pool ${poolId} is at capacity and no alternatives available`);
    }

    // Add connection to pool
    const poolConnection = {
      ...connection,
      poolId,
      addedAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      errorCount: 0,
      responseTime: 0
    };

    pool.connections.set(connectionId, poolConnection);
    this.connections.set(connectionId, poolConnection);
    this.poolAssignments.set(connectionId, poolId);

    // Update metrics
    pool.metrics.totalConnections++;
    pool.metrics.activeConnections++;
    this.metrics.totalConnections++;
    this.metrics.activeConnections++;

    console.log(`🔗 Added connection ${connectionId} to pool ${poolId} (${pool.connections.size}/${pool.maxConnections})`);
    this.emit('connectionAdded', { connectionId, poolId, connection: poolConnection });

    return poolId;
  }

  /**
   * Remove a connection from the pool system
   * @param {string} connectionId - Connection identifier to remove
   */
  removeConnection(connectionId) {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return false;
    }

    const poolId = this.poolAssignments.get(connectionId);
    const pool = this.pools.get(poolId);

    if (pool) {
      pool.connections.delete(connectionId);
      pool.metrics.activeConnections--;
      
      // Check if pool should be scaled down
      if (this.options.enableAutoScaling) {
        this.checkScaleDown();
      }
    }

    this.connections.delete(connectionId);
    this.poolAssignments.delete(connectionId);
    this.metrics.activeConnections--;

    console.log(`🔌 Removed connection ${connectionId} from pool ${poolId}`);
    this.emit('connectionRemoved', { connectionId, poolId });

    return true;
  }

  /**
   * Select the best pool for a new connection
   * @param {Object} options - Selection criteria
   */
  selectPool(options = {}) {
    const availablePools = Array.from(this.pools.values())
      .filter(pool => {
        if (pool.status !== 'active') return false;
        if (options.excludePool === pool.id) return false;
        if (options.region && pool.region !== options.region) return false;
        if (pool.connections.size >= pool.maxConnections) return false;
        return true;
      })
      .sort((a, b) => b.priority - a.priority); // Sort by priority

    if (availablePools.length === 0) {
      return null;
    }

    switch (this.options.loadBalancingStrategy) {
      case 'round-robin':
        return this.selectPoolRoundRobin(availablePools);
      case 'least-connections':
        return this.selectPoolLeastConnections(availablePools);
      case 'weighted':
        return this.selectPoolWeighted(availablePools);
      case 'response-time':
        return this.selectPoolByResponseTime(availablePools);
      default:
        return availablePools[0].id;
    }
  }

  selectPoolRoundRobin(pools) {
    const pool = pools[this.roundRobinIndex % pools.length];
    this.roundRobinIndex++;
    return pool.id;
  }

  selectPoolLeastConnections(pools) {
    const pool = pools.reduce((min, current) => 
      current.connections.size < min.connections.size ? current : min
    );
    return pool.id;
  }

  selectPoolWeighted(pools) {
    const totalWeight = pools.reduce((sum, pool) => sum + pool.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const pool of pools) {
      random -= pool.weight;
      if (random <= 0) {
        return pool.id;
      }
    }
    
    return pools[0].id;
  }

  selectPoolByResponseTime(pools) {
    const pool = pools.reduce((best, current) => 
      current.metrics.averageResponseTime < best.metrics.averageResponseTime ? current : best
    );
    return pool.id;
  }

  /**
   * Get connection information
   * @param {string} connectionId - Connection identifier
   */
  getConnection(connectionId) {
    return this.connections.get(connectionId);
  }

  /**
   * Get pool information
   * @param {string} poolId - Pool identifier
   */
  getPool(poolId) {
    const pool = this.pools.get(poolId);
    if (!pool) return null;

    return {
      ...pool,
      utilization: pool.connections.size / pool.maxConnections,
      connections: Array.from(pool.connections.keys())
    };
  }

  /**
   * Get all pools information
   */
  getAllPools() {
    return Array.from(this.pools.keys()).map(poolId => this.getPool(poolId));
  }

  /**
   * Update connection activity
   * @param {string} connectionId - Connection identifier
   * @param {Object} activity - Activity data
   */
  updateConnectionActivity(connectionId, activity) {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    connection.lastActivity = Date.now();
    connection.messageCount += activity.messageCount || 0;
    connection.errorCount += activity.errorCount || 0;

    if (activity.responseTime) {
      connection.responseTime = activity.responseTime;
      
      // Update pool metrics
      const pool = this.pools.get(connection.poolId);
      if (pool) {
        this.updatePoolResponseTime(pool, activity.responseTime);
      }
    }
  }

  updatePoolResponseTime(pool, responseTime) {
    // Simple moving average
    pool.metrics.averageResponseTime = 
      (pool.metrics.averageResponseTime * 0.9) + (responseTime * 0.1);
  }

  /**
   * Auto-scale pools based on load
   * @param {string} direction - 'up' or 'down'
   */
  autoScale(direction) {
    if (direction === 'up') {
      const totalUtilization = this.calculateTotalUtilization();
      
      if (totalUtilization > this.options.scaleUpThreshold) {
        const newPoolId = `pool_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
        this.createPool(newPoolId, {
          weight: 1,
          region: 'default',
          priority: 1
        });
        
        console.log(`📈 Auto-scaled up: Created pool ${newPoolId}`);
        this.emit('autoScaleUp', { poolId: newPoolId, utilization: totalUtilization });
        
        return newPoolId;
      }
    } else if (direction === 'down') {
      this.checkScaleDown();
    }
    
    return null;
  }

  checkScaleDown() {
    const totalUtilization = this.calculateTotalUtilization();
    
    if (totalUtilization < this.options.scaleDownThreshold && this.pools.size > 1) {
      // Find the least utilized pool
      const pools = Array.from(this.pools.values())
        .filter(pool => pool.status === 'active')
        .sort((a, b) => a.connections.size - b.connections.size);
      
      const leastUtilizedPool = pools[0];
      
      if (leastUtilizedPool && leastUtilizedPool.connections.size === 0) {
        this.removePool(leastUtilizedPool.id);
        console.log(`📉 Auto-scaled down: Removed pool ${leastUtilizedPool.id}`);
        this.emit('autoScaleDown', { poolId: leastUtilizedPool.id, utilization: totalUtilization });
      }
    }
  }

  calculateTotalUtilization() {
    const totalCapacity = Array.from(this.pools.values())
      .reduce((sum, pool) => sum + pool.maxConnections, 0);
    
    return totalCapacity > 0 ? this.metrics.activeConnections / totalCapacity : 0;
  }

  /**
   * Remove a pool
   * @param {string} poolId - Pool identifier to remove
   */
  removePool(poolId) {
    const pool = this.pools.get(poolId);
    if (!pool) return false;

    // Move connections to other pools if any exist
    if (pool.connections.size > 0) {
      console.log(`🔄 Draining pool ${poolId} with ${pool.connections.size} connections`);
      pool.status = 'draining';
      
      // In a real implementation, you would gracefully move connections
      // For now, we'll just mark the pool as draining
      return false;
    }

    this.pools.delete(poolId);
    this.poolWeights.delete(poolId);
    this.metrics.totalPools--;

    console.log(`🗑️ Removed pool: ${poolId}`);
    this.emit('poolRemoved', { poolId });

    return true;
  }

  /**
   * Perform health checks on all pools
   */
  performHealthChecks() {
    const now = Date.now();
    
    for (const [poolId, pool] of this.pools) {
      const timeSinceLastCheck = now - pool.lastHealthCheck;
      
      if (timeSinceLastCheck >= this.options.healthCheckInterval) {
        this.performPoolHealthCheck(pool);
        pool.lastHealthCheck = now;
      }
    }
  }

  performPoolHealthCheck(pool) {
    const startTime = performance.now();
    
    // Check pool health metrics
    const utilization = pool.connections.size / pool.maxConnections;
    const avgResponseTime = pool.metrics.averageResponseTime;
    const errorRate = pool.metrics.errorCount / Math.max(pool.metrics.totalConnections, 1);
    
    let healthStatus = 'healthy';
    const issues = [];
    
    // Check utilization
    if (utilization > 0.9) {
      healthStatus = 'warning';
      issues.push('High utilization');
    }
    
    // Check response time
    if (avgResponseTime > 1000) { // 1 second threshold
      healthStatus = 'warning';
      issues.push('High response time');
    }
    
    // Check error rate
    if (errorRate > 0.1) { // 10% error rate threshold
      healthStatus = 'unhealthy';
      issues.push('High error rate');
    }
    
    const healthCheckDuration = performance.now() - startTime;
    
    const healthReport = {
      poolId: pool.id,
      status: healthStatus,
      utilization,
      avgResponseTime,
      errorRate,
      issues,
      checkDuration: healthCheckDuration,
      timestamp: Date.now()
    };
    
    this.emit('healthCheck', healthReport);
    
    if (healthStatus === 'unhealthy') {
      console.warn(`⚠️ Pool ${pool.id} health check failed:`, issues);
    }
  }

  startHealthChecks() {
    setInterval(() => {
      this.performHealthChecks();
    }, this.options.healthCheckInterval);
  }

  startMetricsCollection() {
    setInterval(() => {
      this.updateMetrics();
    }, 5000);
  }

  updateMetrics() {
    const now = Date.now();
    const timeDiff = now - this.metrics.lastReset;
    
    // Calculate pool utilization
    this.metrics.poolUtilization = this.calculateTotalUtilization();
    
    // Calculate average response time across all pools
    const pools = Array.from(this.pools.values());
    if (pools.length > 0) {
      this.metrics.averageResponseTime = pools.reduce((sum, pool) => 
        sum + pool.metrics.averageResponseTime, 0) / pools.length;
    }
    
    this.metrics.lastReset = now;
    
    this.emit('metrics', this.metrics);
  }

  getMetrics() {
    return {
      ...this.metrics,
      pools: this.pools.size,
      totalCapacity: Array.from(this.pools.values())
        .reduce((sum, pool) => sum + pool.maxConnections, 0),
      poolDetails: Array.from(this.pools.values()).map(pool => ({
        id: pool.id,
        connections: pool.connections.size,
        maxConnections: pool.maxConnections,
        utilization: pool.connections.size / pool.maxConnections,
        status: pool.status,
        metrics: pool.metrics
      }))
    };
  }

  /**
   * Gracefully shutdown the connection pool manager
   */
  async shutdown() {
    console.log('🛑 Shutting down Connection Pool Manager...');
    
    // Mark all pools as draining
    for (const pool of this.pools.values()) {
      pool.status = 'draining';
    }
    
    // Wait for connections to drain (in a real implementation)
    // For now, we'll just clear everything
    
    this.pools.clear();
    this.connections.clear();
    this.poolAssignments.clear();
    this.poolWeights.clear();
    
    console.log('✅ Connection Pool Manager shutdown complete');
  }
}

export default ConnectionPool;
