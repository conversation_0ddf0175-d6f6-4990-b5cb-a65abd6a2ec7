/**
 * Business Metrics API Routes
 * 
 * API endpoints for business metrics collection, analytics, and reporting
 */

const express = require('express');
const { BusinessMetricsCollector } = require('../../services/metrics/business-metrics-collector');
const { BusinessMetricsAnalytics } = require('../../services/metrics/business-metrics-analytics');

const logger = require('../../utils/logger').getLogger('business-metrics-api');

const router = express.Router();

// Initialize business metrics services
const metricsCollector = new BusinessMetricsCollector({
  configPath: process.env.BUSINESS_METRICS_CONFIG_PATH,
  outputPath: process.env.BUSINESS_METRICS_OUTPUT_PATH,
  collectionInterval: parseInt(process.env.METRICS_COLLECTION_INTERVAL || '300000', 10),
  retentionDays: parseInt(process.env.METRICS_RETENTION_DAYS || '90', 10)
});

const metricsAnalytics = new BusinessMetricsAnalytics({
  analysisInterval: parseInt(process.env.METRICS_ANALYSIS_INTERVAL || '3600000', 10),
  trendWindowDays: parseInt(process.env.TREND_WINDOW_DAYS || '7', 10),
  correlationThreshold: parseFloat(process.env.CORRELATION_THRESHOLD || '0.7'),
  anomalyThreshold: parseFloat(process.env.ANOMALY_THRESHOLD || '2.0')
});

// Set up service references
metricsAnalytics.setMetricsCollector(metricsCollector);

// Initialize services
Promise.all([
  metricsCollector.initialize(),
]).then(() => {
  // Start collection and analytics
  metricsCollector.startCollection();
  metricsAnalytics.startAnalytics();
  logger.info('Business metrics services initialized and started');
}).catch(error => {
  logger.error('Failed to initialize business metrics services:', error);
});

/**
 * @swagger
 * /api/business-metrics/current:
 *   get:
 *     summary: Get current business metrics
 *     tags: [Business Metrics]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by metric category
 *     responses:
 *       200:
 *         description: Current business metrics
 */
router.get('/current', (req, res) => {
  try {
    const { category } = req.query;
    const metrics = metricsCollector.getCurrentMetrics(category);
    
    res.json({
      success: true,
      data: {
        metrics,
        count: metrics.length,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Error getting current metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CURRENT_METRICS_FAILED',
        message: 'Failed to get current metrics'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/history/{metricId}:
 *   get:
 *     summary: Get metric history
 *     tags: [Business Metrics]
 *     parameters:
 *       - in: path
 *         name: metricId
 *         required: true
 *         schema:
 *           type: string
 *         description: Metric ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of data points
 *     responses:
 *       200:
 *         description: Metric history
 */
router.get('/history/:metricId', (req, res) => {
  try {
    const { metricId } = req.params;
    const limit = parseInt(req.query.limit || '100', 10);
    
    const history = metricsCollector.getMetricHistory(metricId, limit);
    
    res.json({
      success: true,
      data: {
        metricId,
        history,
        count: history.length
      }
    });
  } catch (error) {
    logger.error('Error getting metric history:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'METRIC_HISTORY_FAILED',
        message: 'Failed to get metric history'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/aggregated:
 *   get:
 *     summary: Get aggregated metrics
 *     tags: [Business Metrics]
 *     parameters:
 *       - in: query
 *         name: interval
 *         required: true
 *         schema:
 *           type: string
 *           enum: [1h, 1d, 1w, 1M]
 *         description: Aggregation interval
 *     responses:
 *       200:
 *         description: Aggregated metrics
 */
router.get('/aggregated', (req, res) => {
  try {
    const { interval } = req.query;
    
    if (!interval) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_INTERVAL',
          message: 'Aggregation interval is required'
        }
      });
    }
    
    const aggregatedMetrics = metricsCollector.getAggregatedMetrics(interval);
    
    res.json({
      success: true,
      data: {
        interval,
        aggregations: aggregatedMetrics,
        count: Object.keys(aggregatedMetrics).length
      }
    });
  } catch (error) {
    logger.error('Error getting aggregated metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'AGGREGATED_METRICS_FAILED',
        message: 'Failed to get aggregated metrics'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/custom:
 *   post:
 *     summary: Add custom metric
 *     tags: [Business Metrics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - metricId
 *               - value
 *             properties:
 *               metricId:
 *                 type: string
 *                 description: Unique metric identifier
 *               value:
 *                 type: number
 *                 description: Metric value
 *               metadata:
 *                 type: object
 *                 description: Additional metric metadata
 *     responses:
 *       201:
 *         description: Custom metric added
 */
router.post('/custom', (req, res) => {
  try {
    const { metricId, value, metadata } = req.body;
    
    if (!metricId || typeof value !== 'number') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'metricId and numeric value are required'
        }
      });
    }
    
    metricsCollector.addCustomMetric(metricId, value, metadata || {});
    
    res.status(201).json({
      success: true,
      message: 'Custom metric added successfully'
    });
  } catch (error) {
    logger.error('Error adding custom metric:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ADD_CUSTOM_METRIC_FAILED',
        message: 'Failed to add custom metric'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/insights:
 *   get:
 *     summary: Get business insights
 *     tags: [Business Metrics]
 *     responses:
 *       200:
 *         description: Business insights and recommendations
 */
router.get('/insights', (req, res) => {
  try {
    const insights = metricsCollector.getBusinessInsights();
    
    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    logger.error('Error getting business insights:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INSIGHTS_FAILED',
        message: 'Failed to get business insights'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/analytics:
 *   get:
 *     summary: Get analytics results
 *     tags: [Business Metrics]
 *     responses:
 *       200:
 *         description: Analytics results including trends and correlations
 */
router.get('/analytics', (req, res) => {
  try {
    const analytics = metricsAnalytics.getAnalyticsResults();
    
    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    logger.error('Error getting analytics results:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ANALYTICS_FAILED',
        message: 'Failed to get analytics results'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/export:
 *   post:
 *     summary: Export metrics data
 *     tags: [Business Metrics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               format:
 *                 type: string
 *                 enum: [json, csv]
 *                 default: json
 *               startDate:
 *                 type: string
 *                 format: date-time
 *               endDate:
 *                 type: string
 *                 format: date-time
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Export file path
 */
router.post('/export', async (req, res) => {
  try {
    const options = req.body;
    
    const filePath = await metricsCollector.exportMetrics(options);
    
    res.json({
      success: true,
      data: {
        filePath,
        message: 'Metrics exported successfully'
      }
    });
  } catch (error) {
    logger.error('Error exporting metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'EXPORT_FAILED',
        message: 'Failed to export metrics'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/statistics:
 *   get:
 *     summary: Get metrics collection statistics
 *     tags: [Business Metrics]
 *     responses:
 *       200:
 *         description: Collection statistics
 */
router.get('/statistics', (req, res) => {
  try {
    const collectorStats = metricsCollector.getStatistics();
    const analyticsStats = metricsAnalytics.getStatistics();
    
    res.json({
      success: true,
      data: {
        collector: collectorStats,
        analytics: analyticsStats
      }
    });
  } catch (error) {
    logger.error('Error getting statistics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATISTICS_FAILED',
        message: 'Failed to get statistics'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-metrics/health:
 *   get:
 *     summary: Get metrics service health
 *     tags: [Business Metrics]
 *     responses:
 *       200:
 *         description: Service health status
 */
router.get('/health', (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        collector: {
          status: metricsCollector.isCollecting ? 'collecting' : 'stopped',
          statistics: metricsCollector.getStatistics()
        },
        analytics: {
          status: 'running',
          statistics: metricsAnalytics.getStatistics()
        }
      }
    };
    
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    logger.error('Error getting health status:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Failed to get health status'
      }
    });
  }
});

// Export services for use in other modules
router.metricsCollector = metricsCollector;
router.metricsAnalytics = metricsAnalytics;

module.exports = router;
