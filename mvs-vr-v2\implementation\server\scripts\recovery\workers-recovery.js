/**
 * Workers Recovery Script
 * 
 * This script handles recovery of background worker services.
 */

const { execSync } = require('child_process');
const logger = require('../../utils/logger').getLogger('workers-recovery');
const workersService = require('../../services/workers');

// Configuration
const config = {
  maxRetries: 3,
  retryDelay: 5000, // 5 seconds
  restartCommand: process.env.WORKERS_RESTART_COMMAND || 'docker-compose restart workers',
  healthCheckTimeout: 30000 // 30 seconds
};

/**
 * Recover worker services
 * @param {Object} options - Recovery options
 * @returns {Promise<Object>} Recovery result
 */
async function recoverWorkers(options = {}) {
  const {
    test = false,
    restart = true
  } = options;
  
  logger.info('Starting workers recovery');
  
  try {
    if (test) {
      logger.info('[TEST] Would perform workers recovery');
      const healthStatus = await checkWorkersHealth();
      return {
        success: true,
        test: true,
        currentHealth: healthStatus,
        message: 'Workers recovery test completed'
      };
    }
    
    // Check current workers health
    let isHealthy = await checkWorkersHealth();
    
    if (isHealthy) {
      logger.info('Workers are already healthy, no recovery needed');
      return {
        success: true,
        message: 'Workers are already healthy',
        restarted: false
      };
    }
    
    // Initialize workers service
    try {
      await workersService.initialize();
      logger.info('Workers service initialized');
    } catch (error) {
      logger.warn(`Failed to initialize workers service: ${error.message}`);
    }
    
    // Restart workers if requested
    if (restart) {
      logger.info('Restarting workers service');
      await restartWorkersService();
      
      // Wait for service to start
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
    }
    
    // Wait for workers to become healthy
    isHealthy = await waitForWorkersHealth();
    
    if (!isHealthy) {
      throw new Error('Workers failed to become healthy after recovery attempts');
    }
    
    logger.info('Workers recovery completed successfully');
    
    return {
      success: true,
      message: 'Workers service recovered successfully',
      restarted: restart
    };
    
  } catch (error) {
    logger.error(`Workers recovery failed: ${error.message}`, { error });
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Check workers health
 * @returns {Promise<boolean>} Health status
 */
async function checkWorkersHealth() {
  try {
    const isHealthy = await workersService.checkStatus();
    return isHealthy;
  } catch (error) {
    logger.debug(`Workers health check failed: ${error.message}`);
    return false;
  }
}

/**
 * Restart workers service
 * @returns {Promise<void>}
 */
async function restartWorkersService() {
  try {
    logger.info(`Executing restart command: ${config.restartCommand}`);
    execSync(config.restartCommand, { stdio: 'inherit' });
    logger.info('Workers service restart command executed');
  } catch (error) {
    logger.error(`Failed to restart workers service: ${error.message}`);
    throw error;
  }
}

/**
 * Wait for workers to become healthy
 * @returns {Promise<boolean>} Final health status
 */
async function waitForWorkersHealth() {
  logger.info('Waiting for workers to become healthy');
  
  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    logger.info(`Health check attempt ${attempt}/${config.maxRetries}`);
    
    const isHealthy = await checkWorkersHealth();
    
    if (isHealthy) {
      logger.info(`Workers became healthy after ${attempt} attempts`);
      return true;
    }
    
    if (attempt < config.maxRetries) {
      logger.info(`Waiting ${config.retryDelay}ms before next attempt`);
      await new Promise(resolve => setTimeout(resolve, config.retryDelay));
    }
  }
  
  logger.error(`Workers failed to become healthy after ${config.maxRetries} attempts`);
  return false;
}

/**
 * Get workers status information
 * @returns {Promise<Object>} Workers status
 */
async function getWorkersStatus() {
  try {
    const healthStatus = await workersService.getHealthStatus();
    const statistics = await workersService.getStatistics();
    
    return {
      available: true,
      health: healthStatus,
      statistics
    };
  } catch (error) {
    return {
      available: false,
      error: error.message
    };
  }
}

/**
 * Perform comprehensive workers health check
 * @returns {Promise<Object>} Comprehensive health status
 */
async function comprehensiveHealthCheck() {
  const healthCheck = {
    timestamp: new Date().toISOString(),
    overall: false,
    checks: {}
  };
  
  // Basic health check
  try {
    const basicHealth = await checkWorkersHealth();
    healthCheck.checks.basic = {
      status: basicHealth ? 'healthy' : 'unhealthy',
      success: basicHealth
    };
  } catch (error) {
    healthCheck.checks.basic = {
      status: 'error',
      success: false,
      error: error.message
    };
  }
  
  // Detailed health status
  try {
    const detailedHealth = await workersService.getHealthStatus();
    healthCheck.checks.detailed = {
      status: detailedHealth.status,
      success: detailedHealth.status === 'healthy',
      data: detailedHealth
    };
  } catch (error) {
    healthCheck.checks.detailed = {
      status: 'error',
      success: false,
      error: error.message
    };
  }
  
  // Worker statistics
  try {
    const statistics = await workersService.getStatistics();
    healthCheck.checks.statistics = {
      status: 'healthy',
      success: true,
      data: statistics
    };
  } catch (error) {
    healthCheck.checks.statistics = {
      status: 'error',
      success: false,
      error: error.message
    };
  }
  
  // Individual worker status
  try {
    const workersStatus = await workersService.getAllWorkersStatus();
    healthCheck.checks.workers = {
      status: 'healthy',
      success: true,
      data: workersStatus
    };
    
    // Check if any workers are stuck
    const stuckWorkers = workersStatus.filter(worker => {
      if (worker.status === 'busy' && worker.lastActivity) {
        const timeSinceActivity = Date.now() - new Date(worker.lastActivity).getTime();
        return timeSinceActivity > 300000; // 5 minutes
      }
      return false;
    });
    
    if (stuckWorkers.length > 0) {
      healthCheck.checks.workers.status = 'degraded';
      healthCheck.checks.workers.warnings = [`${stuckWorkers.length} workers appear to be stuck`];
    }
    
  } catch (error) {
    healthCheck.checks.workers = {
      status: 'error',
      success: false,
      error: error.message
    };
  }
  
  // Determine overall health
  healthCheck.overall = Object.values(healthCheck.checks).every(check => check.success);
  
  return healthCheck;
}

/**
 * Test worker functionality by submitting a test job
 * @returns {Promise<Object>} Test result
 */
async function testWorkerFunctionality() {
  try {
    logger.info('Testing worker functionality');
    
    // Submit a test job
    const jobId = await workersService.addJob('email-sender', {
      to: '<EMAIL>',
      subject: 'Worker Test',
      body: 'This is a test job to verify worker functionality'
    }, {
      priority: 10,
      attempts: 1
    });
    
    // Wait for job completion
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds
    
    while (attempts < maxAttempts) {
      const jobStatus = await workersService.getJobStatus(jobId);
      
      if (jobStatus && (jobStatus.status === 'completed' || jobStatus.status === 'failed')) {
        return {
          success: jobStatus.status === 'completed',
          jobId,
          status: jobStatus.status,
          result: jobStatus.result,
          error: jobStatus.error
        };
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      attempts++;
    }
    
    return {
      success: false,
      jobId,
      error: 'Test job timed out'
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// If script is run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {
    test: args.includes('--test'),
    restart: !args.includes('--no-restart')
  };
  
  recoverWorkers(options)
    .then(result => {
      if (result.success) {
        console.log('Workers recovery completed successfully');
        process.exit(0);
      } else {
        console.error('Workers recovery failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}

module.exports = {
  recoverWorkers,
  checkWorkersHealth,
  waitForWorkersHealth,
  getWorkersStatus,
  comprehensiveHealthCheck,
  testWorkerFunctionality
};
