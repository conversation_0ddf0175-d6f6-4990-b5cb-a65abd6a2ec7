{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/VisualEditorsPerformanceOptimization.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 45643, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 45643, "count": 1}, {"startOffset": 628, "endOffset": 45642, "count": 0}], "isBlockCoverage": true}, {"functionName": "createLargeDataset", "ranges": [{"startOffset": 1338, "endOffset": 1549, "count": 0}], "isBlockCoverage": false}, {"functionName": "createLargeAnimationDataset", "ranges": [{"startOffset": 1622, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2616, "endOffset": 8805, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36385, "count": 1}, {"startOffset": 1017, "endOffset": 10300, "count": 0}, {"startOffset": 10301, "endOffset": 36384, "count": 0}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2891, "endOffset": 3612, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatLastSaved", "ranges": [{"startOffset": 3633, "endOffset": 4361, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 4370, "endOffset": 4413, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadInitialData", "ranges": [{"startOffset": 4433, "endOffset": 5424, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadShowrooms", "ranges": [{"startOffset": 5431, "endOffset": 5804, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadProducts", "ranges": [{"startOffset": 5811, "endOffset": 6173, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadMaterials", "ranges": [{"startOffset": 6180, "endOffset": 6546, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimations", "ranges": [{"startOffset": 6553, "endOffset": 6923, "count": 0}], "isBlockCoverage": false}, {"functionName": "setActiveTab", "ranges": [{"startOffset": 6930, "endOffset": 7597, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLayoutUpdate", "ranges": [{"startOffset": 7604, "endOffset": 8041, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleProductUpdate", "ranges": [{"startOffset": 8048, "endOffset": 8483, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleMaterialUpdate", "ranges": [{"startOffset": 8490, "endOffset": 8931, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLightingUpdate", "ranges": [{"startOffset": 8938, "endOffset": 9058, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleAnimationUpdate", "ranges": [{"startOffset": 9065, "endOffset": 9512, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateLastSaved", "ranges": [{"startOffset": 9519, "endOffset": 9579, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleAutoSave", "ranges": [{"startOffset": 9586, "endOffset": 9662, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 9943, "endOffset": 10049, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10491, "endOffset": 10580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10698, "endOffset": 10781, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10830, "endOffset": 10874, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38148, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1939, "endOffset": 2622, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2643, "endOffset": 2768, "count": 0}], "isBlockCoverage": false}, {"functionName": "gridStyle", "ranges": [{"startOffset": 2779, "endOffset": 2898, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 2907, "endOffset": 2943, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 2963, "endOffset": 3874, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterProducts", "ranges": [{"startOffset": 3881, "endOffset": 4269, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 4276, "endOffset": 4439, "count": 0}], "isBlockCoverage": false}, {"functionName": "to<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4446, "endOffset": 4504, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleSnap", "ranges": [{"startOffset": 4511, "endOffset": 4573, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomIn", "ranges": [{"startOffset": 4580, "endOffset": 4667, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomOut", "ranges": [{"startOffset": 4674, "endOffset": 4764, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetZoom", "ranges": [{"startOffset": 4771, "endOffset": 4816, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleViewMode", "ranges": [{"startOffset": 4823, "endOffset": 4907, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragStart", "ranges": [{"startOffset": 4914, "endOffset": 5068, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragOver", "ranges": [{"startOffset": 5075, "endOffset": 5130, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDrop", "ranges": [{"startOffset": 5137, "endOffset": 6006, "count": 0}], "isBlockCoverage": false}, {"functionName": "startDrag", "ranges": [{"startOffset": 6013, "endOffset": 7166, "count": 0}], "isBlockCoverage": false}, {"functionName": "getItemStyle", "ranges": [{"startOffset": 7173, "endOffset": 7428, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 7435, "endOffset": 7609, "count": 0}], "isBlockCoverage": false}, {"functionName": "rotateItem", "ranges": [{"startOffset": 7616, "endOffset": 7690, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLayout", "ranges": [{"startOffset": 7697, "endOffset": 8416, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetLayout", "ranges": [{"startOffset": 8423, "endOffset": 8556, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 8837, "endOffset": 8943, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9391, "endOffset": 9480, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9605, "endOffset": 9688, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9737, "endOffset": 9781, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 59065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 59065, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 8645, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 8670, "endOffset": 9014, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 9016, "endOffset": 9360, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9486, "endOffset": 9508, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9615, "endOffset": 9646, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 4}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1167", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 4}, {"startOffset": 543, "endOffset": 553, "count": 3}, {"startOffset": 553, "endOffset": 597, "count": 1}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 611, "endOffset": 616, "count": 1}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 812, "endOffset": 904, "count": 1}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 4}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 4}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 4}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 8}, {"startOffset": 2728, "endOffset": 2796, "count": 4}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 4}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1168", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 90564, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 90564, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2630, "endOffset": 3566, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3587, "endOffset": 3746, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalOptionsPrice", "ranges": [{"startOffset": 3753, "endOffset": 4571, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalPrice", "ranges": [{"startOffset": 4578, "endOffset": 4717, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 4726, "endOffset": 4762, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 4782, "endOffset": 5517, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterProducts", "ranges": [{"startOffset": 5524, "endOffset": 5910, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 5917, "endOffset": 6080, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectProductById", "ranges": [{"startOffset": 6087, "endOffset": 6256, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectProduct", "ranges": [{"startOffset": 6263, "endOffset": 7350, "count": 0}], "isBlockCoverage": false}, {"functionName": "addOptionGroup", "ranges": [{"startOffset": 7357, "endOffset": 7574, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeOptionGroup", "ranges": [{"startOffset": 7581, "endOffset": 8171, "count": 0}], "isBlockCoverage": false}, {"functionName": "addOption", "ranges": [{"startOffset": 8178, "endOffset": 8437, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeOption", "ranges": [{"startOffset": 8444, "endOffset": 8849, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectOption", "ranges": [{"startOffset": 8856, "endOffset": 11209, "count": 0}], "isBlockCoverage": false}, {"functionName": "isOptionSelected", "ranges": [{"startOffset": 11216, "endOffset": 11545, "count": 0}], "isBlockCoverage": false}, {"functionName": "isOptionDisabled", "ranges": [{"startOffset": 11634, "endOffset": 11744, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveConfiguration", "ranges": [{"startOffset": 11751, "endOffset": 12657, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetConfiguration", "ranges": [{"startOffset": 12664, "endOffset": 12825, "count": 0}], "isBlockCoverage": false}, {"functionName": "addDependency", "ranges": [{"startOffset": 12892, "endOffset": 13632, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeDependency", "ranges": [{"startOffset": 13639, "endOffset": 13975, "count": 0}], "isBlockCoverage": false}, {"functionName": "addIncompatibility", "ranges": [{"startOffset": 13982, "endOffset": 14782, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeIncompatibility", "ranges": [{"startOffset": 14789, "endOffset": 15155, "count": 0}], "isBlockCoverage": false}, {"functionName": "canSelectOption", "ranges": [{"startOffset": 15248, "endOffset": 16340, "count": 0}], "isBlockCoverage": false}, {"functionName": "areDependenciesSatisfied", "ranges": [{"startOffset": 16420, "endOffset": 17009, "count": 0}], "isBlockCoverage": false}, {"functionName": "addConfigRule", "ranges": [{"startOffset": 17048, "endOffset": 17348, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeConfigRule", "ranges": [{"startOffset": 17390, "endOffset": 17470, "count": 0}], "isBlockCoverage": false}, {"functionName": "showAddDependencyModal", "ranges": [{"startOffset": 17534, "endOffset": 17795, "count": 0}], "isBlockCoverage": false}, {"functionName": "hideAddDependencyModal", "ranges": [{"startOffset": 17802, "endOffset": 17874, "count": 0}], "isBlockCoverage": false}, {"functionName": "confirmAddDependency", "ranges": [{"startOffset": 17881, "endOffset": 18322, "count": 0}], "isBlockCoverage": false}, {"functionName": "showAddIncompatibilityModal", "ranges": [{"startOffset": 18329, "endOffset": 18600, "count": 0}], "isBlockCoverage": false}, {"functionName": "hideAddIncompatibilityModal", "ranges": [{"startOffset": 18607, "endOffset": 18689, "count": 0}], "isBlockCoverage": false}, {"functionName": "confirmAddIncompatibility", "ranges": [{"startOffset": 18696, "endOffset": 19152, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDependencyLabel", "ranges": [{"startOffset": 19225, "endOffset": 19581, "count": 0}], "isBlockCoverage": false}, {"functionName": "getIncompatibilityLabel", "ranges": [{"startOffset": 19588, "endOffset": 19974, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 20255, "endOffset": 20361, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20808, "endOffset": 20897, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21021, "endOffset": 21104, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21153, "endOffset": 21197, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1169", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 150992, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 150992, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 23868, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 23893, "endOffset": 24235, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 24361, "endOffset": 24383, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 24490, "endOffset": 24521, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1170", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ProductConfigurator.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1171", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 49515, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 49515, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2252, "endOffset": 4022, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4043, "endOffset": 4187, "count": 0}], "isBlockCoverage": false}, {"functionName": "previewStyle", "ranges": [{"startOffset": 4194, "endOffset": 4706, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 4715, "endOffset": 4751, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 4771, "endOffset": 5353, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterMaterials", "ranges": [{"startOffset": 5360, "endOffset": 6037, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectCategory", "ranges": [{"startOffset": 6044, "endOffset": 6152, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectMaterialById", "ranges": [{"startOffset": 6159, "endOffset": 6336, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectMaterial", "ranges": [{"startOffset": 6343, "endOffset": 6625, "count": 0}], "isBlockCoverage": false}, {"functionName": "createNewMaterial", "ranges": [{"startOffset": 6632, "endOffset": 7184, "count": 0}], "isBlockCoverage": false}, {"functionName": "openTextureUpload", "ranges": [{"startOffset": 7191, "endOffset": 7314, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTextureSelected", "ranges": [{"startOffset": 7321, "endOffset": 7507, "count": 0}], "isBlockCoverage": false}, {"functionName": "onTextureDrop", "ranges": [{"startOffset": 7514, "endOffset": 7707, "count": 0}], "isBlockCoverage": false}, {"functionName": "uploadTexture", "ranges": [{"startOffset": 7714, "endOffset": 8275, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeTexture", "ranges": [{"startOffset": 8282, "endOffset": 8432, "count": 0}], "isBlockCoverage": false}, {"functionName": "changePreviewShape", "ranges": [{"startOffset": 8439, "endOffset": 8505, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveMaterial", "ranges": [{"startOffset": 8512, "endOffset": 9585, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetMaterial", "ranges": [{"startOffset": 9592, "endOffset": 9733, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 10014, "endOffset": 10120, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10569, "endOffset": 10658, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10784, "endOffset": 10867, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10916, "endOffset": 10960, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1172", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 103167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 103167, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 16186, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 16211, "endOffset": 16566, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 16692, "endOffset": 16714, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 16821, "endOffset": 16852, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1173", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/MaterialTextureEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1174", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 47919, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 47919, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2482, "endOffset": 2614, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectedLight", "ranges": [{"startOffset": 2635, "endOffset": 2741, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2748, "endOffset": 2890, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 2899, "endOffset": 2935, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 2955, "endOffset": 3847, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultLighting", "ranges": [{"startOffset": 3854, "endOffset": 4365, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLightIcon", "ranges": [{"startOffset": 4372, "endOffset": 4652, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLightTypeName", "ranges": [{"startOffset": 4659, "endOffset": 4944, "count": 0}], "isBlockCoverage": false}, {"functionName": "createLight", "ranges": [{"startOffset": 4951, "endOffset": 5791, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectLight", "ranges": [{"startOffset": 5798, "endOffset": 5864, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteLight", "ranges": [{"startOffset": 5871, "endOffset": 6359, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLighting", "ranges": [{"startOffset": 6366, "endOffset": 7412, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetLighting", "ranges": [{"startOffset": 7419, "endOffset": 7943, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 8224, "endOffset": 8330, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8772, "endOffset": 8861, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8980, "endOffset": 9063, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9112, "endOffset": 9156, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1175", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 136111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 136111, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 20715, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 20740, "endOffset": 21086, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 21088, "endOffset": 21666, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 21792, "endOffset": 21814, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 21921, "endOffset": 21952, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1176", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/LightingEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1177", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/AnimationEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 222042, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 222042, "count": 1}, {"startOffset": 971, "endOffset": 55611, "count": 0}, {"startOffset": 55612, "endOffset": 222041, "count": 0}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 5485, "endOffset": 7328, "count": 0}], "isBlockCoverage": false}, {"functionName": "visibleAnimations", "ranges": [{"startOffset": 7462, "endOffset": 7697, "count": 0}], "isBlockCoverage": false}, {"functionName": "shouldUseVirtualScrolling", "ranges": [{"startOffset": 7859, "endOffset": 7989, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectedAnimation", "ranges": [{"startOffset": 7996, "endOffset": 8122, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8129, "endOffset": 8279, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectedTrack", "ranges": [{"startOffset": 8286, "endOffset": 8483, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 8490, "endOffset": 8697, "count": 0}], "isBlockCoverage": false}, {"functionName": "animation1", "ranges": [{"startOffset": 8750, "endOffset": 8876, "count": 0}], "isBlockCoverage": false}, {"functionName": "animation2", "ranges": [{"startOffset": 8883, "endOffset": 9009, "count": 0}], "isBlockCoverage": false}, {"functionName": "canCreateBlend", "ranges": [{"startOffset": 9016, "endOffset": 9240, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeMarkers", "ranges": [{"startOffset": 9247, "endOffset": 9712, "count": 0}], "isBlockCoverage": false}, {"functionName": "scrubberPosition", "ranges": [{"startOffset": 9719, "endOffset": 9797, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalKeyframeCount", "ranges": [{"startOffset": 9804, "endOffset": 10034, "count": 0}], "isBlockCoverage": false}, {"functionName": "totalTrackCount", "ranges": [{"startOffset": 10041, "endOffset": 10210, "count": 0}], "isBlockCoverage": false}, {"functionName": "created", "ranges": [{"startOffset": 10219, "endOffset": 10614, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 10619, "endOffset": 11138, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 11143, "endOffset": 12021, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 12043, "endOffset": 15089, "count": 0}], "isBlockCoverage": false}, {"functionName": "createDefaultAnimation", "ranges": [{"startOffset": 15096, "endOffset": 16188, "count": 0}], "isBlockCoverage": false}, {"functionName": "timeToPosition", "ranges": [{"startOffset": 16219, "endOffset": 16289, "count": 0}], "isBlockCoverage": false}, {"functionName": "positionToTime", "ranges": [{"startOffset": 16296, "endOffset": 16430, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateTimelineWidth", "ranges": [{"startOffset": 16437, "endOffset": 16616, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatTime", "ranges": [{"startOffset": 16623, "endOffset": 17110, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDuration", "ranges": [{"startOffset": 17117, "endOffset": 17365, "count": 0}], "isBlockCoverage": false}, {"functionName": "playAnimation", "ranges": [{"startOffset": 17397, "endOffset": 18164, "count": 0}], "isBlockCoverage": false}, {"functionName": "pauseAnimation", "ranges": [{"startOffset": 18171, "endOffset": 18355, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopAnimation", "ranges": [{"startOffset": 18362, "endOffset": 18442, "count": 0}], "isBlockCoverage": false}, {"functionName": "startScrubbing", "ranges": [{"startOffset": 18474, "endOffset": 18798, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleScrubberDrag", "ranges": [{"startOffset": 18805, "endOffset": 19144, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleScroll", "ranges": [{"startOffset": 19300, "endOffset": 20259, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupResources", "ranges": [{"startOffset": 20332, "endOffset": 20798, "count": 0}], "isBlockCoverage": false}, {"functionName": "initVirtual<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 20883, "endOffset": 22324, "count": 0}], "isBlockCoverage": false}, {"functionName": "initPerformanceMonitor", "ranges": [{"startOffset": 22385, "endOffset": 22732, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureRenderPerformance", "ranges": [{"startOffset": 22789, "endOffset": 24891, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadUserInfo", "ranges": [{"startOffset": 24927, "endOffset": 25282, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveUserInfo", "ranges": [{"startOffset": 25289, "endOffset": 25556, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleCollaboration", "ranges": [{"startOffset": 25563, "endOffset": 26011, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleMouseMove", "ranges": [{"startOffset": 26018, "endOffset": 26667, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborativeUpdate", "ranges": [{"startOffset": 26674, "endOffset": 27112, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationConnected", "ranges": [{"startOffset": 27119, "endOffset": 27439, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationDisconnected", "ranges": [{"startOffset": 27446, "endOffset": 27717, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationError", "ranges": [{"startOffset": 27724, "endOffset": 28034, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCollaborationToggle", "ranges": [{"startOffset": 28041, "endOffset": 28125, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleRemoteSelection", "ranges": [{"startOffset": 28132, "endOffset": 28902, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimationsWithPagination", "ranges": [{"startOffset": 29125, "endOffset": 33398, "count": 0}], "isBlockCoverage": false}, {"functionName": "approximateDataSize", "ranges": [{"startOffset": 33571, "endOffset": 33882, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimationsWithPagination", "ranges": [{"startOffset": 34050, "endOffset": 35062, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureRenderPerformance", "ranges": [{"startOffset": 35119, "endOffset": 35557, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopScrubbing", "ranges": [{"startOffset": 35564, "endOffset": 35626, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectKeyframe", "ranges": [{"startOffset": 35670, "endOffset": 35840, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateKeyframe", "ranges": [{"startOffset": 35847, "endOffset": 36070, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateKeyframeTime", "ranges": [{"startOffset": 36077, "endOffset": 36416, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimationPreview", "ranges": [{"startOffset": 36423, "endOffset": 36891, "count": 0}], "isBlockCoverage": false}, {"functionName": "addTrack", "ranges": [{"startOffset": 36898, "endOffset": 38089, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveAnimations", "ranges": [{"startOffset": 38124, "endOffset": 39039, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAnimations", "ranges": [{"startOffset": 39046, "endOffset": 39761, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAnimation", "ranges": [{"startOffset": 39768, "endOffset": 40442, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectAnimation", "ranges": [{"startOffset": 40449, "endOffset": 40837, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteAnimation", "ranges": [{"startOffset": 40844, "endOffset": 42476, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimation", "ranges": [{"startOffset": 42483, "endOffset": 42743, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimationDuration", "ranges": [{"startOffset": 42750, "endOffset": 43025, "count": 0}], "isBlockCoverage": false}, {"functionName": "openBlendDialog", "ranges": [{"startOffset": 43066, "endOffset": 43580, "count": 0}], "isBlockCoverage": false}, {"functionName": "closeBlendDialog", "ranges": [{"startOffset": 43587, "endOffset": 43649, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateBlendPreview", "ranges": [{"startOffset": 43656, "endOffset": 43944, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendAnimations", "ranges": [{"startOffset": 43951, "endOffset": 44284, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendKeyframes", "ranges": [{"startOffset": 44291, "endOffset": 44489, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValueAtTime", "ranges": [{"startOffset": 44496, "endOffset": 44666, "count": 0}], "isBlockCoverage": false}, {"functionName": "interpolate<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 44673, "endOffset": 44882, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendValues", "ranges": [{"startOffset": 44889, "endOffset": 45062, "count": 0}], "isBlockCoverage": false}, {"functionName": "scaleTracksByFactor", "ranges": [{"startOffset": 45069, "endOffset": 45242, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyEasingPreset", "ranges": [{"startOffset": 45471, "endOffset": 45737, "count": 0}], "isBlockCoverage": false}, {"functionName": "previewEasing", "ranges": [{"startOffset": 45970, "endOffset": 46321, "count": 0}], "isBlockCoverage": false}, {"functionName": "interpolateWithCurve", "ranges": [{"startOffset": 46632, "endOffset": 46945, "count": 0}], "isBlockCoverage": false}, {"functionName": "cubicBezier", "ranges": [{"startOffset": 47299, "endOffset": 47685, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCustomCurve", "ranges": [{"startOffset": 47860, "endOffset": 48156, "count": 0}], "isBlockCoverage": false}, {"functionName": "getEasingPath", "ranges": [{"startOffset": 48320, "endOffset": 49192, "count": 0}], "isBlockCoverage": false}, {"functionName": "startDraggingControlPoint", "ranges": [{"startOffset": 49322, "endOffset": 49918, "count": 0}], "isBlockCoverage": false}, {"functionName": "dragControlPoint", "ranges": [{"startOffset": 50011, "endOffset": 51092, "count": 0}], "isBlockCoverage": false}, {"functionName": "stopDraggingControlPoint", "ranges": [{"startOffset": 51152, "endOffset": 51405, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetCustomCurve", "ranges": [{"startOffset": 51476, "endOffset": 51663, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyCustomCurve", "ranges": [{"startOffset": 51732, "endOffset": 52062, "count": 0}], "isBlockCoverage": false}, {"functionName": "createBlendedAnimation", "ranges": [{"startOffset": 52069, "endOffset": 53330, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 53339, "endOffset": 53646, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendSettings.animation1Id", "ranges": [{"startOffset": 53664, "endOffset": 53744, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendSettings.animation2Id", "ranges": [{"startOffset": 53750, "endOffset": 53830, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendSettings.factor", "ranges": [{"startOffset": 53836, "endOffset": 53910, "count": 0}], "isBlockCoverage": false}, {"functionName": "handler", "ranges": [{"startOffset": 53936, "endOffset": 54944, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 55250, "endOffset": 55356, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 55804, "endOffset": 55894, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 56014, "endOffset": 56098, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 56147, "endOffset": 56191, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1178", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/services/AnimationService.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 30126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 30126, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAnimations", "ranges": [{"startOffset": 596, "endOffset": 857, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 960, "endOffset": 989, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAnimation", "ranges": [{"startOffset": 1230, "endOffset": 1490, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1592, "endOffset": 1620, "count": 0}], "isBlockCoverage": false}, {"functionName": "createAnimation", "ranges": [{"startOffset": 1869, "endOffset": 2110, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2246, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateAnimation", "ranges": [{"startOffset": 2549, "endOffset": 2834, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2939, "endOffset": 2970, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteAnimation", "ranges": [{"startOffset": 3217, "endOffset": 3442, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3547, "endOffset": 3578, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveAnimations", "ranges": [{"startOffset": 3897, "endOffset": 4898, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5002, "endOffset": 5032, "count": 0}], "isBlockCoverage": false}, {"functionName": "createBlendedAnimation", "ranges": [{"startOffset": 5516, "endOffset": 6194, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6306, "endOffset": 6344, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1179", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/AnimationUtils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 52328, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 52328, "count": 1}], "isBlockCoverage": true}, {"functionName": "interpolate", "ranges": [{"startOffset": 692, "endOffset": 1070, "count": 0}], "isBlockCoverage": false}, {"functionName": "interpolateObjects", "ranges": [{"startOffset": 1363, "endOffset": 2658, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendAnimations", "ranges": [{"startOffset": 2892, "endOffset": 4756, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendKeyframes", "ranges": [{"startOffset": 5030, "endOffset": 6049, "count": 0}], "isBlockCoverage": false}, {"functionName": "getValueAtTime", "ranges": [{"startOffset": 6286, "endOffset": 7404, "count": 0}], "isBlockCoverage": false}, {"functionName": "blendValues", "ranges": [{"startOffset": 7624, "endOffset": 7872, "count": 0}], "isBlockCoverage": false}, {"functionName": "scaleTracksByFactor", "ranges": [{"startOffset": 8053, "endOffset": 8727, "count": 0}], "isBlockCoverage": false}, {"functionName": "scaleObject", "ranges": [{"startOffset": 8888, "endOffset": 9241, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1180", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/EasingFunctions.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 47293, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 47293, "count": 1}], "isBlockCoverage": true}, {"functionName": "linear", "ranges": [{"startOffset": 527, "endOffset": 535, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInQuad", "ranges": [{"startOffset": 676, "endOffset": 688, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutQuad", "ranges": [{"startOffset": 831, "endOffset": 849, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutQuad", "ranges": [{"startOffset": 1001, "endOffset": 1052, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInCubic", "ranges": [{"startOffset": 1190, "endOffset": 1206, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutCubic", "ranges": [{"startOffset": 1346, "endOffset": 1368, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutCubic", "ranges": [{"startOffset": 1517, "endOffset": 1591, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInQuart", "ranges": [{"startOffset": 1731, "endOffset": 1751, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutQuart", "ranges": [{"startOffset": 1893, "endOffset": 1919, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutQuart", "ranges": [{"startOffset": 2070, "endOffset": 2132, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInQuint", "ranges": [{"startOffset": 2272, "endOffset": 2296, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutQuint", "ranges": [{"startOffset": 2438, "endOffset": 2468, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutQuint", "ranges": [{"startOffset": 2619, "endOffset": 2691, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInSine", "ranges": [{"startOffset": 2827, "endOffset": 2863, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutSine", "ranges": [{"startOffset": 3001, "endOffset": 3033, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutSine", "ranges": [{"startOffset": 3180, "endOffset": 3219, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInExpo", "ranges": [{"startOffset": 3362, "endOffset": 3410, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutExpo", "ranges": [{"startOffset": 3555, "endOffset": 3602, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutExpo", "ranges": [{"startOffset": 3756, "endOffset": 3957, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInCirc", "ranges": [{"startOffset": 4097, "endOffset": 4128, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutCirc", "ranges": [{"startOffset": 4270, "endOffset": 4299, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutCirc", "ranges": [{"startOffset": 4450, "endOffset": 4633, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInElastic", "ranges": [{"startOffset": 4775, "endOffset": 4900, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutElastic", "ranges": [{"startOffset": 5044, "endOffset": 5167, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutElastic", "ranges": [{"startOffset": 5320, "endOffset": 5618, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInBack", "ranges": [{"startOffset": 5754, "endOffset": 5827, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutBack", "ranges": [{"startOffset": 5965, "endOffset": 6044, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutBack", "ranges": [{"startOffset": 6191, "endOffset": 6428, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInBounce", "ranges": [{"startOffset": 6568, "endOffset": 6615, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeOutBounce", "ranges": [{"startOffset": 6757, "endOffset": 7070, "count": 0}], "isBlockCoverage": false}, {"functionName": "easeInOutBounce", "ranges": [{"startOffset": 7221, "endOffset": 7365, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1181", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/utils/PerformanceOptimizer.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 126348, "count": 1}], "isBlockCoverage": true}, {"functionName": "PerformanceOptimizer", "ranges": [{"startOffset": 873, "endOffset": 1429, "count": 0}], "isBlockCoverage": false}, {"functionName": "setupAutoCleanup", "ranges": [{"startOffset": 1506, "endOffset": 1673, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanupExpiredItems", "ranges": [{"startOffset": 1731, "endOffset": 2150, "count": 0}], "isBlockCoverage": false}, {"functionName": "approximateSize", "ranges": [{"startOffset": 2318, "endOffset": 2973, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3112, "endOffset": 3631, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 3937, "endOffset": 5217, "count": 0}], "isBlockCoverage": false}, {"functionName": "evictByMemory", "ranges": [{"startOffset": 5348, "endOffset": 6333, "count": 0}], "isBlockCoverage": false}, {"functionName": "evictLeastRecentlyUsed", "ranges": [{"startOffset": 6403, "endOffset": 6774, "count": 0}], "isBlockCoverage": false}, {"functionName": "findOldestKey", "ranges": [{"startOffset": 6874, "endOffset": 7153, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 7190, "endOffset": 7257, "count": 0}], "isBlockCoverage": false}, {"functionName": "dispose", "ranges": [{"startOffset": 7299, "endOffset": 7448, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 7532, "endOffset": 7952, "count": 0}], "isBlockCoverage": false}, {"functionName": "VirtualList<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 9004, "endOffset": 10212, "count": 0}], "isBlockCoverage": false}, {"functionName": "initWorker", "ranges": [{"startOffset": 10292, "endOffset": 11528, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleWorkerData", "ranges": [{"startOffset": 11693, "endOffset": 12266, "count": 0}], "isBlockCoverage": false}, {"functionName": "prefetchNextPage", "ranges": [{"startOffset": 12332, "endOffset": 13073, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateScroll", "ranges": [{"startOffset": 13216, "endOffset": 13811, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadMore", "ranges": [{"startOffset": 13918, "endOffset": 16335, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVisibleItems", "ranges": [{"startOffset": 16469, "endOffset": 18731, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateItems", "ranges": [{"startOffset": 19036, "endOffset": 19294, "count": 0}], "isBlockCoverage": false}, {"functionName": "getMetrics", "ranges": [{"startOffset": 19384, "endOffset": 20314, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetMetrics", "ranges": [{"startOffset": 20361, "endOffset": 20493, "count": 0}], "isBlockCoverage": false}, {"functionName": "dispose", "ranges": [{"startOffset": 20533, "endOffset": 20783, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 21001, "endOffset": 21257, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 21470, "endOffset": 21725, "count": 0}], "isBlockCoverage": false}, {"functionName": "measureExecutionTime", "ranges": [{"startOffset": 21935, "endOffset": 22144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22255, "endOffset": 22291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22402, "endOffset": 22437, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22537, "endOffset": 22561, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22661, "endOffset": 22685, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 22797, "endOffset": 22833, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1182", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/shared/utils/logger.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setLogLevel", "ranges": [{"startOffset": 596, "endOffset": 762, "count": 0}], "isBlockCoverage": false}, {"functionName": "getLogLevel", "ranges": [{"startOffset": 844, "endOffset": 896, "count": 0}], "isBlockCoverage": false}, {"functionName": "debug", "ranges": [{"startOffset": 1021, "endOffset": 1188, "count": 0}], "isBlockCoverage": false}, {"functionName": "info", "ranges": [{"startOffset": 1313, "endOffset": 1476, "count": 0}], "isBlockCoverage": false}, {"functionName": "warn", "ranges": [{"startOffset": 1603, "endOffset": 1766, "count": 0}], "isBlockCoverage": false}, {"functionName": "error", "ranges": [{"startOffset": 1892, "endOffset": 2059, "count": 0}], "isBlockCoverage": false}]}]}