
<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR Test Coverage Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9e9e9; border-radius: 3px; }
        .recommendation { padding: 10px; margin: 5px 0; border-left: 4px solid #ff9800; background: #fff3e0; }
        .high { border-left-color: #f44336; background: #ffebee; }
        .medium { border-left-color: #ff9800; background: #fff3e0; }
        .low { border-left-color: #4caf50; background: #e8f5e8; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>MVS-VR Test Coverage Analysis Report</h1>
        <p>Generated on: 2025-05-24T18:00:27.292Z</p>
    </div>
    
    <div class="section">
        <h2>Overall Coverage</h2>
        <p>No overall coverage data available</p>
    </div>
    
    <div class="section">
        <h2>Recommendations</h2>
        <div class="recommendation medium">VisualEditors.vue has low test coverage (0% lines)</div><div class="recommendation medium">ShowroomLayoutEditor.vue has low test coverage (0% lines)</div><div class="recommendation medium">ProductConfigurator.vue has low test coverage (0% lines)</div><div class="recommendation medium">MaterialTextureEditor.vue has low test coverage (0% lines)</div><div class="recommendation medium">LightingEditor.vue has low test coverage (0% lines)</div><div class="recommendation medium">AnimationEditor.vue has low test coverage (0% lines)</div>
    </div>
    
    <div class="section">
        <h2>Component Coverage</h2>
        <table>
            <tr><th>Component</th><th>Lines</th><th>Functions</th><th>Branches</th></tr>
            
        </table>
    </div>
</body>
</html>