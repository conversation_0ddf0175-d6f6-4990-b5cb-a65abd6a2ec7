{"name": "mvs-vr-backend", "version": "1.0.0", "description": "Backend for MVS-VR platform", "main": "index.js", "scripts": {"test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "start": "node src/index.js", "dev": "nodemon src/index.js"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "cookie-parser": "^1.4.6", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "pg": "^8.16.0"}, "devDependencies": {"vitest": "^1.2.0", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^20.11.0", "nodemon": "^3.0.1"}}