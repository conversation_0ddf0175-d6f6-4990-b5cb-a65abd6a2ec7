/**
 * WebSocket Unit Tests
 * Unit tests for WebSocket services without external dependencies
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { EventDispatcher } from '../../services/realtime/event-dispatcher.js';
import { MessageRouter } from '../../services/realtime/message-router.js';
import { SyncEngine } from '../../services/realtime/sync-engine.js';

describe('WebSocket Unit Tests', () => {
  describe('Event Dispatcher', () => {
    let eventDispatcher;

    beforeEach(async () => {
      eventDispatcher = new EventDispatcher({
        enablePersistence: false,
        enableBatching: false,
        redis: null // Disable Redis for unit tests
      });
      
      await new Promise(resolve => {
        if (eventDispatcher.listenerCount('ready') > 0) {
          eventDispatcher.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (eventDispatcher) {
        await eventDispatcher.shutdown();
      }
    });

    it('should publish and deliver events', async () => {
      let receivedEvent = null;
      
      const subscriptionId = eventDispatcher.subscribe('test-channel', (event) => {
        receivedEvent = event;
      });

      await eventDispatcher.publish('test-channel', { test: 'data' });

      // Wait for event delivery
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(receivedEvent).toBeDefined();
      expect(receivedEvent.data.test).toBe('data');
      expect(receivedEvent.channel).toBe('test-channel');

      eventDispatcher.unsubscribe('test-channel', subscriptionId);
    });

    it('should handle event filtering', async () => {
      let receivedEvents = [];
      
      const subscriptionId = eventDispatcher.subscribe('filter-test', (event) => {
        receivedEvents.push(event);
      }, {
        filter: (event) => event.data.important === true
      });

      // Publish events with and without filter criteria
      await eventDispatcher.publish('filter-test', { important: true, message: 'Important' });
      await eventDispatcher.publish('filter-test', { important: false, message: 'Not important' });
      await eventDispatcher.publish('filter-test', { important: true, message: 'Also important' });

      // Wait for event delivery
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(receivedEvents).toHaveLength(2);
      expect(receivedEvents[0].data.message).toBe('Important');
      expect(receivedEvents[1].data.message).toBe('Also important');

      eventDispatcher.unsubscribe('filter-test', subscriptionId);
    });

    it('should provide metrics', () => {
      const metrics = eventDispatcher.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(typeof metrics.channels).toBe('number');
      expect(typeof metrics.totalSubscribers).toBe('number');
      expect(typeof metrics.eventHistorySize).toBe('number');
    });
  });

  describe('Message Router', () => {
    let messageRouter;

    beforeEach(async () => {
      messageRouter = new MessageRouter({
        processingInterval: 10,
        enableMetrics: true
      });
      
      await new Promise(resolve => {
        if (messageRouter.listenerCount('ready') > 0) {
          messageRouter.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (messageRouter) {
        await messageRouter.shutdown();
      }
    });

    it('should add and remove routes', () => {
      const pattern = 'type:test';
      const config = {
        target: 'test-handler',
        priority: 5
      };

      messageRouter.addRoute(pattern, config);
      
      const stats = messageRouter.getRoutingStats();
      expect(stats.routes).toHaveLength(1);
      expect(stats.routes[0].pattern).toBe(pattern);
      expect(stats.routes[0].target).toBe(config.target);

      const removed = messageRouter.removeRoute(pattern);
      expect(removed).toBe(true);
      
      const updatedStats = messageRouter.getRoutingStats();
      expect(updatedStats.routes).toHaveLength(0);
    });

    it('should add filters and transformers', () => {
      messageRouter.addFilter('test-filter', (message) => {
        return message.data && message.data.allowed === true;
      });

      messageRouter.addTransformer('test-transformer', (message) => {
        return {
          ...message,
          transformed: true,
          timestamp: Date.now()
        };
      });

      const stats = messageRouter.getRoutingStats();
      expect(stats.filters).toContain('test-filter');
      expect(stats.transformers).toContain('test-transformer');
    });

    it('should route messages based on patterns', async () => {
      let routedMessages = [];
      
      messageRouter.on('messageDelivery', (delivery) => {
        routedMessages.push(delivery);
      });

      messageRouter.addRoute('type:test', {
        target: 'test-handler',
        priority: 5
      });

      const message = {
        type: 'test',
        data: { content: 'Test message' }
      };

      await messageRouter.routeMessage(message, { source: 'unit-test' });

      // Wait for routing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(routedMessages.length).toBeGreaterThan(0);
      const routed = routedMessages.find(msg => msg.message.type === 'test');
      expect(routed).toBeDefined();
      expect(routed.target).toBe('test-handler');
    });

    it('should provide metrics', () => {
      const metrics = messageRouter.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(typeof metrics.routes).toBe('number');
      expect(typeof metrics.filters).toBe('number');
      expect(typeof metrics.transformers).toBe('number');
      expect(typeof metrics.middlewares).toBe('number');
    });
  });

  describe('Sync Engine', () => {
    let syncEngine;

    beforeEach(async () => {
      syncEngine = new SyncEngine({
        enableVersioning: true,
        conflictResolutionStrategy: 'last-write-wins',
        redis: null // Disable Redis for unit tests
      });
      
      await new Promise(resolve => {
        if (syncEngine.listenerCount('ready') > 0) {
          syncEngine.once('ready', resolve);
        } else {
          resolve();
        }
      });
    });

    afterEach(async () => {
      if (syncEngine) {
        await syncEngine.shutdown();
      }
    });

    it('should synchronize entity updates', async () => {
      const entityId = 'test-entity-1';
      const testData = { name: 'Test Entity', value: 42 };

      // Update entity
      const result = await syncEngine.updateEntity(entityId, testData, {
        clientId: 'test-client'
      });

      expect(result.data).toEqual(expect.objectContaining(testData));
      expect(result.version).toBe(1);
      expect(result.operationId).toBeDefined();

      // Get entity
      const entity = syncEngine.getEntity(entityId);
      expect(entity).toBeDefined();
      expect(entity.data).toEqual(expect.objectContaining(testData));
      expect(entity.version).toBe(1);
    });

    it('should handle conflict resolution', async () => {
      const entityId = 'conflict-test-entity';
      
      // Create initial entity
      await syncEngine.updateEntity(entityId, { value: 1 }, {
        clientId: 'client-1'
      });

      // Simulate concurrent updates with version conflict
      const update1 = syncEngine.updateEntity(entityId, { value: 2 }, {
        clientId: 'client-1',
        version: 1
      });

      const update2 = syncEngine.updateEntity(entityId, { value: 3 }, {
        clientId: 'client-2',
        version: 1 // Same version - should cause conflict
      });

      const results = await Promise.allSettled([update1, update2]);
      
      // Both should succeed due to last-write-wins strategy
      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('fulfilled');

      const finalEntity = syncEngine.getEntity(entityId);
      expect(finalEntity.version).toBeGreaterThan(1);
    });

    it('should handle subscriptions and notifications', async () => {
      const entityId = 'subscription-test-entity';
      let notifications = [];

      // Subscribe to entity
      syncEngine.on('subscriberNotification', (notification) => {
        if (notification.subscription.clientId === 'test-subscriber') {
          notifications.push(notification);
        }
      });

      const subscription = syncEngine.subscribe(entityId, 'test-subscriber');
      expect(subscription).toBeDefined();

      // Update entity
      await syncEngine.updateEntity(entityId, { status: 'active' }, {
        clientId: 'updater'
      });

      // Wait for notification
      await new Promise(resolve => setTimeout(resolve, 50));

      expect(notifications.length).toBeGreaterThan(0);
      const updateNotification = notifications.find(n => 
        n.notification.type === 'entity-updated'
      );
      expect(updateNotification).toBeDefined();
    });

    it('should provide metrics', () => {
      const metrics = syncEngine.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(typeof metrics.entities).toBe('number');
      expect(typeof metrics.subscriptions).toBe('number');
      expect(typeof metrics.pendingConflicts).toBe('number');
      expect(typeof metrics.offlineQueues).toBe('number');
    });
  });

  describe('Service Integration', () => {
    let eventDispatcher, messageRouter, syncEngine;

    beforeEach(async () => {
      // Initialize services without external dependencies
      eventDispatcher = new EventDispatcher({
        enablePersistence: false,
        enableBatching: false,
        redis: null
      });
      
      messageRouter = new MessageRouter({
        processingInterval: 10
      });
      
      syncEngine = new SyncEngine({
        enableVersioning: true,
        redis: null
      });

      // Wait for all services to be ready
      await Promise.all([
        new Promise(resolve => {
          if (eventDispatcher.listenerCount('ready') > 0) {
            eventDispatcher.once('ready', resolve);
          } else {
            resolve();
          }
        }),
        new Promise(resolve => {
          if (messageRouter.listenerCount('ready') > 0) {
            messageRouter.once('ready', resolve);
          } else {
            resolve();
          }
        }),
        new Promise(resolve => {
          if (syncEngine.listenerCount('ready') > 0) {
            syncEngine.once('ready', resolve);
          } else {
            resolve();
          }
        })
      ]);
    });

    afterEach(async () => {
      await Promise.all([
        eventDispatcher?.shutdown(),
        messageRouter?.shutdown(),
        syncEngine?.shutdown()
      ]);
    });

    it('should integrate event dispatcher with message router', async () => {
      let routedMessages = [];
      
      // Setup message router
      messageRouter.addRoute('type:sync', {
        target: 'sync-handler',
        priority: 8
      });
      
      messageRouter.on('messageDelivery', (delivery) => {
        routedMessages.push(delivery);
      });

      // Setup event dispatcher to message router integration
      eventDispatcher.subscribe('test-integration', async (event) => {
        await messageRouter.routeMessage(event.data, {
          source: 'event-dispatcher'
        });
      });

      // Publish event
      await eventDispatcher.publish('test-integration', {
        type: 'sync',
        entityId: 'test-entity',
        data: { value: 123 }
      });

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(routedMessages.length).toBeGreaterThan(0);
      const syncMessage = routedMessages.find(msg => msg.target === 'sync-handler');
      expect(syncMessage).toBeDefined();
    });

    it('should handle performance under load', async () => {
      const startTime = Date.now();
      const messageCount = 50; // Reduced for unit tests
      let processedCount = 0;

      eventDispatcher.subscribe('load-test', () => {
        processedCount++;
      });

      // Send many messages quickly
      const promises = [];
      for (let i = 0; i < messageCount; i++) {
        promises.push(eventDispatcher.publish('load-test', {
          index: i,
          timestamp: Date.now()
        }));
      }

      await Promise.all(promises);

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 200));

      const endTime = Date.now();
      const duration = endTime - startTime;
      const messagesPerSecond = messageCount / (duration / 1000);

      console.log(`Processed ${messageCount} messages in ${duration}ms (${messagesPerSecond.toFixed(2)} msg/s)`);
      
      // Should handle at least 10 messages per second in unit tests
      expect(messagesPerSecond).toBeGreaterThan(10);
      expect(processedCount).toBe(messageCount);
    });
  });
});
