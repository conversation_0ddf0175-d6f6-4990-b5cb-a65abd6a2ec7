{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/VisualEditors.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37747, "count": 1}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 1844, "endOffset": 1854, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 1916, "endOffset": 1924, "count": 0}], "isBlockCoverage": false}, {"functionName": "data", "ranges": [{"startOffset": 1940, "endOffset": 2156, "count": 5}, {"startOffset": 2021, "endOffset": 2144, "count": 0}], "isBlockCoverage": true}, {"functionName": "setActiveEditor", "ranges": [{"startOffset": 2175, "endOffset": 2240, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleDataChange", "ranges": [{"startOffset": 2251, "endOffset": 2411, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4324, "endOffset": 7693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4657, "endOffset": 4821, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4877, "endOffset": 5038, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5099, "endOffset": 5593, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5669, "endOffset": 6188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6285, "endOffset": 6988, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7071, "endOffset": 7689, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/test-utils/component-test-utils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "createWrapper", "ranges": [{"startOffset": 902, "endOffset": 1797, "count": 5}, {"startOffset": 995, "endOffset": 1031, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1899, "endOffset": 1928, "count": 5}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2060, "endOffset": 2134, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2231, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "wait", "ranges": [{"startOffset": 2440, "endOffset": 2519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2612, "endOffset": 2632, "count": 0}], "isBlockCoverage": false}, {"functionName": "findAll", "ranges": [{"startOffset": 2982, "endOffset": 3573, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3669, "endOffset": 3692, "count": 0}], "isBlockCoverage": false}, {"functionName": "trigger", "ranges": [{"startOffset": 4111, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4800, "endOffset": 4823, "count": 0}], "isBlockCoverage": false}, {"functionName": "setValue", "ranges": [{"startOffset": 5049, "endOffset": 5320, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5417, "endOffset": 5441, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasClass", "ranges": [{"startOffset": 5652, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6246, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}]}]}