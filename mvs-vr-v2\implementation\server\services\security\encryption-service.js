/**
 * Advanced Encryption Service
 * Provides AES-256-GCM encryption, field-level encryption, and key management
 */

import crypto from 'crypto';
import { EventEmitter } from 'events';

export class EncryptionService extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      algorithm: options.algorithm || 'aes-256-gcm',
      keyDerivation: options.keyDerivation || 'pbkdf2',
      keyIterations: options.keyIterations || 100000,
      keyLength: options.keyLength || 32,
      ivLength: options.ivLength || 16,
      tagLength: options.tagLength || 16,
      saltLength: options.saltLength || 32,
      enableKeyRotation: options.enableKeyRotation || true,
      keyRotationInterval: options.keyRotationInterval || 86400000, // 24 hours
      enableFieldEncryption: options.enableFieldEncryption || true,
      enableHSM: options.enableHSM || false,
      hsmConfig: options.hsmConfig || {},
      ...options
    };

    // Key management
    this.masterKey = null;
    this.dataKeys = new Map(); // keyId -> key data
    this.fieldKeys = new Map(); // field -> key data
    this.keyRotationTimer = null;
    
    // Encryption cache
    this.encryptionCache = new Map();
    this.decryptionCache = new Map();
    
    // Metrics
    this.metrics = {
      encryptionOperations: 0,
      decryptionOperations: 0,
      keyRotations: 0,
      fieldEncryptions: 0,
      cacheHits: 0,
      cacheMisses: 0,
      errors: 0,
      lastReset: Date.now()
    };

    this.initialize();
  }

  async initialize() {
    try {
      // Initialize master key
      await this.initializeMasterKey();
      
      // Setup key rotation if enabled
      if (this.options.enableKeyRotation) {
        this.startKeyRotation();
      }
      
      console.log('🔐 Encryption Service initialized');
      this.emit('ready');
    } catch (error) {
      console.error('❌ Failed to initialize Encryption Service:', error);
      this.emit('error', error);
    }
  }

  async initializeMasterKey() {
    if (this.options.enableHSM) {
      // Initialize HSM connection
      await this.initializeHSM();
    } else {
      // Generate or load master key
      this.masterKey = await this.generateMasterKey();
    }
  }

  async initializeHSM() {
    // HSM initialization would go here
    // This is a placeholder for HSM integration
    console.log('🔒 HSM integration not implemented in this demo');
  }

  async generateMasterKey() {
    // In production, this should be loaded from secure storage
    const salt = crypto.randomBytes(this.options.saltLength);
    const password = process.env.ENCRYPTION_PASSWORD || 'default-password-change-in-production';
    
    return new Promise((resolve, reject) => {
      crypto.pbkdf2(password, salt, this.options.keyIterations, this.options.keyLength, 'sha256', (err, key) => {
        if (err) reject(err);
        else resolve({ key, salt });
      });
    });
  }

  /**
   * Encrypt data using AES-256-GCM
   * @param {string|Buffer} data - Data to encrypt
   * @param {Object} options - Encryption options
   */
  async encrypt(data, options = {}) {
    try {
      const startTime = Date.now();
      
      // Convert data to buffer if string
      const dataBuffer = Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8');
      
      // Generate or use provided key
      const keyData = options.keyId ? 
        await this.getDataKey(options.keyId) : 
        await this.generateDataKey();
      
      // Generate IV
      const iv = crypto.randomBytes(this.options.ivLength);
      
      // Create cipher
      const cipher = crypto.createCipher(this.options.algorithm, keyData.key);
      cipher.setAAD(Buffer.from(options.additionalData || ''));
      
      // Encrypt data
      const encrypted = Buffer.concat([
        cipher.update(dataBuffer),
        cipher.final()
      ]);
      
      // Get authentication tag
      const tag = cipher.getAuthTag();
      
      // Create result
      const result = {
        encrypted: encrypted.toString('base64'),
        iv: iv.toString('base64'),
        tag: tag.toString('base64'),
        keyId: keyData.id,
        algorithm: this.options.algorithm,
        timestamp: Date.now()
      };
      
      // Add to cache if enabled
      if (options.enableCache) {
        const cacheKey = this.generateCacheKey(data, options);
        this.encryptionCache.set(cacheKey, result);
      }
      
      this.metrics.encryptionOperations++;
      
      const duration = Date.now() - startTime;
      this.emit('encrypted', { 
        keyId: keyData.id, 
        dataSize: dataBuffer.length, 
        duration 
      });
      
      return result;
    } catch (error) {
      this.metrics.errors++;
      console.error('Encryption error:', error);
      throw new Error(`Encryption failed: ${error.message}`);
    }
  }

  /**
   * Decrypt data using AES-256-GCM
   * @param {Object} encryptedData - Encrypted data object
   * @param {Object} options - Decryption options
   */
  async decrypt(encryptedData, options = {}) {
    try {
      const startTime = Date.now();
      
      // Check cache first
      if (options.enableCache) {
        const cacheKey = this.generateCacheKey(encryptedData, options);
        if (this.decryptionCache.has(cacheKey)) {
          this.metrics.cacheHits++;
          return this.decryptionCache.get(cacheKey);
        }
        this.metrics.cacheMisses++;
      }
      
      // Get key
      const keyData = await this.getDataKey(encryptedData.keyId);
      if (!keyData) {
        throw new Error(`Key not found: ${encryptedData.keyId}`);
      }
      
      // Convert from base64
      const encrypted = Buffer.from(encryptedData.encrypted, 'base64');
      const iv = Buffer.from(encryptedData.iv, 'base64');
      const tag = Buffer.from(encryptedData.tag, 'base64');
      
      // Create decipher
      const decipher = crypto.createDecipher(encryptedData.algorithm, keyData.key);
      decipher.setAuthTag(tag);
      decipher.setAAD(Buffer.from(options.additionalData || ''));
      
      // Decrypt data
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]);
      
      const result = options.returnBuffer ? decrypted : decrypted.toString('utf8');
      
      // Add to cache if enabled
      if (options.enableCache) {
        const cacheKey = this.generateCacheKey(encryptedData, options);
        this.decryptionCache.set(cacheKey, result);
      }
      
      this.metrics.decryptionOperations++;
      
      const duration = Date.now() - startTime;
      this.emit('decrypted', { 
        keyId: encryptedData.keyId, 
        dataSize: decrypted.length, 
        duration 
      });
      
      return result;
    } catch (error) {
      this.metrics.errors++;
      console.error('Decryption error:', error);
      throw new Error(`Decryption failed: ${error.message}`);
    }
  }

  /**
   * Encrypt specific fields in an object
   * @param {Object} data - Object with fields to encrypt
   * @param {Array} fields - Array of field names to encrypt
   * @param {Object} options - Encryption options
   */
  async encryptFields(data, fields, options = {}) {
    if (!this.options.enableFieldEncryption) {
      throw new Error('Field encryption is disabled');
    }

    const result = { ...data };
    const encryptedFields = {};

    for (const field of fields) {
      if (data[field] !== undefined && data[field] !== null) {
        const fieldKey = await this.getFieldKey(field);
        const encrypted = await this.encrypt(JSON.stringify(data[field]), {
          keyId: fieldKey.id,
          ...options
        });
        
        result[field] = encrypted;
        encryptedFields[field] = true;
        this.metrics.fieldEncryptions++;
      }
    }

    // Add metadata about encrypted fields
    result._encrypted = encryptedFields;
    result._encryptedAt = Date.now();

    return result;
  }

  /**
   * Decrypt specific fields in an object
   * @param {Object} data - Object with encrypted fields
   * @param {Array} fields - Array of field names to decrypt
   * @param {Object} options - Decryption options
   */
  async decryptFields(data, fields, options = {}) {
    if (!this.options.enableFieldEncryption) {
      throw new Error('Field encryption is disabled');
    }

    const result = { ...data };

    for (const field of fields) {
      if (data[field] && data._encrypted && data._encrypted[field]) {
        try {
          const decrypted = await this.decrypt(data[field], options);
          result[field] = JSON.parse(decrypted);
        } catch (error) {
          console.error(`Failed to decrypt field ${field}:`, error);
          // Keep encrypted value if decryption fails
        }
      }
    }

    // Remove encryption metadata
    delete result._encrypted;
    delete result._encryptedAt;

    return result;
  }

  /**
   * Generate a new data encryption key
   */
  async generateDataKey() {
    const keyId = this.generateKeyId();
    const key = crypto.randomBytes(this.options.keyLength);
    
    const keyData = {
      id: keyId,
      key,
      createdAt: Date.now(),
      algorithm: this.options.algorithm,
      version: 1
    };

    this.dataKeys.set(keyId, keyData);
    
    this.emit('keyGenerated', { keyId, algorithm: this.options.algorithm });
    
    return keyData;
  }

  /**
   * Get data encryption key by ID
   * @param {string} keyId - Key identifier
   */
  async getDataKey(keyId) {
    return this.dataKeys.get(keyId);
  }

  /**
   * Get or create field encryption key
   * @param {string} fieldName - Field name
   */
  async getFieldKey(fieldName) {
    if (this.fieldKeys.has(fieldName)) {
      return this.fieldKeys.get(fieldName);
    }

    const keyId = `field_${fieldName}_${this.generateKeyId()}`;
    const key = crypto.randomBytes(this.options.keyLength);
    
    const keyData = {
      id: keyId,
      key,
      field: fieldName,
      createdAt: Date.now(),
      algorithm: this.options.algorithm,
      version: 1
    };

    this.fieldKeys.set(fieldName, keyData);
    
    this.emit('fieldKeyGenerated', { keyId, fieldName });
    
    return keyData;
  }

  /**
   * Rotate encryption keys
   */
  async rotateKeys() {
    try {
      console.log('🔄 Starting key rotation...');
      
      // Rotate master key
      const newMasterKey = await this.generateMasterKey();
      const oldMasterKey = this.masterKey;
      this.masterKey = newMasterKey;
      
      // Rotate data keys
      const oldDataKeys = new Map(this.dataKeys);
      this.dataKeys.clear();
      
      // Rotate field keys
      const oldFieldKeys = new Map(this.fieldKeys);
      this.fieldKeys.clear();
      
      this.metrics.keyRotations++;
      
      this.emit('keysRotated', {
        rotatedAt: Date.now(),
        dataKeysRotated: oldDataKeys.size,
        fieldKeysRotated: oldFieldKeys.size
      });
      
      console.log('✅ Key rotation completed');
    } catch (error) {
      console.error('❌ Key rotation failed:', error);
      this.emit('keyRotationError', error);
    }
  }

  /**
   * Start automatic key rotation
   */
  startKeyRotation() {
    if (this.keyRotationTimer) {
      clearInterval(this.keyRotationTimer);
    }

    this.keyRotationTimer = setInterval(() => {
      this.rotateKeys();
    }, this.options.keyRotationInterval);

    console.log(`🔄 Key rotation scheduled every ${this.options.keyRotationInterval / 1000} seconds`);
  }

  /**
   * Stop automatic key rotation
   */
  stopKeyRotation() {
    if (this.keyRotationTimer) {
      clearInterval(this.keyRotationTimer);
      this.keyRotationTimer = null;
    }
  }

  /**
   * Generate hash for data integrity verification
   * @param {string|Buffer} data - Data to hash
   * @param {string} algorithm - Hash algorithm
   */
  generateHash(data, algorithm = 'sha256') {
    const hash = crypto.createHash(algorithm);
    hash.update(data);
    return hash.digest('hex');
  }

  /**
   * Verify data integrity using hash
   * @param {string|Buffer} data - Original data
   * @param {string} expectedHash - Expected hash
   * @param {string} algorithm - Hash algorithm
   */
  verifyHash(data, expectedHash, algorithm = 'sha256') {
    const actualHash = this.generateHash(data, algorithm);
    return actualHash === expectedHash;
  }

  generateKeyId() {
    return `key_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  }

  generateCacheKey(data, options) {
    const input = typeof data === 'string' ? data : JSON.stringify(data);
    return crypto.createHash('sha256').update(input + JSON.stringify(options)).digest('hex');
  }

  /**
   * Clear encryption/decryption caches
   */
  clearCaches() {
    this.encryptionCache.clear();
    this.decryptionCache.clear();
    console.log('🧹 Encryption caches cleared');
  }

  /**
   * Get encryption service metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      dataKeys: this.dataKeys.size,
      fieldKeys: this.fieldKeys.size,
      encryptionCacheSize: this.encryptionCache.size,
      decryptionCacheSize: this.decryptionCache.size,
      keyRotationEnabled: this.options.enableKeyRotation,
      fieldEncryptionEnabled: this.options.enableFieldEncryption
    };
  }

  /**
   * Export key for backup (encrypted with master key)
   * @param {string} keyId - Key to export
   */
  async exportKey(keyId) {
    const keyData = this.dataKeys.get(keyId);
    if (!keyData) {
      throw new Error(`Key not found: ${keyId}`);
    }

    // Encrypt key with master key for export
    const exported = await this.encrypt(JSON.stringify(keyData), {
      additionalData: 'key-export'
    });

    return {
      keyId,
      exportedAt: Date.now(),
      encrypted: exported
    };
  }

  /**
   * Import key from backup
   * @param {Object} exportedKey - Exported key data
   */
  async importKey(exportedKey) {
    try {
      const decrypted = await this.decrypt(exportedKey.encrypted, {
        additionalData: 'key-export'
      });
      
      const keyData = JSON.parse(decrypted);
      this.dataKeys.set(keyData.id, keyData);
      
      this.emit('keyImported', { keyId: keyData.id });
      
      return keyData.id;
    } catch (error) {
      throw new Error(`Key import failed: ${error.message}`);
    }
  }

  /**
   * Gracefully shutdown the encryption service
   */
  async shutdown() {
    console.log('🛑 Shutting down Encryption Service...');
    
    // Stop key rotation
    this.stopKeyRotation();
    
    // Clear sensitive data from memory
    this.dataKeys.clear();
    this.fieldKeys.clear();
    this.clearCaches();
    
    if (this.masterKey) {
      this.masterKey = null;
    }
    
    console.log('✅ Encryption Service shutdown complete');
  }
}

export default EncryptionService;
