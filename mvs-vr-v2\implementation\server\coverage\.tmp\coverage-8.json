{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1449", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/%00plugin-vue:export-helper", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1367, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_exports__.default", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}, {"startOffset": 308, "endOffset": 336, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "1880", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/visual-editors/ShowroomLayoutEditor.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 44900, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 44900, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1840, "endOffset": 15274, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1898, "endOffset": 2824, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1987, "endOffset": 2425, "count": 101}, {"startOffset": 2050, "endOffset": 2123, "count": 33}, {"startOffset": 2123, "endOffset": 2167, "count": 68}, {"startOffset": 2167, "endOffset": 2365, "count": 34}, {"startOffset": 2365, "endOffset": 2424, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2861, "endOffset": 2922, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2981, "endOffset": 4800, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3049, "endOffset": 3224, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3307, "endOffset": 3817, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3899, "endOffset": 4188, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4257, "endOffset": 4535, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4602, "endOffset": 4794, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4856, "endOffset": 5541, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4925, "endOffset": 5140, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5212, "endOffset": 5535, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5593, "endOffset": 6829, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5685, "endOffset": 5909, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5989, "endOffset": 6196, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6288, "endOffset": 6823, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6889, "endOffset": 7815, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6974, "endOffset": 7216, "count": 1}, {"startOffset": 7120, "endOffset": 7215, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7297, "endOffset": 7503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7596, "endOffset": 7809, "count": 1}, {"startOffset": 7733, "endOffset": 7808, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7883, "endOffset": 9067, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7962, "endOffset": 8290, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8360, "endOffset": 8701, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8582, "endOffset": 8615, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8785, "endOffset": 9061, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9134, "endOffset": 10450, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9212, "endOffset": 9459, "count": 1}, {"startOffset": 9376, "endOffset": 9458, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9523, "endOffset": 10068, "count": 1}, {"startOffset": 9842, "endOffset": 10067, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10145, "endOffset": 10444, "count": 1}, {"startOffset": 10364, "endOffset": 10443, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10505, "endOffset": 11790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10577, "endOffset": 10752, "count": 1}, {"startOffset": 10671, "endOffset": 10751, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10815, "endOffset": 11136, "count": 1}, {"startOffset": 10981, "endOffset": 11135, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11199, "endOffset": 11487, "count": 1}, {"startOffset": 11373, "endOffset": 11486, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11550, "endOffset": 11784, "count": 1}, {"startOffset": 11718, "endOffset": 11783, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11858, "endOffset": 13437, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11945, "endOffset": 12295, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12373, "endOffset": 12698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12771, "endOffset": 13101, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13173, "endOffset": 13431, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13490, "endOffset": 14393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13560, "endOffset": 13747, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13809, "endOffset": 14010, "count": 1}, {"startOffset": 13920, "endOffset": 14009, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14070, "endOffset": 14199, "count": 1}, {"startOffset": 14125, "endOffset": 14198, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14258, "endOffset": 14387, "count": 1}, {"startOffset": 14313, "endOffset": 14386, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14447, "endOffset": 15270, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14524, "endOffset": 14859, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14631, "endOffset": 14646, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 14931, "endOffset": 15264, "count": 1}, {"startOffset": 15159, "endOffset": 15263, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15038, "endOffset": 15053, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1881", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 70243, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 70243, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1043, "endOffset": 1726, "count": 34}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1747, "endOffset": 1872, "count": 51}, {"startOffset": 1795, "endOffset": 1865, "count": 17}], "isBlockCoverage": true}, {"functionName": "gridStyle", "ranges": [{"startOffset": 1883, "endOffset": 2002, "count": 34}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 2011, "endOffset": 2047, "count": 34}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 2067, "endOffset": 2978, "count": 34}, {"startOffset": 2287, "endOffset": 2292, "count": 0}, {"startOffset": 2506, "endOffset": 2511, "count": 0}, {"startOffset": 2595, "endOffset": 2887, "count": 33}, {"startOffset": 2896, "endOffset": 2972, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterProducts", "ranges": [{"startOffset": 2985, "endOffset": 3373, "count": 3}, {"startOffset": 3033, "endOffset": 3110, "count": 2}, {"startOffset": 3110, "endOffset": 3372, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3215, "endOffset": 3358, "count": 2}, {"startOffset": 3278, "endOffset": 3358, "count": 1}], "isBlockCoverage": true}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 3380, "endOffset": 3543, "count": 163}, {"startOffset": 3503, "endOffset": 3518, "count": 97}, {"startOffset": 3519, "endOffset": 3536, "count": 66}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3454, "endOffset": 3478, "count": 145}], "isBlockCoverage": true}, {"functionName": "to<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3550, "endOffset": 3608, "count": 1}], "isBlockCoverage": true}, {"functionName": "toggleSnap", "ranges": [{"startOffset": 3615, "endOffset": 3677, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomIn", "ranges": [{"startOffset": 3684, "endOffset": 3771, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomOut", "ranges": [{"startOffset": 3778, "endOffset": 3868, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetZoom", "ranges": [{"startOffset": 3875, "endOffset": 3920, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleViewMode", "ranges": [{"startOffset": 3927, "endOffset": 4011, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragStart", "ranges": [{"startOffset": 4018, "endOffset": 4172, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragOver", "ranges": [{"startOffset": 4179, "endOffset": 4234, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDrop", "ranges": [{"startOffset": 4241, "endOffset": 5110, "count": 0}], "isBlockCoverage": false}, {"functionName": "startDrag", "ranges": [{"startOffset": 5117, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}, {"functionName": "getItemStyle", "ranges": [{"startOffset": 6277, "endOffset": 6532, "count": 21}], "isBlockCoverage": true}, {"functionName": "removeItem", "ranges": [{"startOffset": 6539, "endOffset": 6713, "count": 0}], "isBlockCoverage": false}, {"functionName": "rotateItem", "ranges": [{"startOffset": 6720, "endOffset": 6794, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLayout", "ranges": [{"startOffset": 6801, "endOffset": 7520, "count": 4}, {"startOffset": 6884, "endOffset": 7213, "count": 2}, {"startOffset": 7437, "endOffset": 7514, "count": 0}], "isBlockCoverage": true}, {"functionName": "resetLayout", "ranges": [{"startOffset": 7527, "endOffset": 7660, "count": 1}], "isBlockCoverage": true}, {"functionName": "_sfc_render", "ranges": [{"startOffset": 9284, "endOffset": 24845, "count": 116}, {"startOffset": 9544, "endOffset": 9907, "count": 34}, {"startOffset": 10101, "endOffset": 10184, "count": 34}, {"startOffset": 10249, "endOffset": 10481, "count": 34}, {"startOffset": 10635, "endOffset": 10720, "count": 34}, {"startOffset": 10785, "endOffset": 11021, "count": 34}, {"startOffset": 11292, "endOffset": 11423, "count": 34}, {"startOffset": 11520, "endOffset": 11639, "count": 34}, {"startOffset": 11852, "endOffset": 11909, "count": 34}, {"startOffset": 12199, "endOffset": 12326, "count": 34}, {"startOffset": 12516, "endOffset": 12577, "count": 34}, {"startOffset": 13371, "endOffset": 13490, "count": 34}, {"startOffset": 13676, "endOffset": 13733, "count": 34}, {"startOffset": 13760, "endOffset": 14133, "count": 34}, {"startOffset": 14362, "endOffset": 14484, "count": 34}, {"startOffset": 14784, "endOffset": 14841, "count": 34}, {"startOffset": 14876, "endOffset": 14967, "count": 34}, {"startOffset": 15137, "endOffset": 15257, "count": 34}, {"startOffset": 17581, "endOffset": 17664, "count": 34}, {"startOffset": 17691, "endOffset": 17842, "count": 34}, {"startOffset": 18067, "endOffset": 18150, "count": 34}, {"startOffset": 18177, "endOffset": 18331, "count": 34}, {"startOffset": 18572, "endOffset": 18647, "count": 34}, {"startOffset": 18674, "endOffset": 18825, "count": 34}, {"startOffset": 18966, "endOffset": 19044, "count": 34}, {"startOffset": 19071, "endOffset": 19223, "count": 34}, {"startOffset": 19364, "endOffset": 19446, "count": 34}, {"startOffset": 19473, "endOffset": 19627, "count": 34}, {"startOffset": 19854, "endOffset": 19946, "count": 34}, {"startOffset": 20115, "endOffset": 20129, "count": 0}, {"startOffset": 20675, "endOffset": 20759, "count": 34}, {"startOffset": 20796, "endOffset": 20872, "count": 34}, {"startOffset": 20943, "endOffset": 21236, "count": 114}, {"startOffset": 21255, "endOffset": 21311, "count": 2}, {"startOffset": 23948, "endOffset": 24810, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 10117, "endOffset": 10183, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 10651, "endOffset": 10719, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 11868, "endOffset": 11908, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 12532, "endOffset": 12576, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12778, "endOffset": 13102, "count": 348}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 13692, "endOffset": 13732, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onUpdate:modelValue._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 14800, "endOffset": 14840, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.withDirectives.__vite_ssr_import_0__.createElementVNode.onInput._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 14892, "endOffset": 14966, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15533, "endOffset": 17099, "count": 162}, {"startOffset": 16287, "endOffset": 16581, "count": 0}], "isBlockCoverage": true}, {"functionName": "onDragstart", "ranges": [{"startOffset": 15791, "endOffset": 15840, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 17597, "endOffset": 17663, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 18083, "endOffset": 18149, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 18588, "endOffset": 18646, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 18983, "endOffset": 19043, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 19381, "endOffset": 19445, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.onClick._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 19871, "endOffset": 19945, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.onDragover._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 20692, "endOffset": 20758, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vite_ssr_import_0__.createElementBlock.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementVNode.__vite_ssr_import_0__.createElementBlock.onDrop._cache.<computed>._cache.<computed>", "ranges": [{"startOffset": 20813, "endOffset": 20871, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 21501, "endOffset": 23856, "count": 21}, {"startOffset": 22041, "endOffset": 22320, "count": 0}, {"startOffset": 22460, "endOffset": 22643, "count": 18}, {"startOffset": 23043, "endOffset": 23213, "count": 18}, {"startOffset": 23534, "endOffset": 23710, "count": 18}], "isBlockCoverage": true}, {"functionName": "onMousedown", "ranges": [{"startOffset": 21827, "endOffset": 21871, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 22953, "endOffset": 22990, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 23444, "endOffset": 23481, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1882", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 570, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 570, "count": 1}], "isBlockCoverage": true}]}]}