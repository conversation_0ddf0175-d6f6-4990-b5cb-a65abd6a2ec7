/**
 * CSRF Protection Middleware
 * Provides comprehensive Cross-Site Request Forgery protection
 */

const crypto = require('crypto');
const { promisify } = require('util');

class CSRFProtection {
  constructor(options = {}) {
    this.options = {
      tokenLength: options.tokenLength || 32,
      cookieName: options.cookieName || 'csrf-token',
      headerName: options.headerName || 'x-csrf-token',
      sessionKey: options.sessionKey || 'csrfSecret',
      ignoreMethods: options.ignoreMethods || ['GET', 'HEAD', 'OPTIONS'],
      secure: options.secure !== false,
      sameSite: options.sameSite || 'strict',
      maxAge: options.maxAge || 3600000, // 1 hour
      ...options,
    };

    this.generateToken = promisify(crypto.randomBytes);
  }

  /**
   * Generate CSRF token
   */
  async generateCSRFToken() {
    const buffer = await this.generateToken(this.options.tokenLength);
    return buffer.toString('hex');
  }

  /**
   * Create CSRF secret for session
   */
  async createSecret() {
    const buffer = await this.generateToken(this.options.tokenLength);
    return buffer.toString('hex');
  }

  /**
   * Generate token from secret
   */
  generateTokenFromSecret(secret) {
    const hash = crypto.createHash('sha256');
    hash.update(secret);
    hash.update(Date.now().toString());
    return hash.digest('hex');
  }

  /**
   * Verify CSRF token
   */
  verifyToken(token, secret) {
    if (!token || !secret) {
      return false;
    }

    try {
      // Generate expected token
      const expectedToken = this.generateTokenFromSecret(secret);

      // Ensure both tokens are the same length for timing-safe comparison
      if (token.length !== expectedToken.length) {
        return false;
      }

      // Simple timing-safe comparison
      return crypto.timingSafeEqual(Buffer.from(token, 'hex'), Buffer.from(expectedToken, 'hex'));
    } catch (error) {
      // Handle any errors (invalid hex, etc.)
      return false;
    }
  }

  /**
   * CSRF middleware
   */
  middleware() {
    return async (req, res, next) => {
      try {
        // Skip for ignored methods
        if (this.options.ignoreMethods.includes(req.method)) {
          return next();
        }

        // Initialize session if needed
        if (!req.session) {
          return res.status(500).json({
            error: 'Session middleware required for CSRF protection',
          });
        }

        // Generate secret if not exists
        if (!req.session[this.options.sessionKey]) {
          req.session[this.options.sessionKey] = await this.createSecret();
        }

        const secret = req.session[this.options.sessionKey];

        // For state-changing requests, verify token
        if (!this.options.ignoreMethods.includes(req.method)) {
          const token = req.headers[this.options.headerName] || req.body._csrf || req.query._csrf;

          if (!this.verifyToken(token, secret)) {
            return res.status(403).json({
              error: 'Invalid CSRF token',
              code: 'CSRF_TOKEN_INVALID',
            });
          }
        }

        // Generate new token for response
        const newToken = this.generateTokenFromSecret(secret);

        // Set token in cookie
        res.cookie(this.options.cookieName, newToken, {
          httpOnly: false, // Allow JavaScript access
          secure: this.options.secure,
          sameSite: this.options.sameSite,
          maxAge: this.options.maxAge,
        });

        // Add token to response locals for templates
        res.locals.csrfToken = newToken;

        next();
      } catch (error) {
        console.error('CSRF Protection Error:', error);
        res.status(500).json({
          error: 'CSRF protection error',
          code: 'CSRF_ERROR',
        });
      }
    };
  }

  /**
   * Get token endpoint
   */
  getTokenEndpoint() {
    return async (req, res) => {
      try {
        if (!req.session) {
          return res.status(500).json({
            error: 'Session required',
          });
        }

        // Generate secret if not exists
        if (!req.session[this.options.sessionKey]) {
          req.session[this.options.sessionKey] = await this.createSecret();
        }

        const secret = req.session[this.options.sessionKey];
        const token = this.generateTokenFromSecret(secret);

        res.json({
          token,
          headerName: this.options.headerName,
          cookieName: this.options.cookieName,
        });
      } catch (error) {
        console.error('CSRF Token Generation Error:', error);
        res.status(500).json({
          error: 'Failed to generate CSRF token',
        });
      }
    };
  }
}

module.exports = CSRFProtection;
