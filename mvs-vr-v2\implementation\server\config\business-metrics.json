{"metrics": [{"id": "total_users", "name": "Total Registered Users", "description": "Total number of registered users in the system", "type": "counter", "category": "customer", "unit": "count", "source": "database", "query": "SELECT COUNT(*) FROM users", "aggregation": "sum", "tags": ["users", "registration", "growth"], "thresholds": {"warning": 10000, "critical": 50000}, "businessImpact": "high"}, {"id": "active_sessions", "name": "Active User Sessions", "description": "Number of currently active user sessions", "type": "gauge", "category": "engagement", "unit": "count", "source": "database", "query": "SELECT COUNT(*) FROM user_sessions WHERE active = true", "aggregation": "average", "tags": ["sessions", "activity", "engagement"], "thresholds": {"warning": 1000, "critical": 5000}, "businessImpact": "high"}, {"id": "api_requests_per_minute", "name": "API Requests per Minute", "description": "Number of API requests processed per minute", "type": "rate", "category": "performance", "unit": "requests/min", "source": "api", "query": "api_gateway_requests_total", "aggregation": "sum", "tags": ["api", "requests", "throughput"], "thresholds": {"warning": 10000, "critical": 50000}, "businessImpact": "medium"}, {"id": "api_response_time", "name": "Average API Response Time", "description": "Average response time for API requests", "type": "histogram", "category": "performance", "unit": "milliseconds", "source": "api", "query": "api_gateway_response_time_avg", "aggregation": "average", "tags": ["api", "performance", "latency"], "thresholds": {"warning": 500, "critical": 1000}, "businessImpact": "high"}, {"id": "api_error_rate", "name": "API Error Rate", "description": "Percentage of API requests resulting in errors", "type": "gauge", "category": "quality", "unit": "percentage", "source": "api", "query": "api_gateway_error_rate", "aggregation": "average", "tags": ["api", "errors", "quality"], "thresholds": {"warning": 1.0, "critical": 5.0}, "businessImpact": "critical"}, {"id": "api_success_rate", "name": "API Success Rate", "description": "Percentage of API requests completed successfully", "type": "gauge", "category": "quality", "unit": "percentage", "source": "api", "query": "api_gateway_success_rate", "aggregation": "average", "tags": ["api", "success", "quality"], "thresholds": {"warning": 95.0, "critical": 90.0}, "businessImpact": "critical"}, {"id": "database_connections", "name": "Active Database Connections", "description": "Number of active database connections", "type": "gauge", "category": "performance", "unit": "count", "source": "database", "query": "SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'", "aggregation": "average", "tags": ["database", "connections", "performance"], "thresholds": {"warning": 80, "critical": 100}, "businessImpact": "medium"}, {"id": "query_response_time", "name": "Database Query Response Time", "description": "Average database query response time", "type": "histogram", "category": "performance", "unit": "milliseconds", "source": "database", "query": "database_query_duration_avg", "aggregation": "average", "tags": ["database", "performance", "queries"], "thresholds": {"warning": 100, "critical": 500}, "businessImpact": "high"}, {"id": "cpu_usage", "name": "System CPU Usage", "description": "Current CPU utilization percentage", "type": "gauge", "category": "performance", "unit": "percentage", "source": "system", "query": "system_cpu_usage", "aggregation": "average", "tags": ["system", "cpu", "performance"], "thresholds": {"warning": 70.0, "critical": 90.0}, "businessImpact": "medium"}, {"id": "memory_usage", "name": "System Memory Usage", "description": "Current memory utilization percentage", "type": "gauge", "category": "performance", "unit": "percentage", "source": "system", "query": "system_memory_usage", "aggregation": "average", "tags": ["system", "memory", "performance"], "thresholds": {"warning": 80.0, "critical": 95.0}, "businessImpact": "medium"}, {"id": "disk_usage", "name": "System Disk Usage", "description": "Current disk utilization percentage", "type": "gauge", "category": "performance", "unit": "percentage", "source": "system", "query": "system_disk_usage", "aggregation": "average", "tags": ["system", "disk", "performance"], "thresholds": {"warning": 80.0, "critical": 95.0}, "businessImpact": "high"}, {"id": "network_throughput", "name": "Network Throughput", "description": "Current network throughput in Mbps", "type": "gauge", "category": "performance", "unit": "mbps", "source": "system", "query": "system_network_throughput", "aggregation": "average", "tags": ["system", "network", "throughput"], "thresholds": {"warning": 800, "critical": 950}, "businessImpact": "medium"}, {"id": "external_service_uptime", "name": "External Service Uptime", "description": "Uptime percentage of external services", "type": "gauge", "category": "operational", "unit": "percentage", "source": "external", "query": "external_service_uptime", "aggregation": "average", "tags": ["external", "uptime", "availability"], "thresholds": {"warning": 95.0, "critical": 90.0}, "businessImpact": "medium"}, {"id": "third_party_api_calls", "name": "Third-party API Calls", "description": "Number of calls made to third-party APIs", "type": "counter", "category": "operational", "unit": "count", "source": "external", "query": "third_party_api_calls_total", "aggregation": "sum", "tags": ["external", "api", "integration"], "thresholds": {"warning": 100000, "critical": 500000}, "businessImpact": "low"}, {"id": "user_engagement_score", "name": "User Engagement Score", "description": "Calculated user engagement score based on activity", "type": "gauge", "category": "engagement", "unit": "score", "source": "calculated", "query": "user_engagement_calculation", "aggregation": "average", "tags": ["engagement", "users", "activity"], "thresholds": {"warning": 5.0, "critical": 2.0}, "businessImpact": "high"}, {"id": "system_health_score", "name": "Overall System Health Score", "description": "Calculated overall system health score", "type": "gauge", "category": "operational", "unit": "score", "source": "calculated", "query": "system_health_calculation", "aggregation": "average", "tags": ["health", "system", "overall"], "thresholds": {"warning": 70.0, "critical": 50.0}, "businessImpact": "critical"}, {"id": "business_performance_index", "name": "Business Performance Index", "description": "Composite business performance indicator", "type": "gauge", "category": "revenue", "unit": "index", "source": "calculated", "query": "business_performance_calculation", "aggregation": "average", "tags": ["business", "performance", "kpi"], "thresholds": {"warning": 70.0, "critical": 50.0}, "businessImpact": "critical"}, {"id": "vendor_portal_usage", "name": "Vendor Portal Usage", "description": "Number of active vendor portal sessions", "type": "gauge", "category": "customer", "unit": "count", "source": "database", "query": "SELECT COUNT(*) FROM vendor_sessions WHERE active = true", "aggregation": "average", "tags": ["vendor", "portal", "usage"], "thresholds": {"warning": 100, "critical": 500}, "businessImpact": "high"}, {"id": "admin_portal_usage", "name": "Admin Portal Usage", "description": "Number of active admin portal sessions", "type": "gauge", "category": "operational", "unit": "count", "source": "database", "query": "SELECT COUNT(*) FROM admin_sessions WHERE active = true", "aggregation": "average", "tags": ["admin", "portal", "usage"], "thresholds": {"warning": 20, "critical": 50}, "businessImpact": "medium"}, {"id": "backup_success_rate", "name": "Backup Success Rate", "description": "Percentage of successful backup operations", "type": "gauge", "category": "operational", "unit": "percentage", "source": "system", "query": "backup_success_rate", "aggregation": "average", "tags": ["backup", "success", "reliability"], "thresholds": {"warning": 95.0, "critical": 90.0}, "businessImpact": "high"}, {"id": "security_incidents", "name": "Security Incidents", "description": "Number of security incidents detected", "type": "counter", "category": "quality", "unit": "count", "source": "system", "query": "security_incidents_total", "aggregation": "sum", "tags": ["security", "incidents", "alerts"], "thresholds": {"warning": 1, "critical": 5}, "businessImpact": "critical"}, {"id": "data_processing_volume", "name": "Data Processing Volume", "description": "Volume of data processed per hour", "type": "gauge", "category": "operational", "unit": "gb", "source": "system", "query": "data_processing_volume", "aggregation": "sum", "tags": ["data", "processing", "volume"], "thresholds": {"warning": 1000, "critical": 5000}, "businessImpact": "medium"}, {"id": "storage_utilization", "name": "Storage Utilization", "description": "Percentage of storage capacity utilized", "type": "gauge", "category": "performance", "unit": "percentage", "source": "system", "query": "storage_utilization", "aggregation": "average", "tags": ["storage", "capacity", "utilization"], "thresholds": {"warning": 80.0, "critical": 95.0}, "businessImpact": "high"}]}