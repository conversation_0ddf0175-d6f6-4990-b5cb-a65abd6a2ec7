{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 0}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2431", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/unit/auth-middleware.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 32830, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 32830, "count": 1}, {"startOffset": 1102, "endOffset": 32829, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 339, "endOffset": 374, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 396, "endOffset": 637, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 451, "endOffset": 600, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 664, "endOffset": 693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1160, "endOffset": 10456, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2432", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/api/middleware/auth-middleware.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 112235, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 112235, "count": 1}, {"startOffset": 1181, "endOffset": 1186, "count": 0}, {"startOffset": 1246, "endOffset": 1251, "count": 0}, {"startOffset": 1298, "endOffset": 1324, "count": 0}, {"startOffset": 1377, "endOffset": 1397, "count": 0}, {"startOffset": 1438, "endOffset": 1465, "count": 0}, {"startOffset": 1504, "endOffset": 1520, "count": 0}, {"startOffset": 1805, "endOffset": 1813, "count": 0}, {"startOffset": 1814, "endOffset": 1823, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeKeystore", "ranges": [{"startOffset": 2332, "endOffset": 3032, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3085, "endOffset": 3154, "count": 0}], "isBlockCoverage": false}, {"functionName": "createRateLimiter", "ranges": [{"startOffset": 3307, "endOffset": 4739, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackFailedAttempt", "ranges": [{"startOffset": 4820, "endOffset": 5687, "count": 0}], "isBlockCoverage": false}, {"functionName": "isIpBlocked", "ranges": [{"startOffset": 5736, "endOffset": 5918, "count": 0}], "isBlockCoverage": false}, {"functionName": "encryptToken", "ranges": [{"startOffset": 6528, "endOffset": 6984, "count": 0}], "isBlockCoverage": false}, {"functionName": "decryptToken", "ranges": [{"startOffset": 7145, "endOffset": 7468, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTokenRevoked", "ranges": [{"startOffset": 7633, "endOffset": 7963, "count": 0}], "isBlockCoverage": false}, {"functionName": "revokeToken", "ranges": [{"startOffset": 8145, "endOffset": 8591, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateDirectusToken", "ranges": [{"startOffset": 8761, "endOffset": 9429, "count": 0}], "isBlockCoverage": false}, {"functionName": "validateSupabaseToken", "ranges": [{"startOffset": 9587, "endOffset": 10194, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshSupabaseToken", "ranges": [{"startOffset": 10362, "endOffset": 11236, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSecureCookie", "ranges": [{"startOffset": 11474, "endOffset": 11798, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTokenFromRequest", "ranges": [{"startOffset": 11959, "endOffset": 12708, "count": 0}], "isBlockCoverage": false}, {"functionName": "trackAuthEvent", "ranges": [{"startOffset": 12847, "endOffset": 13582, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateCsrfToken", "ranges": [{"startOffset": 13682, "endOffset": 13740, "count": 0}], "isBlockCoverage": false}, {"functionName": "authenticate", "ranges": [{"startOffset": 14086, "endOffset": 19304, "count": 0}], "isBlockCoverage": false}]}]}