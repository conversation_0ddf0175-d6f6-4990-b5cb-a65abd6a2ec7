{"result": [{"scriptId": "1121", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/vitest.setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7997, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 741, "endOffset": 1819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1853, "endOffset": 1906, "count": 1}], "isBlockCoverage": true}, {"functionName": "toBeInTheDocument", "ranges": [{"startOffset": 1949, "endOffset": 2148, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeVisible", "ranges": [{"startOffset": 2152, "endOffset": 2625, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2428", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/integration/directus-supabase-integration.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37397, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37397, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 638, "endOffset": 1229, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1283, "endOffset": 1477, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1594, "endOffset": 1792, "count": 16}], "isBlockCoverage": true}, {"functionName": "DirectusSupabaseIntegration", "ranges": [{"startOffset": 1978, "endOffset": 2099, "count": 9}], "isBlockCoverage": true}, {"functionName": "syncVendorData", "ranges": [{"startOffset": 2102, "endOffset": 2890, "count": 7}, {"startOffset": 2308, "endOffset": 2343, "count": 0}, {"startOffset": 2345, "endOffset": 2788, "count": 0}], "isBlockCoverage": true}, {"functionName": "syncAssetData", "ranges": [{"startOffset": 2893, "endOffset": 3741, "count": 2}, {"startOffset": 3093, "endOffset": 3127, "count": 0}, {"startOffset": 3129, "endOffset": 3640, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleAuthSync", "ranges": [{"startOffset": 3744, "endOffset": 4541, "count": 2}, {"startOffset": 3982, "endOffset": 4300, "count": 1}, {"startOffset": 4300, "endOffset": 4348, "count": 0}, {"startOffset": 4350, "endOffset": 4380, "count": 0}, {"startOffset": 4380, "endOffset": 4412, "count": 1}, {"startOffset": 4427, "endOffset": 4436, "count": 1}, {"startOffset": 4446, "endOffset": 4537, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4614, "endOffset": 12087, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4676, "endOffset": 4824, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4891, "endOffset": 6945, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4981, "endOffset": 5991, "count": 1}, {"startOffset": 5588, "endOffset": 5990, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6069, "endOffset": 6336, "count": 1}, {"startOffset": 6330, "endOffset": 6335, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6406, "endOffset": 6939, "count": 1}, {"startOffset": 6933, "endOffset": 6938, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7011, "endOffset": 8349, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7100, "endOffset": 8002, "count": 1}, {"startOffset": 7753, "endOffset": 8001, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8079, "endOffset": 8343, "count": 1}, {"startOffset": 8337, "endOffset": 8342, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8419, "endOffset": 9796, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8516, "endOffset": 9472, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9544, "endOffset": 9790, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9855, "endOffset": 10785, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9953, "endOffset": 10779, "count": 1}, {"startOffset": 10715, "endOffset": 10778, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleWebhook", "ranges": [{"startOffset": 10253, "endOffset": 10407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10855, "endOffset": 12083, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10931, "endOffset": 12077, "count": 1}, {"startOffset": 11958, "endOffset": 12076, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11034, "endOffset": 11395, "count": 0}], "isBlockCoverage": false}, {"functionName": "syncWithRetry", "ranges": [{"startOffset": 11551, "endOffset": 11903, "count": 1}, {"startOffset": 11634, "endOffset": 11895, "count": 3}, {"startOffset": 11781, "endOffset": 11793, "count": 1}, {"startOffset": 11793, "endOffset": 11885, "count": 2}, {"startOffset": 11895, "endOffset": 11902, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11824, "endOffset": 11871, "count": 2}], "isBlockCoverage": true}]}]}