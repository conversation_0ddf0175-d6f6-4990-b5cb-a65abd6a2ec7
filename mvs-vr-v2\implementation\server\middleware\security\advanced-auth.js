/**
 * Advanced Authentication Middleware
 * Provides comprehensive authentication with MFA, session management, and security features
 */

import jwt from 'jsonwebtoken';
import { createHash, randomBytes } from 'crypto';
import rateLimit from 'express-rate-limit';

export class AdvancedAuthMiddleware {
  constructor(options = {}) {
    this.options = {
      jwtSecret: options.jwtSecret || process.env.JWT_SECRET || 'default-secret-change-in-production',
      jwtExpiresIn: options.jwtExpiresIn || '1h',
      refreshTokenExpiresIn: options.refreshTokenExpiresIn || '7d',
      enableMFA: options.enableMFA || true,
      enableSessionTracking: options.enableSessionTracking || true,
      enableDeviceTracking: options.enableDeviceTracking || true,
      maxSessionsPerUser: options.maxSessionsPerUser || 5,
      sessionTimeout: options.sessionTimeout || 3600000, // 1 hour
      enableRateLimiting: options.enableRateLimiting || true,
      rateLimitWindow: options.rateLimitWindow || 900000, // 15 minutes
      rateLimitMax: options.rateLimitMax || 5,
      enableAuditLogging: options.enableAuditLogging || true,
      ...options
    };

    // Session and token storage
    this.activeSessions = new Map(); // sessionId -> session data
    this.refreshTokens = new Map(); // tokenId -> token data
    this.userSessions = new Map(); // userId -> Set of sessionIds
    this.deviceFingerprints = new Map(); // deviceId -> device data
    
    // Security tracking
    this.loginAttempts = new Map(); // userId -> attempt data
    this.suspiciousActivity = new Map(); // userId -> activity data
    
    // Services (would be injected in real implementation)
    this.mfaService = options.mfaService || null;
    this.encryptionService = options.encryptionService || null;
    this.auditLogger = options.auditLogger || null;

    this.setupRateLimiting();
  }

  setupRateLimiting() {
    if (this.options.enableRateLimiting) {
      this.loginRateLimit = rateLimit({
        windowMs: this.options.rateLimitWindow,
        max: this.options.rateLimitMax,
        message: 'Too many login attempts, please try again later',
        standardHeaders: true,
        legacyHeaders: false,
        keyGenerator: (req) => {
          return req.ip + ':' + (req.body?.email || req.body?.username || 'unknown');
        }
      });
    }
  }

  /**
   * Authentication middleware
   */
  authenticate() {
    return async (req, res, next) => {
      try {
        const token = this.extractToken(req);
        
        if (!token) {
          return this.sendUnauthorized(res, 'No authentication token provided');
        }

        // Verify JWT token
        const decoded = await this.verifyToken(token);
        
        // Check if session is valid
        if (this.options.enableSessionTracking) {
          const sessionValid = await this.validateSession(decoded.sessionId);
          if (!sessionValid) {
            return this.sendUnauthorized(res, 'Invalid or expired session');
          }
        }

        // Check MFA if enabled
        if (this.options.enableMFA && decoded.requiresMFA) {
          const mfaValid = await this.validateMFA(decoded.userId, req);
          if (!mfaValid) {
            return res.status(403).json({
              success: false,
              error: 'MFA verification required',
              mfaRequired: true
            });
          }
        }

        // Update session activity
        if (this.options.enableSessionTracking) {
          await this.updateSessionActivity(decoded.sessionId, req);
        }

        // Add user info to request
        req.user = {
          id: decoded.userId,
          email: decoded.email,
          roles: decoded.roles || [],
          sessionId: decoded.sessionId,
          deviceId: decoded.deviceId,
          permissions: decoded.permissions || []
        };

        // Log successful authentication
        if (this.options.enableAuditLogging) {
          this.logAuthEvent('auth_success', req.user.id, req);
        }

        next();
      } catch (error) {
        console.error('Authentication error:', error);
        
        if (this.options.enableAuditLogging) {
          this.logAuthEvent('auth_failure', null, req, error.message);
        }
        
        return this.sendUnauthorized(res, 'Authentication failed');
      }
    };
  }

  /**
   * Login endpoint handler
   */
  async login(req, res) {
    try {
      const { email, password, mfaToken, deviceInfo } = req.body;

      if (!email || !password) {
        return res.status(400).json({
          success: false,
          error: 'Email and password are required'
        });
      }

      // Apply rate limiting
      if (this.options.enableRateLimiting) {
        await new Promise((resolve, reject) => {
          this.loginRateLimit(req, res, (err) => {
            if (err) reject(err);
            else resolve();
          });
        });
      }

      // Validate credentials (placeholder - would integrate with your user service)
      const user = await this.validateCredentials(email, password);
      if (!user) {
        this.trackFailedLogin(email, req);
        return res.status(401).json({
          success: false,
          error: 'Invalid credentials'
        });
      }

      // Check if MFA is required
      let mfaRequired = false;
      if (this.options.enableMFA && this.mfaService) {
        const mfaStatus = this.mfaService.getUserMFAStatus(user.id);
        mfaRequired = mfaStatus.totpEnabled || mfaStatus.smsEnabled || mfaStatus.emailEnabled;
        
        if (mfaRequired && !mfaToken) {
          return res.status(200).json({
            success: false,
            mfaRequired: true,
            availableMethods: this.getAvailableMFAMethods(mfaStatus)
          });
        }

        if (mfaRequired && mfaToken) {
          const mfaValid = await this.verifyMFAToken(user.id, mfaToken);
          if (!mfaValid) {
            return res.status(401).json({
              success: false,
              error: 'Invalid MFA token'
            });
          }
        }
      }

      // Generate device fingerprint
      const deviceId = this.generateDeviceFingerprint(deviceInfo, req);
      
      // Create session
      const sessionId = await this.createSession(user, deviceId, req);
      
      // Generate tokens
      const accessToken = await this.generateAccessToken(user, sessionId, deviceId);
      const refreshToken = await this.generateRefreshToken(user, sessionId);

      // Track successful login
      this.trackSuccessfulLogin(user.id, req);

      // Log login event
      if (this.options.enableAuditLogging) {
        this.logAuthEvent('login_success', user.id, req, { mfaUsed: mfaRequired });
      }

      res.json({
        success: true,
        data: {
          accessToken,
          refreshToken,
          expiresIn: this.options.jwtExpiresIn,
          user: {
            id: user.id,
            email: user.email,
            roles: user.roles || [],
            permissions: user.permissions || []
          },
          sessionId,
          deviceId
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: 'Login failed'
      });
    }
  }

  /**
   * Refresh token endpoint handler
   */
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          error: 'Refresh token is required'
        });
      }

      // Validate refresh token
      const tokenData = this.refreshTokens.get(refreshToken);
      if (!tokenData || Date.now() > tokenData.expiresAt) {
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired refresh token'
        });
      }

      // Validate session
      const sessionValid = await this.validateSession(tokenData.sessionId);
      if (!sessionValid) {
        return res.status(401).json({
          success: false,
          error: 'Session expired'
        });
      }

      // Generate new access token
      const user = tokenData.user;
      const newAccessToken = await this.generateAccessToken(user, tokenData.sessionId, tokenData.deviceId);

      // Optionally rotate refresh token
      let newRefreshToken = refreshToken;
      if (Math.random() < 0.1) { // 10% chance to rotate
        this.refreshTokens.delete(refreshToken);
        newRefreshToken = await this.generateRefreshToken(user, tokenData.sessionId);
      }

      res.json({
        success: true,
        data: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
          expiresIn: this.options.jwtExpiresIn
        }
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(500).json({
        success: false,
        error: 'Token refresh failed'
      });
    }
  }

  /**
   * Logout endpoint handler
   */
  async logout(req, res) {
    try {
      const { sessionId } = req.user || {};
      const { refreshToken } = req.body;

      // Invalidate session
      if (sessionId) {
        await this.invalidateSession(sessionId);
      }

      // Invalidate refresh token
      if (refreshToken) {
        this.refreshTokens.delete(refreshToken);
      }

      // Log logout event
      if (this.options.enableAuditLogging && req.user) {
        this.logAuthEvent('logout', req.user.id, req);
      }

      res.json({
        success: true,
        message: 'Logged out successfully'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        error: 'Logout failed'
      });
    }
  }

  extractToken(req) {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }
    return null;
  }

  async verifyToken(token) {
    return new Promise((resolve, reject) => {
      jwt.verify(token, this.options.jwtSecret, (err, decoded) => {
        if (err) reject(err);
        else resolve(decoded);
      });
    });
  }

  async generateAccessToken(user, sessionId, deviceId) {
    const payload = {
      userId: user.id,
      email: user.email,
      roles: user.roles || [],
      permissions: user.permissions || [],
      sessionId,
      deviceId,
      requiresMFA: false, // Would be determined based on user settings
      iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, this.options.jwtSecret, {
      expiresIn: this.options.jwtExpiresIn
    });
  }

  async generateRefreshToken(user, sessionId) {
    const tokenId = randomBytes(32).toString('hex');
    const expiresAt = Date.now() + this.parseTimeToMs(this.options.refreshTokenExpiresIn);

    const tokenData = {
      id: tokenId,
      userId: user.id,
      sessionId,
      user,
      createdAt: Date.now(),
      expiresAt,
      deviceId: null // Would be set if needed
    };

    this.refreshTokens.set(tokenId, tokenData);
    return tokenId;
  }

  async createSession(user, deviceId, req) {
    const sessionId = randomBytes(32).toString('hex');
    const session = {
      id: sessionId,
      userId: user.id,
      deviceId,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      expiresAt: Date.now() + this.options.sessionTimeout,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      active: true
    };

    this.activeSessions.set(sessionId, session);

    // Track user sessions
    if (!this.userSessions.has(user.id)) {
      this.userSessions.set(user.id, new Set());
    }
    
    const userSessionSet = this.userSessions.get(user.id);
    userSessionSet.add(sessionId);

    // Enforce session limit
    if (userSessionSet.size > this.options.maxSessionsPerUser) {
      const oldestSession = Array.from(userSessionSet)[0];
      await this.invalidateSession(oldestSession);
    }

    return sessionId;
  }

  async validateSession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.active) {
      return false;
    }

    if (Date.now() > session.expiresAt) {
      await this.invalidateSession(sessionId);
      return false;
    }

    return true;
  }

  async updateSessionActivity(sessionId, req) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.lastActivity = Date.now();
      session.expiresAt = Date.now() + this.options.sessionTimeout;
    }
  }

  async invalidateSession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      session.active = false;
      this.activeSessions.delete(sessionId);
      
      // Remove from user sessions
      const userSessionSet = this.userSessions.get(session.userId);
      if (userSessionSet) {
        userSessionSet.delete(sessionId);
      }
    }
  }

  generateDeviceFingerprint(deviceInfo, req) {
    const fingerprint = createHash('sha256')
      .update(JSON.stringify({
        userAgent: req.get('User-Agent'),
        acceptLanguage: req.get('Accept-Language'),
        acceptEncoding: req.get('Accept-Encoding'),
        ...deviceInfo
      }))
      .digest('hex');

    // Store device info
    this.deviceFingerprints.set(fingerprint, {
      ...deviceInfo,
      firstSeen: Date.now(),
      lastSeen: Date.now(),
      userAgent: req.get('User-Agent')
    });

    return fingerprint;
  }

  async validateCredentials(email, password) {
    // Placeholder for credential validation
    // In real implementation, this would hash the password and check against database
    return {
      id: 'user123',
      email,
      roles: ['user'],
      permissions: ['read', 'write']
    };
  }

  async validateMFA(userId, req) {
    // Placeholder for MFA validation
    // Would check if user has completed MFA for this session
    return true;
  }

  async verifyMFAToken(userId, token) {
    if (!this.mfaService) return true;
    
    try {
      const result = await this.mfaService.verifyTOTP(userId, token);
      return result.verified;
    } catch (error) {
      return false;
    }
  }

  getAvailableMFAMethods(mfaStatus) {
    const methods = [];
    if (mfaStatus.totpEnabled) methods.push('totp');
    if (mfaStatus.smsEnabled) methods.push('sms');
    if (mfaStatus.emailEnabled) methods.push('email');
    return methods;
  }

  trackFailedLogin(identifier, req) {
    // Track failed login attempts for security monitoring
    console.log(`Failed login attempt for ${identifier} from ${req.ip}`);
  }

  trackSuccessfulLogin(userId, req) {
    // Track successful logins for security monitoring
    console.log(`Successful login for user ${userId} from ${req.ip}`);
  }

  logAuthEvent(event, userId, req, metadata = {}) {
    if (this.auditLogger) {
      this.auditLogger.log({
        event,
        userId,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: Date.now(),
        ...metadata
      });
    }
  }

  sendUnauthorized(res, message) {
    return res.status(401).json({
      success: false,
      error: message
    });
  }

  parseTimeToMs(timeString) {
    const units = {
      's': 1000,
      'm': 60 * 1000,
      'h': 60 * 60 * 1000,
      'd': 24 * 60 * 60 * 1000
    };
    
    const match = timeString.match(/^(\d+)([smhd])$/);
    if (!match) return 3600000; // Default 1 hour
    
    const [, value, unit] = match;
    return parseInt(value) * units[unit];
  }

  getMetrics() {
    return {
      activeSessions: this.activeSessions.size,
      refreshTokens: this.refreshTokens.size,
      deviceFingerprints: this.deviceFingerprints.size,
      totalUsers: this.userSessions.size
    };
  }

  async shutdown() {
    console.log('🛑 Shutting down Advanced Auth Middleware...');
    
    // Clear sensitive data
    this.activeSessions.clear();
    this.refreshTokens.clear();
    this.userSessions.clear();
    this.deviceFingerprints.clear();
    this.loginAttempts.clear();
    this.suspiciousActivity.clear();
    
    console.log('✅ Advanced Auth Middleware shutdown complete');
  }
}

export default AdvancedAuthMiddleware;
