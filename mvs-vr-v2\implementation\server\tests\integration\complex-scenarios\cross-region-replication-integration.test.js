/**
 * Cross-Region Backup Replication Tests
 * 
 * This file contains tests for the cross-region backup replication functionality.
 */

const { expect } = require('chai');
const sinon = require('sinon');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const {
  runReplication,
  verifyReplication,
  getReplicationStatus,
  getReplicationLag
} = require('../scripts/backup/cross-region-replication');
const { runAllRecoveryTests } = require('../scripts/backup/cross-region-recovery');

describe('Cross-Region Backup Replication', () => {
  let sandbox;
  
  beforeEach(() => {
    sandbox = sinon.createSandbox();
    
    // Mock the logger
    sandbox.stub(console, 'log');
    sandbox.stub(console, 'error');
    sandbox.stub(console, 'info');
  });
  
  afterEach(() => {
    sandbox.restore();
  });
  
  describe('runReplication', () => {
    it('should replicate backups to the secondary region', async () => {
      // Mock S3 client
      const mockS3Client = {
        send: sandbox.stub()
      };
      
      // Mock ListObjectsV2Command response
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Bucket'))).resolves({
        Contents: [
          {
            Key: 'backup1.tar.gz',
            Size: 1024,
            LastModified: new Date()
          },
          {
            Key: 'backup2.tar.gz',
            Size: 2048,
            LastModified: new Date()
          }
        ]
      });
      
      // Mock HeadObjectCommand response
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Key', 'backup1.tar.gz'))).resolves({
        ContentLength: 1024,
        LastModified: new Date()
      });
      
      // Mock CopyObjectCommand response
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('CopySource'))).resolves({
        CopyObjectResult: {
          ETag: '"abcdef1234567890"'
        }
      });
      
      // Mock createS3Client
      sandbox.stub(require('../scripts/backup/cross-region-replication'), 'createS3Client').returns(mockS3Client);
      
      // Mock fs functions
      sandbox.stub(fs, 'existsSync').returns(false);
      sandbox.stub(fs.promises, 'mkdir').resolves();
      sandbox.stub(fs.promises, 'writeFile').resolves();
      
      // Run replication
      const result = await runReplication();
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.buckets).to.be.an('object');
      
      // Verify that S3 client was called with the correct parameters
      expect(mockS3Client.send.calledWith(sinon.match.has('input', sinon.match.has('Bucket')))).to.be.true;
      expect(mockS3Client.send.calledWith(sinon.match.has('input', sinon.match.has('CopySource')))).to.be.true;
    });
    
    it('should handle errors during replication', async () => {
      // Mock S3 client to throw an error
      const mockS3Client = {
        send: sandbox.stub().rejects(new Error('S3 error'))
      };
      
      // Mock createS3Client
      sandbox.stub(require('../scripts/backup/cross-region-replication'), 'createS3Client').returns(mockS3Client);
      
      // Mock fs functions
      sandbox.stub(fs, 'existsSync').returns(false);
      sandbox.stub(fs.promises, 'mkdir').resolves();
      sandbox.stub(fs.promises, 'writeFile').resolves();
      
      // Run replication
      const result = await runReplication();
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.error).to.be.a('string');
    });
  });
  
  describe('verifyReplication', () => {
    it('should verify that backups are correctly replicated', async () => {
      // Mock S3 client
      const mockS3Client = {
        send: sandbox.stub()
      };
      
      // Mock ListObjectsV2Command response for primary region
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Bucket', 'primary-bucket'))).resolves({
        Contents: [
          {
            Key: 'backup1.tar.gz',
            Size: 1024,
            LastModified: new Date()
          },
          {
            Key: 'backup2.tar.gz',
            Size: 2048,
            LastModified: new Date()
          }
        ]
      });
      
      // Mock ListObjectsV2Command response for secondary region
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Bucket', 'secondary-bucket'))).resolves({
        Contents: [
          {
            Key: 'backup1.tar.gz',
            Size: 1024,
            LastModified: new Date()
          },
          {
            Key: 'backup2.tar.gz',
            Size: 2048,
            LastModified: new Date()
          }
        ]
      });
      
      // Mock HeadObjectCommand response
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Key'))).resolves({
        ContentLength: 1024,
        LastModified: new Date(),
        Metadata: {
          'md5-checksum': 'abcdef1234567890'
        }
      });
      
      // Mock createS3Client
      sandbox.stub(require('../scripts/backup/cross-region-replication'), 'createS3Client').returns(mockS3Client);
      
      // Mock config
      const originalConfig = require('../scripts/backup/cross-region-replication').config;
      const mockConfig = {
        ...originalConfig,
        buckets: {
          database: {
            primary: 'primary-bucket',
            secondary: 'secondary-bucket'
          }
        }
      };
      sandbox.stub(require('../scripts/backup/cross-region-replication'), 'config').value(mockConfig);
      
      // Run verification
      const result = await verifyReplication('database');
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.success).to.be.true;
      expect(result.totalObjects).to.equal(2);
      expect(result.replicatedObjects).to.equal(2);
      expect(result.missingObjects).to.equal(0);
      expect(result.outdatedObjects).to.equal(0);
    });
    
    it('should detect missing and outdated objects', async () => {
      // Mock S3 client
      const mockS3Client = {
        send: sandbox.stub()
      };
      
      // Mock ListObjectsV2Command response for primary region
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Bucket', 'primary-bucket'))).resolves({
        Contents: [
          {
            Key: 'backup1.tar.gz',
            Size: 1024,
            LastModified: new Date()
          },
          {
            Key: 'backup2.tar.gz',
            Size: 2048,
            LastModified: new Date()
          },
          {
            Key: 'backup3.tar.gz',
            Size: 3072,
            LastModified: new Date()
          }
        ]
      });
      
      // Mock ListObjectsV2Command response for secondary region
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Bucket', 'secondary-bucket'))).resolves({
        Contents: [
          {
            Key: 'backup1.tar.gz',
            Size: 1024,
            LastModified: new Date(Date.now() - 3600000) // 1 hour old
          },
          {
            Key: 'backup3.tar.gz',
            Size: 3072,
            LastModified: new Date()
          }
        ]
      });
      
      // Mock HeadObjectCommand response
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Key', 'backup1.tar.gz'))).resolves({
        ContentLength: 1024,
        LastModified: new Date(Date.now() - 3600000), // 1 hour old
        Metadata: {
          'md5-checksum': 'abcdef1234567890'
        }
      });
      
      mockS3Client.send.withArgs(sinon.match.has('input', sinon.match.has('Key', 'backup3.tar.gz'))).resolves({
        ContentLength: 3072,
        LastModified: new Date(),
        Metadata: {
          'md5-checksum': '0987654321fedcba'
        }
      });
      
      // Mock createS3Client
      sandbox.stub(require('../scripts/backup/cross-region-replication'), 'createS3Client').returns(mockS3Client);
      
      // Mock config
      const originalConfig = require('../scripts/backup/cross-region-replication').config;
      const mockConfig = {
        ...originalConfig,
        buckets: {
          database: {
            primary: 'primary-bucket',
            secondary: 'secondary-bucket'
          }
        }
      };
      sandbox.stub(require('../scripts/backup/cross-region-replication'), 'config').value(mockConfig);
      
      // Run verification
      const result = await verifyReplication('database');
      
      // Verify result
      expect(result).to.be.an('object');
      expect(result.success).to.be.false;
      expect(result.totalObjects).to.equal(3);
      expect(result.replicatedObjects).to.equal(2);
      expect(result.missingObjects).to.equal(1);
      expect(result.outdatedObjects).to.equal(1);
    });
  });
  
  describe('getReplicationStatus', () => {
    it('should return the current replication status', async () => {
      // Mock verifyReplication
      const verifyReplicationStub = sandbox.stub(require('../scripts/backup/cross-region-replication'), 'verifyReplication');
      verifyReplicationStub.withArgs('database').resolves({
        success: true,
        totalObjects: 10,
        replicatedObjects: 10,
        missingObjects: 0,
        outdatedObjects: 0,
        replicationLag: 0
      });
      verifyReplicationStub.withArgs('files').resolves({
        success: true,
        totalObjects: 20,
        replicatedObjects: 18,
        missingObjects: 1,
        outdatedObjects: 1,
        replicationLag: 300
      });
      verifyReplicationStub.withArgs('config').resolves({
        success: true,
        totalObjects: 5,
        replicatedObjects: 5,
        missingObjects: 0,
        outdatedObjects: 0,
        replicationLag: 0
      });
      
      // Run getReplicationStatus
      const status = await getReplicationStatus();
      
      // Verify result
      expect(status).to.be.an('object');
      expect(status.success).to.be.false; // One bucket has issues
      expect(status.buckets).to.be.an('object');
      expect(status.buckets.database.success).to.be.true;
      expect(status.buckets.files.success).to.be.false;
      expect(status.buckets.config.success).to.be.true;
      expect(status.totalObjects).to.equal(35);
      expect(status.replicatedObjects).to.equal(33);
      expect(status.missingObjects).to.equal(1);
      expect(status.outdatedObjects).to.equal(1);
      expect(status.replicationLag).to.equal(300);
    });
  });
  
  describe('getReplicationLag', () => {
    it('should calculate replication lag correctly', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 3600000);
      
      const lag = getReplicationLag(oneHourAgo, now);
      
      expect(lag).to.be.approximately(3600, 10); // Allow small difference due to test execution time
    });
    
    it('should return 0 for identical timestamps', () => {
      const now = new Date();
      
      const lag = getReplicationLag(now, now);
      
      expect(lag).to.equal(0);
    });
    
    it('should handle null timestamps', () => {
      const now = new Date();
      
      const lag = getReplicationLag(null, now);
      
      expect(lag).to.be.a('number');
      expect(lag).to.be.greaterThan(0);
    });
  });
  
  describe('runAllRecoveryTests', () => {
    it('should run recovery tests for all bucket types', async () => {
      // Mock getLatestBackup
      const getLatestBackupStub = sandbox.stub(require('../scripts/backup/cross-region-recovery'), 'getLatestBackup');
      getLatestBackupStub.resolves({
        bucket: 'secondary-bucket',
        key: 'backup.tar.gz',
        size: 1024,
        lastModified: new Date()
      });
      
      // Mock downloadBackup
      const downloadBackupStub = sandbox.stub(require('../scripts/backup/cross-region-recovery'), 'downloadBackup');
      downloadBackupStub.resolves();
      
      // Mock execSync
      const execSyncStub = sandbox.stub(require('child_process'), 'execSync');
      execSyncStub.returns('Recovery output');
      
      // Mock fs functions
      sandbox.stub(fs, 'existsSync').returns(false);
      sandbox.stub(fs.promises, 'mkdir').resolves();
      sandbox.stub(fs.promises, 'writeFile').resolves();
      sandbox.stub(fs.promises, 'readFile').resolves('{}');
      
      // Run recovery tests
      const results = await runAllRecoveryTests();
      
      // Verify results
      expect(results).to.be.an('object');
      expect(results.buckets).to.be.an('object');
      expect(Object.keys(results.buckets).length).to.be.greaterThan(0);
    });
  });
});
